'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowRightIcon, ChartBarIcon, ClockIcon, CodeBracketIcon, LightBulbIcon } from '@heroicons/react/24/outline';
import { CalendarDaysIcon } from '@heroicons/react/24/solid';
import { motion } from 'motion/react';
import FontSwitcher from '../../components/FontSwitcher';

// Custom components
import ModernNavbar from '../components/landing/ModernNavbar';
import LandingBackgroundBlobs from '../components/LandingBackgroundBlobs';
import DemoChart from '../components/landing/DemoChart';
import GrainOverlay from '../components/GrainOverlay';
import VignetteEffect from '../components/VignetteEffect';
import ParallaxSection from '../components/ParallaxSection';
import LottieHeroAnimation from '../components/landing/LottieHeroAnimation';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';


export default function LandingPage() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        alert('Please enter a valid email address.');
        setIsSubmitting(false);
        return;
      }

      // Get existing emails from localStorage
      const existingEmails = JSON.parse(localStorage.getItem('kairosWaitlist') || '[]');

      // Check if email already exists
      if (existingEmails.includes(email.toLowerCase())) {
        alert('This email is already on our waitlist!');
        setIsSubmitting(false);
        return;
      }

      // Send email notification using Web3Forms
      try {
        const formData = new FormData();
        formData.append('access_key', '5aca658d-f4f1-4b81-af9c-7ab7dbf435b3');
        formData.append('name', 'Kairos AI Waitlist');
        formData.append('email', email);
        formData.append('message', `New Kairos AI waitlist signup!

Email: ${email}
Timestamp: ${new Date().toLocaleString()}
Source: Kairos AI Landing Page
User Agent: ${navigator.userAgent}`);

        const response = await fetch('https://api.web3forms.com/submit', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (result.success) {
          console.log('✅ Email sent <NAME_EMAIL> via Web3Forms');
          console.log('📧 Response:', result);
        } else {
          console.log('❌ Web3Forms failed:', result.message);
        }
      } catch (emailError) {
        console.log('❌ Email service error:', emailError);
      }

      // Always log for manual collection as backup
      console.log('📝 NEW WAITLIST SIGNUP:');
      console.log('📧 Email:', email);
      console.log('🕒 Time:', new Date().toLocaleString());
      console.log('💾 Stored in localStorage for backup');

      // Add new email to the list (regardless of email sending success)
      const updatedEmails = [...existingEmails, email.toLowerCase()];
      localStorage.setItem('kairosWaitlist', JSON.stringify(updatedEmails));

      // Store submission timestamp
      localStorage.setItem('kairosWaitlistTimestamp', new Date().toISOString());

      // Show success state (keep until page reload)
      setIsSubmitted(true);
      setEmail('');

    } catch (err) {
      alert('There was an error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="min-h-screen w-full bg-white dark:bg-black text-gray-900 dark:text-gray-100" style={{backgroundColor: 'inherit'}}>
      <FontSwitcher>
        <LandingBackgroundBlobs />
        <GrainOverlay />
        <VignetteEffect />
        <ModernNavbar />

      {/* Hero Section - PREMIUM with YOUR COLORS */}
      <section className="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 py-20 overflow-hidden">
        {/* FANTASTIC dotted background */}
        <div className="absolute inset-0 dots-multi"></div>
        <div className="absolute inset-0 dots-kairos-red"></div>
 <div className="max-w-7xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div
              className="text-left space-y-6"
              initial="hidden"
              animate="visible"
              variants={containerVariants}
            >
              <motion.h1
                className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight font-roboto relative z-10"
                variants={itemVariants}
              >
                <span className="text-gray-900 dark:text-gray-100">Agents that build world models for </span>
                <span className="text-red-400 relative text-outline-kairos text-glow-minimal">
                  forecasting
                     </span>
              </motion.h1>

              <motion.p
                className="text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-2xl leading-relaxed"
                variants={itemVariants}
              >
                A powerful agentic forecasting system that goes beyond traditional time series models by intelligently gathering context and simulating scenarios, saving businesses hours of manual work and cost reduction.
              </motion.p>

              {/* Waitlist Form */}
              <motion.div
                className="w-full max-w-md lg:max-w-md pt-4 relative z-20"
                variants={itemVariants}
                id="waitlist"
              >
                {isSubmitted ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center p-4 sm:p-6 bg-foreground/20 glass-card backdrop-blur-xl border-2 border-green-400 dark:border-green-600 rounded-2xl w-full shadow-2xl shadow-green-400/20"
                  >
                    <div className="text-green-600  text-xl sm:text-2xl mb-2">✓</div>
                    <h3 className="text-base sm:text-lg font-semibold text-green-600 mb-1">
                      You're on the list!
                    </h3>
                    <p className="text-gray-700 dark:text-gray-300 text-xs sm:text-sm">
                      Thanks for joining our waitlist. We'll be in touch soon!
                    </p>
                  </motion.div>
                ) : (
                  <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3 w-full">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      required
                      disabled={isSubmitting}
                      className="flex-grow px-4 py-3 sm:py-3 text-sm sm:text-base rounded-full bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-400 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation"
                    />
                    <motion.button
                      type="submit"
                      disabled={isSubmitting}
                      className={`inline-flex items-center px-8 py-4 font-bold rounded-full border-2 border-black transition-colors duration-300 text-base relative z-10 ${
                        isSubmitting
                          ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                          : 'bg-red-400 hover:bg-red-200 text-white cursor-pointer'
                      }`}
                      whileHover={!isSubmitting ? { scale: 1.05, y: -3 } : {}}
                      whileTap={!isSubmitting ? { scale: 0.95 } : {}}
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                          Joining...
                        </>
                      ) : (
                        'Join Waitlist'
                      )}
                    </motion.button>
                  </form>
                )}
              </motion.div>
            </motion.div>

            {/* Right side - Animated SVG */}
            <motion.div
              className="flex justify-center lg:justify-end"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <LottieHeroAnimation />
            </motion.div>
          </div>
        </div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 1 }}
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ repeat: Infinity, duration: 1.5 }}
            onClick={() => {
              const el = document.getElementById('demo');
              if (el) {
                const yOffset = -40; // Less negative offset for more downward scroll
                const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;
                window.scrollTo({ top: y, behavior: 'smooth' });
              }
            }}
            style={{ cursor: 'pointer' }}
          >
            <ArrowRightIcon className="h-6 w-6 text-white rotate-90" />
          </motion.div>
        </motion.div>
      </section>

      {/* Vision Section - PREMIUM with YOUR COLORS */}
      <section id="about" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* FANTASTIC backgrounds with your colors */}
           <div className="absolute inset-0 dots-kairos-beige"></div>
        <div className="absolute inset-0 dots-kairos-purple"></div>

        <motion.div
          className="max-w-6xl mx-auto relative z-10"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.8 }}
        >
          {/* Hero Title */}
          <motion.div
            className="text-center mb-20"
            initial={{ y: 60, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h2 className="text-5xl md:text-7xl font-bold font-roboto mb-6 relative z-10">
              <span className="text-gray-900 dark:text-gray-100">Our </span>
              <span className="text-red-400 relative text-outline-kairos text-glow-minimal">
                Vision
                 </span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Revolutionizing how machines understand and predict the future through time-aware intelligence
            </p>
          </motion.div>

          {/* Context & Causality */}
          <motion.div
            className="glass-card p-8 lg:p-12 mb-8 group cursor-pointer"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            whileHover={{ y: -4, transition: { duration: 0.3 } }}
          >
            <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
              {/* Left side - Text content */}
              <div className="text-center lg:text-left space-y-6">
                <div className="mb-6 p-4 rounded-2xl bg-red-50 dark:bg-red-900/20 w-fit mx-auto lg:mx-0 group-hover:bg-red-100 dark:group-hover:bg-red-900/30 transition-all duration-300 border border-red-200 dark:border-red-800">
                  <div className="w-8 h-8 bg-red-400 rounded-lg"></div>
                </div>
                <h3 className="text-2xl lg:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                  <span className="relative text-outline-kairos text-glow-minimal">
                    Context & Causality
                  </span>
                </h3>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                  Building intelligent agents that understand context and causality to power real-world forecasting and decision-making with unprecedented accuracy.
                </p>
              </div>

              {/* Right side - Animation - Hidden on mobile */}
              <div className="hidden lg:flex justify-center lg:justify-end">
                <div className="w-full max-w-sm">
                  <DotLottieReact
                    src="/animations/data.lottie"
                    loop
                    autoplay
                    style={{
                      width: '100%',
                      height: '100%',
                      minHeight: '300px',
                      filter: 'hue-rotate(350deg) saturate(1.2)'
                    }}
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Time as a Core Dimension */}
          <motion.div
            className="glass-card p-8 lg:p-12 mb-16 group cursor-pointer"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            whileHover={{ y: -4, transition: { duration: 0.3 } }}
          >
            <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
              {/* Left side - Animation - Hidden on mobile */}
              <div className="hidden lg:flex justify-center lg:justify-start order-2 lg:order-1">
                <div className="w-full max-w-sm">
                  <DotLottieReact
                    src="/animations/ccicle.lottie"
                    loop
                    autoplay
                    style={{
                      width: '100%',
                      height: '100%',
                      minHeight: '300px',
                      filter: 'hue-rotate(350deg) saturate(1.2)'
                    }}
                  />
                </div>
              </div>

              {/* Right side - Text content */}
              <div className="text-center lg:text-left space-y-6 order-1 lg:order-2">
                <div className="mb-6 p-4 rounded-2xl bg-red-50 dark:bg-red-900/20 w-fit mx-auto lg:mx-0 group-hover:bg-red-100 dark:group-hover:bg-red-900/30 transition-all duration-300 border border-red-200 dark:border-red-800">
                  <div className="w-8 h-8 bg-red-400 rounded-lg"></div>
                </div>
                <h3 className="text-2xl lg:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                  <span className="relative text-outline-kairos text-glow-minimal">
                    Time as a Core Dimension
                  </span>
                </h3>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                  Creating foundational time series models that treat time as a core dimension, enabling machines to reason over contexts and simulate dynamic futures.
                </p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </section>

      {/* Features Section - PREMIUM with YOUR COLORS */}
      <section id="features" className="py-24 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* FANTASTIC backgrounds with your colors */}
        <div className="absolute inset-0 bg-gray-50/50 dark:bg-gray-800/20"></div>
        <div className="absolute inset-0 dots-multi"></div>
        <div className="absolute inset-0 dots-kairos-beige"></div>

        <motion.div
          className="max-w-6xl mx-auto relative"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="text-center mb-20"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold font-roboto mb-6 relative z-10">
              <span className="text-gray-900 dark:text-gray-100">Powerful </span>
              <span className="text-red-400 relative text-outline-kairos text-glow-minimal">
                Features
              </span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Discover the cutting-edge capabilities that make Kairos the future of forecasting
            </p>
          </motion.div>

          <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <motion.div
              className="glass-card p-8 flex flex-col h-full group cursor-pointer"
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
              whileHover={{ y: -8, transition: { duration: 0.3 } }}
            >
              <div className="mb-6 p-4 rounded-2xl bg-red-50 dark:bg-red-900/20 w-fit group-hover:bg-red-100 dark:group-hover:bg-red-900/30 transition-all duration-300 border border-red-200 dark:border-red-800">
                <ChartBarIcon className="h-8 w-8 text-kairos-red group-hover:animate-pulse" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 leading-relaxed">
                Multi-Modal Data Processing
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Gathers and processes both structured (time series) and unstructured (text, news, web) data to generate accurate, real-world forecasts.
              </p>
            </motion.div>

            {/* Feature 2 */}
            <motion.div
              className="glass-card p-8 flex flex-col h-full group cursor-pointer"
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              whileHover={{ y: -8, transition: { duration: 0.3 } }}
            >
              <div className="mb-6 p-4 rounded-2xl bg-purple-50 dark:bg-purple-900/20 w-fit group-hover:bg-purple-100 dark:group-hover:bg-purple-900/30 transition-all duration-300 border border-purple-200 dark:border-purple-800">
                <CodeBracketIcon className="h-8 w-8 text-kairos-purple group-hover:animate-pulse" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 leading-relaxed">
                Causal Discovery & Transparency
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Performs causal discovery and provides transparent reasoning through explainability graphs and traceable logic.
              </p>
            </motion.div>

            {/* Feature 3 */}
            <motion.div
              className="glass-card p-8 flex flex-col h-full group cursor-pointer"
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
              whileHover={{ y: -8, transition: { duration: 0.3 } }}
            >
              <div className="mb-6 p-4 rounded-2xl bg-amber-50 dark:bg-amber-900/20 w-fit group-hover:bg-amber-100 dark:group-hover:bg-amber-900/30 transition-all duration-300 border border-amber-200 dark:border-amber-800">
                <LightBulbIcon className="h-8 w-8 text-kairos-beige group-hover:animate-pulse" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 leading-relaxed">
                Foundation Models & Dashboard
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Combines time series foundation models with a user-facing dashboard for tailored insights, scenario simulations, and business-specific agent templates.
              </p>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Demo Section - PREMIUM with YOUR COLORS - Hidden on mobile */}
      <section id="demo" className="hidden lg:block py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* FANTASTIC backgrounds with your colors */}
        <div className="absolute inset-0 bg-gray-50/30 dark:bg-gray-800/10"></div>
        <div className="absolute inset-0 dots-multi"></div>
        <div className="absolute inset-0 dots-kairos-red"></div>

        <motion.div
          className="max-w-7xl mx-auto relative z-10"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.8 }}
        >
          <motion.div
            className="text-center mb-20"
            initial={{ y: 60, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h2 className="text-5xl md:text-7xl font-bold font-roboto mb-8 relative z-10">
              <span className="text-gray-900 dark:text-gray-100">See Kairos </span>
              <span className="text-red-400 relative text-outline-kairos text-glow-minimal">
                in Action
              </span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Experience the future of forecasting with our interactive demonstration.
              Watch as AI transforms complex data into actionable insights.
            </p>
          </motion.div>

          {/* Premium Demo Container */}
          <motion.div
            className="relative"
            initial={{ y: 80, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {/* Main demo card - PREMIUM */}
            <div className="glass-card p-12 relative overflow-hidden border-glow-kairos">
              {/* FANTASTIC overlays with your colors */}
              <div className="absolute inset-0  pointer-events-none"></div>
              <div className="absolute inset-0 dots-kairos-purple opacity-15 pointer-events-none"></div>

              {/* Demo chart container - PREMIUM */}
              <motion.div
                className="relative z-10 bg-white/90 dark:bg-gray-800/90 rounded-3xl p-8 border border-gray-200 dark:border-gray-700 shadow-2xl overflow-hidden"
                initial={{ scale: 0.95, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.6 }}
                whileHover={{
                  scale: 1.02,
                  transition: { duration: 0.4, ease: "easeOut" }
                }}
              >
                <div className="absolute inset-0 dots-kairos-red opacity-10"></div>
                <div className="relative z-10">
                  <DemoChart />
                </div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </section>

      {/* Talk to Founders Section - PREMIUM */}
      <section id="founders" className="pt-24 pb-48 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* FANTASTIC backgrounds with your colors */}
        <div className="absolute inset-0 bg-gray-50/40 dark:bg-gray-800/20"></div>
        <div className="absolute inset-0 dots-multi"></div>
        <div className="absolute inset-0 dots-kairos-beige"></div>
        <motion.div
          className="max-w-5xl mx-auto text-center relative z-10"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold mb-8 text-gray-900 dark:text-gray-100 font-roboto"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <span className="text-gray-900 dark:text-gray-100">Talk to Our </span>
            <span className="text-red-400 relative text-outline-kairos text-glow-minimal">
              Founders
            </span>
          </motion.h2>
          <motion.p
            className="text-xl text-gray-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            Schedule a personalized demo and discover how Kairos can transform your forecasting workflow.
            Learn directly from the team building the future of AI-powered predictions.
          </motion.p>

          <motion.div
            className="flex flex-col lg:flex-row gap-12 items-center justify-center"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            {/* CTA Button */}
            <motion.div className="text-center">
              <motion.a
                href="https://calendly.com/jajoo-kairosai/30min"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-10 py-5 bg-red-400 hover:bg-red-200 text-white font-bold rounded-full border-2 border-black transition-colors duration-300 text-lg"
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
              >
                <CalendarDaysIcon className="h-6 w-6 mr-3" />
                Schedule a Demo
              </motion.a>
              <p className="text-muted text-sm mt-3">30-minute personalized session</p>
            </motion.div>

            {/* Value Proposition */}
           
          </motion.div>


        </motion.div>
      </section>

      {/* Footer */}
      <footer className="py-16 px-4 sm:px-6 lg:px-8 border-t border-border/20">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <motion.div
              className="md:col-span-2"
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4 font-roboto">Kairos</h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed max-w-md">
                Trying to teach models what Einstein taught physics: that time isn't just a label, it's a dimension!
              </p>
              <div className="mt-6 text-sm text-gray-500 dark:text-gray-400">
                © 2024 Kairos AI. All rights reserved.
              </div>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h3 className="text-lg text-gray-900 dark:text-gray-100 mb-4">Links</h3>
              <ul className="space-y-2">
                <li><Link href="/" className="text-gray-600 dark:text-gray-300 hover:text-red-400 transition-colors">Home</Link></li>
                <li><Link href="/about" className="text-gray-600 dark:text-gray-300 hover:text-red-400 transition-colors">About</Link></li>
                <li><Link href="/contact" className="text-gray-600 dark:text-gray-300 hover:text-red-400 transition-colors">Contact</Link></li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <h3 className="text-lg text-gray-900 dark:text-gray-100 mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><Link href="/privacy" className="text-gray-600 dark:text-gray-300 hover:text-red-400 transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="text-gray-600 dark:text-gray-300 hover:text-red-400 transition-colors">Terms of Service</Link></li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Connect</h3>
              <div className="flex space-x-4">
                <motion.a
                  href="https://x.com/kairos_ai__"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-400 transition-colors duration-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                  whileHover={{ scale: 1.1 }}
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </motion.a>
                <motion.a
                  href="https://www.linkedin.com/company/kairos-ai/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-400 transition-colors duration-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                  whileHover={{ scale: 1.1 }}
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                  </svg>
                </motion.a>
                <motion.a
                  href="http://www.github.com/KairosAI-IN"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-400 transition-colors duration-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                  whileHover={{ scale: 1.1 }}
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                  </svg>
                </motion.a>
              </div>
            </motion.div>
          </div>

          <motion.div
            className="mt-12 pt-8 border-t border-white/10 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <p className="text-gray-400">
              &copy; {new Date().getFullYear()} Kairos AI. All rights reserved.
            </p>
          </motion.div>
        </motion.div>
      </footer>
      </FontSwitcher>
    </div>
  );
}
