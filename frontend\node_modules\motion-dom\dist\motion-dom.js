!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("motion-utils")):"function"==typeof define&&define.amd?define(["exports","motion-utils"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).MotionDom={},t.MotionUtils)}(this,(function(t,e){"use strict";const n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],s={value:null,addProjectionMetrics:null};function i(t,i){let a=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},l=()=>a=!0,u=n.reduce(((t,e)=>(t[e]=function(t,e){let n=new Set,i=new Set,a=!1,r=!1;const o=new WeakSet;let l={delta:0,timestamp:0,isProcessing:!1},u=0;function c(e){o.has(e)&&(h.schedule(e),t()),u++,e(l)}const h={schedule:(t,e=!1,s=!1)=>{const r=s&&a?n:i;return e&&o.add(t),r.has(t)||r.add(t),t},cancel:t=>{i.delete(t),o.delete(t)},process:t=>{l=t,a?r=!0:(a=!0,[n,i]=[i,n],n.forEach(c),e&&s.value&&s.value.frameloop[e].push(u),u=0,n.clear(),a=!1,r&&(r=!1,h.process(t)))}};return h}(l,i?e:void 0),t)),{}),{setup:c,read:h,resolveKeyframes:d,preUpdate:m,update:p,preRender:f,render:y,postRender:g}=u,v=()=>{const n=e.MotionGlobalConfig.useManualTiming?o.timestamp:performance.now();a=!1,e.MotionGlobalConfig.useManualTiming||(o.delta=r?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,c.process(o),h.process(o),d.process(o),m.process(o),p.process(o),f.process(o),y.process(o),g.process(o),o.isProcessing=!1,a&&i&&(r=!1,t(v))};return{schedule:n.reduce(((e,n)=>{const s=u[n];return e[n]=(e,n=!1,i=!1)=>(a||(a=!0,r=!0,o.isProcessing||t(v)),s.schedule(e,n,i)),e}),{}),cancel:t=>{for(let e=0;e<n.length;e++)u[n[e]].cancel(t)},state:o,steps:u}}const{schedule:a,cancel:r,state:o,steps:l}=i("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:e.noop,!0);let u;function c(){u=void 0}const h={now:()=>(void 0===u&&h.set(o.isProcessing||e.MotionGlobalConfig.useManualTiming?o.timestamp:performance.now()),u),set:t=>{u=t,queueMicrotask(c)}},d={layout:0,mainThread:0,waapi:0},m=t=>e=>"string"==typeof e&&e.startsWith(t),p=m("--"),f=m("var(--"),y=t=>!!f(t)&&g.test(t.split("/*")[0].trim()),g=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,v={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},T={...v,transform:t=>e.clamp(0,1,t)},b={...v,default:1},w=t=>Math.round(1e5*t)/1e5,M=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const x=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,A=(t,e)=>n=>Boolean("string"==typeof n&&x.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),S=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[i,a,r,o]=s.match(M);return{[t]:parseFloat(i),[e]:parseFloat(a),[n]:parseFloat(r),alpha:void 0!==o?parseFloat(o):1}},k={...v,transform:t=>Math.round((t=>e.clamp(0,255,t))(t))},E={test:A("rgb","red"),parse:S("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+k.transform(t)+", "+k.transform(e)+", "+k.transform(n)+", "+w(T.transform(s))+")"};const P={test:A("#"),parse:function(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}},transform:E.transform},V=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),R=V("deg"),D=V("%"),F=V("px"),C=V("vh"),K=V("vw"),O=(()=>({...D,parse:t=>D.parse(t)/100,transform:t=>D.transform(100*t)}))(),L={test:A("hsl","hue"),parse:S("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+D.transform(w(e))+", "+D.transform(w(n))+", "+w(T.transform(s))+")"},W={test:t=>E.test(t)||P.test(t)||L.test(t),parse:t=>E.test(t)?E.parse(t):L.test(t)?L.parse(t):P.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?E.transform(t):L.transform(t)},N=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const $="number",B="color",j=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function I(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let a=0;const r=e.replace(j,(t=>(W.test(t)?(s.color.push(a),i.push(B),n.push(W.parse(t))):t.startsWith("var(")?(s.var.push(a),i.push("var"),n.push(t)):(s.number.push(a),i.push($),n.push(parseFloat(t))),++a,"${}"))).split("${}");return{values:n,split:r,indexes:s,types:i}}function Y(t){return I(t).values}function X(t){const{split:e,types:n}=I(t),s=e.length;return t=>{let i="";for(let a=0;a<s;a++)if(i+=e[a],void 0!==t[a]){const e=n[a];i+=e===$?w(t[a]):e===B?W.transform(t[a]):t[a]}return i}}const U=t=>"number"==typeof t?0:t;const q={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(M)?.length||0)+(t.match(N)?.length||0)>0},parse:Y,createTransformer:X,getAnimatableNone:function(t){const e=Y(t);return X(t)(e.map(U))}};function G(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function _({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let i=0,a=0,r=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,o=2*n-s;i=G(o,s,t+1/3),a=G(o,s,t),r=G(o,s,t-1/3)}else i=a=r=n;return{red:Math.round(255*i),green:Math.round(255*a),blue:Math.round(255*r),alpha:s}}function z(t,e){return n=>n>0?e:t}const Z=(t,e,n)=>t+(e-t)*n,H=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},J=[P,E,L];function Q(t){const n=(s=t,J.find((t=>t.test(s))));var s;if(e.warning(Boolean(n),`'${t}' is not an animatable color. Use the equivalent color code instead.`),!Boolean(n))return!1;let i=n.parse(t);return n===L&&(i=_(i)),i}const tt=(t,e)=>{const n=Q(t),s=Q(e);if(!n||!s)return z(t,e);const i={...n};return t=>(i.red=H(n.red,s.red,t),i.green=H(n.green,s.green,t),i.blue=H(n.blue,s.blue,t),i.alpha=Z(n.alpha,s.alpha,t),E.transform(i))},et=new Set(["none","hidden"]);function nt(t,e){return et.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function st(t,e){return n=>Z(t,e,n)}function it(t){return"number"==typeof t?st:"string"==typeof t?y(t)?z:W.test(t)?tt:ot:Array.isArray(t)?at:"object"==typeof t?W.test(t)?tt:rt:z}function at(t,e){const n=[...t],s=n.length,i=t.map(((t,n)=>it(t)(t,e[n])));return t=>{for(let e=0;e<s;e++)n[e]=i[e](t);return n}}function rt(t,e){const n={...t,...e},s={};for(const i in n)void 0!==t[i]&&void 0!==e[i]&&(s[i]=it(t[i])(t[i],e[i]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const ot=(t,n)=>{const s=q.createTransformer(n),i=I(t),a=I(n);return i.indexes.var.length===a.indexes.var.length&&i.indexes.color.length===a.indexes.color.length&&i.indexes.number.length>=a.indexes.number.length?et.has(t)&&!a.values.length||et.has(n)&&!i.values.length?nt(t,n):e.pipe(at(function(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const a=e.types[i],r=t.indexes[a][s[a]],o=t.values[r]??0;n[i]=o,s[a]++}return n}(i,a),a.values),s):(e.warning(!0,`Complex values '${t}' and '${n}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),z(t,n))};function lt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Z(t,e,n);return it(t)(t,e)}const ut=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>a.update(e,t),stop:()=>r(e),now:()=>o.isProcessing?o.timestamp:h.now()}},ct=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let e=0;e<i;e++)s+=t(e/(i-1))+", ";return`linear(${s.substring(0,s.length-2)})`},ht=2e4;function dt(t){let e=0;let n=t.next(e);for(;!n.done&&e<ht;)e+=50,n=t.next(e);return e>=ht?1/0:e}function mt(t,n=100,s){const i=s({...t,keyframes:[0,n]}),a=Math.min(dt(i),ht);return{type:"keyframes",ease:t=>i.next(a*t).value/n,duration:e.millisecondsToSeconds(a)}}function pt(t,n,s){const i=Math.max(n-5,0);return e.velocityPerSecond(s-t(i),n-i)}const ft=100,yt=10,gt=1,vt=0,Tt=800,bt=.3,wt=.3,Mt={granular:.01,default:2},xt={granular:.005,default:.5},At=.01,St=10,kt=.05,Et=1,Pt=.001;function Vt({duration:t=Tt,bounce:n=bt,velocity:s=vt,mass:i=gt}){let a,r;e.warning(t<=e.secondsToMilliseconds(St),"Spring duration must be 10 seconds or less");let o=1-n;o=e.clamp(kt,Et,o),t=e.clamp(At,St,e.millisecondsToSeconds(t)),o<1?(a=e=>{const n=e*o,i=n*t,a=n-s,r=Dt(e,o),l=Math.exp(-i);return Pt-a/r*l},r=e=>{const n=e*o*t,i=n*s+s,r=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-n),u=Dt(Math.pow(e,2),o);return(-a(e)+Pt>0?-1:1)*((i-r)*l)/u}):(a=e=>Math.exp(-e*t)*((e-s)*t+1)-.001,r=e=>Math.exp(-e*t)*(t*t*(s-e)));const l=function(t,e,n){let s=n;for(let n=1;n<Rt;n++)s-=t(s)/e(s);return s}(a,r,5/t);if(t=e.secondsToMilliseconds(t),isNaN(l))return{stiffness:ft,damping:yt,duration:t};{const e=Math.pow(l,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}const Rt=12;function Dt(t,e){return t*Math.sqrt(1-e*e)}const Ft=["duration","bounce"],Ct=["stiffness","damping","mass"];function Kt(t,e){return e.some((e=>void 0!==t[e]))}function Ot(t=wt,n=bt){const s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:n}:t;let{restSpeed:i,restDelta:a}=s;const r=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:r},{stiffness:u,damping:c,mass:h,duration:d,velocity:m,isResolvedFromDuration:p}=function(t){let n={velocity:vt,stiffness:ft,damping:yt,mass:gt,isResolvedFromDuration:!1,...t};if(!Kt(t,Ct)&&Kt(t,Ft))if(t.visualDuration){const s=t.visualDuration,i=2*Math.PI/(1.2*s),a=i*i,r=2*e.clamp(.05,1,1-(t.bounce||0))*Math.sqrt(a);n={...n,mass:gt,stiffness:a,damping:r}}else{const e=Vt(t);n={...n,...e,mass:gt},n.isResolvedFromDuration=!0}return n}({...s,velocity:-e.millisecondsToSeconds(s.velocity||0)}),f=m||0,y=c/(2*Math.sqrt(u*h)),g=o-r,v=e.millisecondsToSeconds(Math.sqrt(u/h)),T=Math.abs(g)<5;let b;if(i||(i=T?Mt.granular:Mt.default),a||(a=T?xt.granular:xt.default),y<1){const t=Dt(v,y);b=e=>{const n=Math.exp(-y*v*e);return o-n*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===y)b=t=>o-Math.exp(-v*t)*(g+(f+v*g)*t);else{const t=v*Math.sqrt(y*y-1);b=e=>{const n=Math.exp(-y*v*e),s=Math.min(t*e,300);return o-n*((f+y*v*g)*Math.sinh(s)+t*g*Math.cosh(s))/t}}const w={calculatedDuration:p&&d||null,next:t=>{const n=b(t);if(p)l.done=t>=d;else{let s=0===t?f:0;y<1&&(s=0===t?e.secondsToMilliseconds(f):pt(b,t,n));const r=Math.abs(s)<=i,u=Math.abs(o-n)<=a;l.done=r&&u}return l.value=l.done?o:n,l},toString:()=>{const t=Math.min(dt(w),ht),e=ct((e=>w.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Lt({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:r,min:o,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},m=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l;let p=n*e;const f=h+p,y=void 0===r?f:r(f);y!==f&&(p=y-h);const g=t=>-p*Math.exp(-t/s),v=t=>y+g(t),T=t=>{const e=g(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?y:n};let b,w;const M=t=>{var e;(e=d.value,void 0!==o&&e<o||void 0!==l&&e>l)&&(b=t,w=Ot({keyframes:[d.value,m(d.value)],velocity:pt(v,t,d.value),damping:i,stiffness:a,restDelta:u,restSpeed:c}))};return M(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==b||(e=!0,T(t),M(t)),void 0!==b&&t>=b?w.next(t-b):(!e&&T(t),d)}}}function Wt(t,n,{clamp:s=!0,ease:i,mixer:a}={}){const r=t.length;if(e.invariant(r===n.length,"Both input and output ranges must be the same length"),1===r)return()=>n[0];if(2===r&&n[0]===n[1])return()=>n[1];const o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),n=[...n].reverse());const l=function(t,n,s){const i=[],a=s||e.MotionGlobalConfig.mix||lt,r=t.length-1;for(let s=0;s<r;s++){let r=a(t[s],t[s+1]);if(n){const t=Array.isArray(n)?n[s]||e.noop:n;r=e.pipe(t,r)}i.push(r)}return i}(n,i,a),u=l.length,c=s=>{if(o&&s<t[0])return n[0];let i=0;if(u>1)for(;i<t.length-2&&!(s<t[i+1]);i++);const a=e.progress(t[i],t[i+1],s);return l[i](a)};return s?n=>c(e.clamp(t[0],t[r-1],n)):c}function Nt(t,n){const s=t[t.length-1];for(let i=1;i<=n;i++){const a=e.progress(0,n,i);t.push(Z(s,1,a))}}function $t(t){const e=[0];return Nt(e,t.length-1),e}function Bt(t,e){return t.map((t=>t*e))}function jt(t,n){return t.map((()=>n||e.easeInOut)).splice(0,t.length-1)}function It({duration:t=300,keyframes:n,times:s,ease:i="easeInOut"}){const a=e.isEasingArray(i)?i.map(e.easingDefinitionToFunction):e.easingDefinitionToFunction(i),r={done:!1,value:n[0]},o=Wt(Bt(s&&s.length===n.length?s:$t(n),t),n,{ease:Array.isArray(a)?a:jt(n,a)});return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}Ot.applyToOptions=t=>{const n=mt(t,100,Ot);return t.ease=n.ease,t.duration=e.secondsToMilliseconds(n.duration),t.type="keyframes",t};const Yt=t=>null!==t;function Xt(t,{repeat:e,repeatType:n="loop"},s,i=1){const a=t.filter(Yt),r=i<0||e&&"loop"!==n&&e%2==1?0:a.length-1;return r&&void 0!==s?s:a[r]}const Ut={decay:Lt,inertia:Lt,tween:It,keyframes:It,spring:Ot};function qt(t){"string"==typeof t.type&&(t.type=Ut[t.type])}class Gt{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const _t=t=>t/100;class zt extends Gt{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){const{motionValue:t}=this.options;t&&t.updatedAt!==h.now()&&this.tick(h.now())}if(this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:e}=this.options;e&&e()},d.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;qt(t);const{type:n=It,repeat:s=0,repeatDelay:i=0,repeatType:a,velocity:r=0}=t;let{keyframes:o}=t;const l=n||It;l!==It&&"number"!=typeof o[0]&&(this.mixKeyframes=e.pipe(_t,lt(o[0],o[1])),o=[0,100]);const u=l({...t,keyframes:o});"mirror"===a&&(this.mirroredGenerator=l({...t,keyframes:[...o].reverse(),velocity:-r})),null===u.calculatedDuration&&(u.calculatedDuration=dt(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=u}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,n=!1){const{generator:s,totalDuration:i,mixKeyframes:a,mirroredGenerator:r,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return s.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:d,repeatDelay:m,type:p,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const g=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let T=this.currentTime,b=s;if(h){const t=Math.min(this.currentTime,i)/o;let n=Math.floor(t),s=t%1;!s&&t>=1&&(s=1),1===s&&n--,n=Math.min(n,h+1);Boolean(n%2)&&("reverse"===d?(s=1-s,m&&(s-=m/o)):"mirror"===d&&(b=r)),T=e.clamp(0,1,s)*o}const w=v?{done:!1,value:c[0]}:b.next(T);a&&(w.value=a(w.value));let{done:M}=w;v||null===l||(M=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&M);return x&&p!==Lt&&(w.value=Xt(c,this.options,y,this.speed)),f&&f(w.value),x&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return e.millisecondsToSeconds(this.calculatedDuration)}get time(){return e.millisecondsToSeconds(this.currentTime)}set time(t){t=e.secondsToMilliseconds(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(h.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=e.millisecondsToSeconds(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=ut,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(h.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,d.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Zt(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Ht=t=>180*t/Math.PI,Jt=t=>{const e=Ht(Math.atan2(t[1],t[0]));return te(e)},Qt={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Jt,rotateZ:Jt,skewX:t=>Ht(Math.atan(t[1])),skewY:t=>Ht(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},te=t=>((t%=360)<0&&(t+=360),t),ee=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ne=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),se={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ee,scaleY:ne,scale:t=>(ee(t)+ne(t))/2,rotateX:t=>te(Ht(Math.atan2(t[6],t[5]))),rotateY:t=>te(Ht(Math.atan2(-t[2],t[0]))),rotateZ:Jt,rotate:Jt,skewX:t=>Ht(Math.atan(t[4])),skewY:t=>Ht(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ie(t){return t.includes("scale")?1:0}function ae(t,e){if(!t||"none"===t)return ie(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=se,i=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Qt,i=e}if(!i)return ie(e);const a=s[e],r=i[1].split(",").map(re);return"function"==typeof a?a(r):r[a]}function re(t){return parseFloat(t.trim())}const oe=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],le=(()=>new Set(oe))(),ue=t=>t===v||t===F,ce=new Set(["x","y","z"]),he=oe.filter((t=>!ce.has(t)));const de={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ae(e,"x"),y:(t,{transform:e})=>ae(e,"y")};de.translateX=de.x,de.translateY=de.y;const me=new Set;let pe=!1,fe=!1,ye=!1;function ge(){if(fe){const t=Array.from(me).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return he.forEach((n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}fe=!1,pe=!1,me.forEach((t=>t.complete(ye))),me.clear()}function ve(){me.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(fe=!0)}))}function Te(){ye=!0,ve(),ge(),ye=!1}class be{constructor(t,e,n,s,i,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(me.add(this),pe||(pe=!0,a.read(ve),a.resolveKeyframes(ge))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const i=s?.get(),a=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){const s=n.readValue(e,a);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=a),s&&void 0===i&&s.set(t[0])}Zt(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),me.delete(this)}cancel(){"scheduled"===this.state&&(me.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const we=t=>t.startsWith("--");function Me(t,e,n){we(e)?t.style.setProperty(e,n):t.style[e]=n}const xe=e.memo((()=>void 0!==window.ScrollTimeline)),Ae={};function Se(t,n){const s=e.memo(t);return()=>Ae[n]??s()}const ke=Se((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),Ee=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Pe={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ee([0,.65,.55,1]),circOut:Ee([.55,0,1,.45]),backIn:Ee([.31,.01,.66,-.59]),backOut:Ee([.33,1.53,.69,.99])};function Ve(t,n){return t?"function"==typeof t?ke()?ct(t,n):"ease-out":e.isBezierDefinition(t)?Ee(t):Array.isArray(t)?t.map((t=>Ve(t,n)||Pe.easeOut)):Pe[t]:void 0}function Re(t,e,n,{delay:i=0,duration:a=300,repeat:r=0,repeatType:o="loop",ease:l="easeOut",times:u}={},c=void 0){const h={[e]:n};u&&(h.offset=u);const m=Ve(l,a);Array.isArray(m)&&(h.easing=m),s.value&&d.waapi++;const p={delay:i,duration:a,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};c&&(p.pseudoElement=c);const f=t.animate(h,p);return s.value&&f.finished.finally((()=>{d.waapi--})),f}function De(t){return"function"==typeof t&&"applyToOptions"in t}function Fe({type:t,...e}){return De(t)&&ke()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Ce extends Gt{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:s,keyframes:i,pseudoElement:a,allowFlatten:r=!1,finalKeyframe:o,onComplete:l}=t;this.isPseudoElement=Boolean(a),this.allowFlatten=r,this.options=t,e.invariant("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');const u=Fe(t);this.animation=Re(n,s,i,u,a),!1===u.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!a){const t=Xt(i,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):Me(n,s,t),this.animation.cancel()}l?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return e.millisecondsToSeconds(Number(t))}get time(){return e.millisecondsToSeconds(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=e.secondsToMilliseconds(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&xe()?(this.animation.timeline=t,e.noop):n(this)}}const Ke={anticipate:e.anticipate,backInOut:e.backInOut,circInOut:e.circInOut};function Oe(t){"string"==typeof t.ease&&t.ease in Ke&&(t.ease=Ke[t.ease])}class Le extends Ce{constructor(t){Oe(t),qt(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:s,onComplete:i,element:a,...r}=this.options;if(!n)return;if(void 0!==t)return void n.set(t);const o=new zt({...r,autoplay:!1}),l=e.secondsToMilliseconds(this.finishedTime??this.time);n.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}}const We=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!q.test(t)&&"0"!==t||t.startsWith("url(")));const Ne=new Set(["opacity","clipPath","filter","transform"]),$e=e.memo((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));function Be(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:a,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:o,transformTemplate:l}=e.owner.getProps();return $e()&&n&&Ne.has(n)&&("transform"!==n||!l)&&!o&&!s&&"mirror"!==i&&0!==a&&"inertia"!==r}class je{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>t.finished)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map((e=>e.attachTimeline(t)));return()=>{e.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Ie extends Ce{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const Ye=new WeakMap;const Xe=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Ue(t){const e=Xe.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function qe(t,n,s=1){e.invariant(s<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);const[i,a]=Ue(t);if(!i)return;const r=window.getComputedStyle(n).getPropertyValue(i);if(r){const t=r.trim();return e.isNumericalString(t)?parseFloat(t):t}return y(a)?qe(a,n,s+1):a}function Ge(t,e){return t?.[e]??t?.default??t}const _e=new Set(["width","height","top","left","right","bottom",...oe]),ze=t=>e=>e.test(t),Ze=[v,F,D,R,K,C,{test:t=>"auto"===t,parse:t=>t}],He=t=>Ze.find(ze(t));const Je=new Set(["brightness","contrast","saturate","opacity"]);function Qe(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(M)||[];if(!s)return t;const i=n.replace(s,"");let a=Je.has(e)?1:0;return s!==n&&(a*=100),e+"("+a+i+")"}const tn=/\b([a-z-]*)\(.*?\)/gu,en={...q,getAnimatableNone:t=>{const e=t.match(tn);return e?e.map(Qe).join(" "):t}},nn={...v,transform:Math.round},sn={rotate:R,rotateX:R,rotateY:R,rotateZ:R,scale:b,scaleX:b,scaleY:b,scaleZ:b,skew:R,skewX:R,skewY:R,distance:F,translateX:F,translateY:F,translateZ:F,x:F,y:F,z:F,perspective:F,transformPerspective:F,opacity:T,originX:O,originY:O,originZ:F},an={borderWidth:F,borderTopWidth:F,borderRightWidth:F,borderBottomWidth:F,borderLeftWidth:F,borderRadius:F,radius:F,borderTopLeftRadius:F,borderTopRightRadius:F,borderBottomRightRadius:F,borderBottomLeftRadius:F,width:F,maxWidth:F,height:F,maxHeight:F,top:F,right:F,bottom:F,left:F,padding:F,paddingTop:F,paddingRight:F,paddingBottom:F,paddingLeft:F,margin:F,marginTop:F,marginRight:F,marginBottom:F,marginLeft:F,backgroundPositionX:F,backgroundPositionY:F,...sn,zIndex:nn,fillOpacity:T,strokeOpacity:T,numOctaves:nn},rn={...an,color:W,backgroundColor:W,outlineColor:W,fill:W,stroke:W,borderColor:W,borderTopColor:W,borderRightColor:W,borderBottomColor:W,borderLeftColor:W,filter:en,WebkitFilter:en},on=t=>rn[t];function ln(t,e){let n=on(t);return n!==en&&(n=q),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const un=new Set(["auto","none","0"]);const cn=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);const hn=e.memo((()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0})),dn=new Set(["opacity","clipPath","filter","transform"]);function mn(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const pn={current:void 0};class fn{constructor(t,e={}){this.version="__VERSION__",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=h.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=h.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new e.SubscriptionManager);const s=this.events[t].add(n);return"change"===t?()=>{s(),a.read((()=>{this.events.change.getSize()||this.stop()}))}:s}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return pn.current&&pn.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=h.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e.velocityPerSecond(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function yn(t,e){return new fn(t,e)}const gn=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class vn{constructor(){this.latest={},this.values=new Map}set(t,e,n,s){const i=this.values.get(t);i&&i.onRemove();const o=()=>{this.latest[t]=gn(e.get(),an[t]),n&&a.render(n)};o();const l=e.on("change",o);s&&e.addDependent(s);const u=()=>{l(),n&&r(n),this.values.delete(t),s&&e.removeDependent(s)};return this.values.set(t,{value:e,onRemove:u}),u}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}const Tn={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const bn=new WeakMap;function wn(t,e,n,s){let i,a;return le.has(n)?(e.get("transform")||e.set("transform",new fn("none"),(()=>{t.style.transform=function(t){let e="",n=!0;for(let s=0;s<oe.length;s++){const i=oe[s],a=t.latest[i];if(void 0===a)continue;let r=!0;r="number"==typeof a?a===(i.startsWith("scale")?1:0):0===parseFloat(a),r||(n=!1,e+=`${Tn[i]||i}(${t.latest[i]}) `)}return n?"none":e.trim()}(e)})),a=e.get("transform")):i=we(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,s,i,a)}const{schedule:Mn,cancel:xn}=i(queueMicrotask,!1),An={x:!1,y:!1};function Sn(){return An.x||An.y}function kn(t,e){const n=mn(t),s=new AbortController;return[n,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function En(t){return!("touch"===t.pointerType||Sn())}const Pn=(t,e)=>!!e&&(t===e||Pn(t,e.parentElement)),Vn=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Rn=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Dn=new WeakSet;function Fn(t){return e=>{"Enter"===e.key&&t(e)}}function Cn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Kn(t){return Vn(t)&&!Sn()}function On(){const{value:t}=s;null!==t?(t.frameloop.rate.push(o.delta),t.animations.mainThread.push(d.mainThread),t.animations.waapi.push(d.waapi),t.animations.layout.push(d.layout)):r(On)}function Ln(t){return t.reduce(((t,e)=>t+e),0)/t.length}function Wn(t,e=Ln){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const Nn=t=>Math.round(1e3/t);function $n(){s.value=null,s.addProjectionMetrics=null}function Bn(){const{value:t}=s;if(!t)throw new Error("Stats are not being measured");$n(),r(On);const e={frameloop:{setup:Wn(t.frameloop.setup),rate:Wn(t.frameloop.rate),read:Wn(t.frameloop.read),resolveKeyframes:Wn(t.frameloop.resolveKeyframes),preUpdate:Wn(t.frameloop.preUpdate),update:Wn(t.frameloop.update),preRender:Wn(t.frameloop.preRender),render:Wn(t.frameloop.render),postRender:Wn(t.frameloop.postRender)},animations:{mainThread:Wn(t.animations.mainThread),waapi:Wn(t.animations.waapi),layout:Wn(t.animations.layout)},layoutProjection:{nodes:Wn(t.layoutProjection.nodes),calculatedTargetDeltas:Wn(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:Wn(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=Nn(n.min),n.max=Nn(n.max),n.avg=Nn(n.avg),[n.min,n.max]=[n.max,n.min],e}function jn(...t){const e=!Array.isArray(t[0]),n=e?0:-1,s=t[0+n],i=Wt(t[1+n],t[2+n],t[3+n]);return e?i(s):i}function In(t){const e=[];pn.current=e;const n=t();pn.current=void 0;const s=yn(n);return function(t,e,n){const s=()=>e.set(n()),i=()=>a.preRender(s,!1,!0),o=t.map((t=>t.on("change",i)));e.on("destroy",(()=>{o.forEach((t=>t())),r(s)}))}(e,s,t),s}const Yn=[...Ze,W,q];function Xn(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let Un={},qn=null;const Gn=(t,e)=>{Un[t]=e},_n=()=>{qn||(qn=document.createElement("style"),qn.id="motion-view");let t="";for(const e in Un){const n=Un[e];t+=`${e} {\n`;for(const[e,s]of Object.entries(n))t+=`  ${e}: ${s};\n`;t+="}\n"}qn.textContent=t,document.head.appendChild(qn),Un={}},zn=()=>{qn&&qn.parentElement&&qn.parentElement.removeChild(qn)};function Zn(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function Hn(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}const Jn=["layout","enter","exit","new","old"];function Qn(t){const{update:n,targets:s,options:i}=t;if(!document.startViewTransition)return new Promise((async t=>{await n(),t(new je([]))}));(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",s)||Gn(":root",{"view-transition-name":"none"}),Gn("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),_n();const a=document.startViewTransition((async()=>{await n()}));return a.finished.finally((()=>{zn()})),new Promise((t=>{a.ready.then((()=>{const n=document.getAnimations().filter(Hn),a=[];s.forEach(((t,n)=>{for(const s of Jn){if(!t[s])continue;const{keyframes:r,options:o}=t[s];for(let[t,l]of Object.entries(r)){if(!l)continue;const r={...Ge(i,t),...Ge(o,t)},u=Xn(s);if("opacity"===t&&!Array.isArray(l)){l=["new"===u?0:1,l]}"function"==typeof r.delay&&(r.delay=r.delay(0,1)),r.duration&&(r.duration=e.secondsToMilliseconds(r.duration)),r.delay&&(r.delay=e.secondsToMilliseconds(r.delay));const c=new Ce({...r,element:document.documentElement,name:t,pseudoElement:`::view-transition-${u}(${n})`,keyframes:l});a.push(c)}}}));for(const t of n){if("finished"===t.playState)continue;const{effect:n}=t;if(!(n&&n instanceof KeyframeEffect))continue;const{pseudoElement:r}=n;if(!r)continue;const o=Zn(r);if(!o)continue;const l=s.get(o.layer);if(l)ts(l,"enter")&&ts(l,"exit")&&n.getKeyframes().some((t=>t.mixBlendMode))?a.push(new Ie(t)):t.cancel();else{const s="group"===o.type?"layout":"";let r={...Ge(i,s)};r.duration&&(r.duration=e.secondsToMilliseconds(r.duration)),r=Fe(r);const l=Ve(r.ease,r.duration);n.updateTiming({delay:e.secondsToMilliseconds(r.delay??0),duration:r.duration,easing:l}),a.push(new Ie(t))}}t(new je(a))}))}))}function ts(t,e){return t?.[e]?.keyframes.opacity}let es=[],ns=null;function ss(){ns=null;const[t]=es;var n;t&&(n=t,e.removeItem(es,n),ns=n,Qn(n).then((t=>{n.notifyReady(t),t.finished.finally(ss)})))}function is(){for(let t=es.length-1;t>=0;t--){const e=es[t],{interrupt:n}=e.options;if("immediate"===n){const n=es.slice(0,t+1).map((t=>t.update)),s=es.slice(t+1);e.update=()=>{n.forEach((t=>t()))},es=[e,...s];break}}ns&&"immediate"!==es[0]?.options.interrupt||ss()}class as{constructor(t,n={}){var s;this.currentTarget="root",this.targets=new Map,this.notifyReady=e.noop,this.readyPromise=new Promise((t=>{this.notifyReady=t})),this.update=t,this.options={interrupt:"wait",...n},s=this,es.push(s),Mn.render(is)}get(t){return this.currentTarget=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentTarget:s,targets:i}=this;i.has(s)||i.set(s,{});i.get(s)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const rs=a,os=n.reduce(((t,e)=>(t[e]=t=>r(t),t)),{});t.AsyncMotionValueAnimation=class extends Gt{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:a="loop",keyframes:r,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=h.now();const d={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:i,repeatType:a,name:o,motionValue:l,element:u,...c},m=u?.KeyframeResolver||be;this.keyframeResolver=new m(r,((t,e,n)=>this.onKeyframesResolved(t,e,d,!n)),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,n,s,i){this.keyframeResolver=void 0;const{name:a,type:r,velocity:o,delay:l,isHandoff:u,onUpdate:c}=s;this.resolvedAt=h.now(),function(t,n,s,i){const a=t[0];if(null===a)return!1;if("display"===n||"visibility"===n)return!0;const r=t[t.length-1],o=We(a,n),l=We(r,n);return e.warning(o===l,`You are trying to animate ${n} from "${a}" to "${r}". ${a} is not an animatable value - to enable this animation set ${a} to a value animatable to ${r} via the \`style\` property.`),!(!o||!l)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===s||De(s))&&i)}(t,a,r,o)||(!e.MotionGlobalConfig.instantAnimations&&l||c?.(Xt(t,s,n)),t[0]=t[t.length-1],s.duration=0,s.repeat=0);const d={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:t},m=!u&&Be(d)?new Le({...d,element:d.motionValue.owner.current}):new zt(d);m.finished.then((()=>this.notifyFinished())).catch(e.noop),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(this.keyframeResolver?.resume(),Te()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}},t.DOMKeyframesResolver=class extends be{constructor(t,e,n,s,i){super(t,e,n,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),y(s))){const i=qe(s,e.current);void 0!==i&&(t[n]=i),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!_e.has(n)||2!==t.length)return;const[s,i]=t,a=He(s),r=He(i);if(a!==r)if(ue(a)&&ue(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else de[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,s=[];for(let n=0;n<t.length;n++)(null===t[n]||("number"==typeof(i=t[n])?0===i:null===i||"none"===i||"0"===i||e.isZeroValueString(i)))&&s.push(n);var i;s.length&&function(t,e,n){let s,i=0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!un.has(e)&&I(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=ln(n,s)}(t,s,n)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=de[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const i=n.length-1,a=n[i];n[i]=de[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}},t.GroupAnimation=je,t.GroupAnimationWithThen=class extends je{then(t,e){return this.finished.finally(t).then((()=>{}))}},t.JSAnimation=zt,t.KeyframeResolver=be,t.MotionValue=fn,t.NativeAnimation=Ce,t.NativeAnimationExtended=Le,t.NativeAnimationWrapper=Ie,t.ViewTransitionBuilder=as,t.acceleratedValues=dn,t.activeAnimations=d,t.alpha=T,t.analyseComplexValue=I,t.animateValue=function(t){return new zt(t)},t.animateView=function(t,e={}){return new as(t,e)},t.animationMapKey=(t,e="")=>`${t}:${e}`,t.applyPxDefaults=function(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&cn.has(e)&&(t[n]=t[n]+"px")},t.calcGeneratorDuration=dt,t.cancelFrame=r,t.cancelMicrotask=xn,t.cancelSync=os,t.collectMotionValues=pn,t.color=W,t.complex=q,t.convertOffsetToTimes=Bt,t.createGeneratorEasing=mt,t.createRenderBatcher=i,t.cubicBezierAsString=Ee,t.defaultEasing=jt,t.defaultOffset=$t,t.defaultValueTypes=rn,t.degrees=R,t.dimensionValueTypes=Ze,t.fillOffset=Nt,t.fillWildcards=Zt,t.findDimensionValueType=He,t.findValueType=t=>Yn.find(ze(t)),t.flushKeyframeResolvers=Te,t.frame=a,t.frameData=o,t.frameSteps=l,t.generateLinearEasing=ct,t.getAnimatableNone=ln,t.getAnimationMap=function(t){const e=Ye.get(t)||new Map;return Ye.set(t,e),e},t.getComputedStyle=function(t,e){const n=window.getComputedStyle(t);return we(e)?n.getPropertyValue(e):n[e]},t.getDefaultValueType=on,t.getMixer=it,t.getValueAsType=gn,t.getValueTransition=Ge,t.getVariableValue=qe,t.hex=P,t.hover=function(t,e,n={}){const[s,i,a]=kn(t,n),r=t=>{if(!En(t))return;const{target:n}=t,s=e(n,t);if("function"!=typeof s||!n)return;const a=t=>{En(t)&&(s(t),n.removeEventListener("pointerleave",a))};n.addEventListener("pointerleave",a,i)};return s.forEach((t=>{t.addEventListener("pointerenter",r,i)})),a},t.hsla=L,t.hslaToRgba=_,t.inertia=Lt,t.interpolate=Wt,t.invisibleValues=et,t.isCSSVariableName=p,t.isCSSVariableToken=y,t.isDragActive=Sn,t.isDragging=An,t.isGenerator=De,t.isNodeOrChild=Pn,t.isPrimaryPointer=Vn,t.isWaapiSupportedEasing=function t(n){return Boolean("function"==typeof n&&ke()||!n||"string"==typeof n&&(n in Pe||ke())||e.isBezierDefinition(n)||Array.isArray(n)&&n.every(t))},t.keyframes=It,t.mapEasingToNativeEasing=Ve,t.mapValue=function(t,e,n,s){const i=jn(e,n,s);return In((()=>i(t.get())))},t.maxGeneratorDuration=ht,t.microtask=Mn,t.mix=lt,t.mixArray=at,t.mixColor=tt,t.mixComplex=ot,t.mixImmediate=z,t.mixLinearColor=H,t.mixNumber=Z,t.mixObject=rt,t.mixVisibility=nt,t.motionValue=yn,t.number=v,t.numberValueTypes=an,t.observeTimeline=function(t,e){let n;const s=()=>{const{currentTime:s}=e,i=(null===s?0:s.value)/100;n!==i&&t(i),n=i};return a.preUpdate(s,!0),()=>r(s)},t.parseCSSVariable=Ue,t.parseValueFromTransform=ae,t.percent=D,t.positionalKeys=_e,t.press=function(t,e,n={}){const[s,i,a]=kn(t,n),r=t=>{const s=t.currentTarget;if(!Kn(t)||Dn.has(s))return;Dn.add(s);const a=e(s,t),r=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),Dn.has(s)&&Dn.delete(s),Kn(t)&&"function"==typeof a&&a(t,{success:e})},o=t=>{r(t,s===window||s===document||n.useGlobalTarget||Pn(s,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",o,i),window.addEventListener("pointercancel",l,i)};return s.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,i),t instanceof HTMLElement&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const s=Fn((()=>{if(Dn.has(n))return;Cn(n,"down");const t=Fn((()=>{Cn(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>Cn(n,"cancel")),e)}));n.addEventListener("keydown",s,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",s)),e)})(t,i))),e=t,Rn.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),a},t.progressPercentage=O,t.px=F,t.readTransformValue=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return ae(n,e)},t.recordStats=function(){if(s.value)throw $n(),new Error("Stats are already being measured");const t=s;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},a.postRender(On,!0),Bn},t.resolveElements=mn,t.rgbUnit=k,t.rgba=E,t.scale=b,t.setDragLock=function(t){return"x"===t||"y"===t?An[t]?null:(An[t]=!0,()=>{An[t]=!1}):An.x||An.y?null:(An.x=An.y=!0,()=>{An.x=An.y=!1})},t.setStyle=Me,t.spring=Ot,t.startWaapiAnimation=Re,t.statsBuffer=s,t.styleEffect=function(t,e){const n=mn(t),s=[];for(let t=0;t<n.length;t++){const i=n[t],a=bn.get(i)??new vn;bn.set(i,a);for(const t in e){const n=wn(i,a,t,e[t]);s.push(n)}}return()=>{for(const t of s)t()}},t.supportedWaapiEasing=Pe,t.supportsBrowserAnimation=Be,t.supportsFlags=Ae,t.supportsLinearEasing=ke,t.supportsPartialKeyframes=hn,t.supportsScrollTimeline=xe,t.sync=rs,t.testValueType=ze,t.time=h,t.transform=jn,t.transformPropOrder=oe,t.transformProps=le,t.transformValue=In,t.transformValueTypes=sn,t.vh=C,t.vw=K}));
