!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,(function(t,e){"use strict";function n(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,Object.freeze(e)}var i=n(e),s=React,o=Symbol.for("react.element"),r=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function c(t,e,n){var i,s={},r=null,c=null;for(i in void 0!==n&&(r=""+n),void 0!==e.key&&(r=""+e.key),void 0!==e.ref&&(c=e.ref),e)a.call(e,i)&&!u.hasOwnProperty(i)&&(s[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps)void 0===s[i]&&(s[i]=e[i]);return{$$typeof:o,type:t,key:r,ref:c,props:s,_owner:l.current}}const h=r,d=c,p=c,m=e.createContext({});function f(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}const g="undefined"!=typeof window,y=g?e.useLayoutEffect:e.useEffect,v=e.createContext(null),x=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class w extends i.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,n=t instanceof HTMLElement&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function T({children:t,isPresent:n,anchorX:s}){const o=e.useId(),r=e.useRef(null),a=e.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=e.useContext(x);return e.useInsertionEffect((()=>{const{width:t,height:e,top:i,left:u,right:c}=a.current;if(n||!r.current||!t||!e)return;const h="left"===s?`left: ${u}`:`right: ${c}`;r.current.dataset.motionPopId=o;const d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`\n          [data-motion-pop-id="${o}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${h}px !important;\n            top: ${i}px !important;\n          }\n        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}}),[n]),d(w,{isPresent:n,childRef:r,sizeRef:a,children:i.cloneElement(t,{ref:r})})}const P=({children:t,initial:n,isPresent:s,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:l,anchorX:u})=>{const c=f(S),h=e.useId();let p=!0,m=e.useMemo((()=>(p=!1,{id:h,initial:n,isPresent:s,custom:r,onExitComplete:t=>{c.set(t,!0);for(const t of c.values())if(!t)return;o&&o()},register:t=>(c.set(t,!1),()=>c.delete(t))})),[s,c,o]);return a&&p&&(m={...m}),e.useMemo((()=>{c.forEach(((t,e)=>c.set(e,!1)))}),[s]),i.useEffect((()=>{!s&&!c.size&&o&&o()}),[s]),"popLayout"===l&&(t=d(T,{isPresent:s,anchorX:u,children:t})),d(v.Provider,{value:m,children:t})};function S(){return new Map}function b(t=!0){const n=e.useContext(v);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,r=e.useId();e.useEffect((()=>{if(t)return o(r)}),[t]);const a=e.useCallback((()=>t&&s&&s(r)),[r,s,t]);return!i&&s?[!1,a]:[!0]}const E=t=>t.key||"";function A(t){const n=[];return e.Children.forEach(t,(t=>{e.isValidElement(t)&&n.push(t)})),n}const M=e.createContext(null);function V(t,e){-1===t.indexOf(e)&&t.push(e)}function C(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}function R([...t],e,n){const i=e<0?t.length+e:e;if(i>=0&&i<t.length){const i=n<0?t.length+n:n,[s]=t.splice(e,1);t.splice(i,0,s)}return t}const D=(t,e,n)=>n>e?e:n<t?t:n;let k=()=>{},L=()=>{};const j={},B=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),O=t=>/^0[^.\s]+$/u.test(t);function F(t){let e;return()=>(void 0===e&&(e=t()),e)}const I=t=>t,U=(t,e)=>n=>e(t(n)),W=(...t)=>t.reduce(U),N=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class ${constructor(){this.subscriptions=[]}add(t){return V(this.subscriptions,t),()=>C(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const z=t=>1e3*t,H=t=>t/1e3;function X(t,e){return e?t*(1e3/e):0}const Y=new Set;const K=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t},_=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function G(t,e,n,i){if(t===e&&n===i)return I;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=_(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:_(s(t),e,i)}const q=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Z=t=>e=>1-t(1-e),J=G(.33,1.53,.69,.99),Q=Z(J),tt=q(Q),et=t=>(t*=2)<1?.5*Q(t):.5*(2-Math.pow(2,-10*(t-1))),nt=t=>1-Math.sin(Math.acos(t)),it=Z(nt),st=q(nt),ot=G(.42,0,1,1),rt=G(0,0,.58,1),at=G(.42,0,.58,1);const lt=t=>Array.isArray(t)&&"number"!=typeof t[0];function ut(t,e){return lt(t)?t[K(0,t.length,e)]:t}const ct=t=>Array.isArray(t)&&"number"==typeof t[0],ht={linear:I,easeIn:ot,easeInOut:at,easeOut:rt,circIn:nt,circInOut:st,circOut:it,backIn:Q,backInOut:tt,backOut:J,anticipate:et},dt=t=>{if(ct(t)){t.length;const[e,n,i,s]=t;return G(e,n,i,s)}return"string"==typeof t?ht[t]:t},pt=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],mt={value:null,addProjectionMetrics:null};function ft(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=pt.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){r.has(e)&&(c.schedule(e),t()),l++,e(a)}const c={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(u),e&&mt.value&&mt.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,c.process(t)))}};return c}(o,e?n:void 0),t)),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:h,preRender:d,render:p,postRender:m}=r,f=()=>{const o=j.useManualTiming?s.timestamp:performance.now();n=!1,j.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),h.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(f))};return{schedule:pt.reduce(((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(f)),a.schedule(e,o,r)),e}),{}),cancel:t=>{for(let e=0;e<pt.length;e++)r[pt[e]].cancel(t)},state:s,steps:r}}const{schedule:gt,cancel:yt,state:vt,steps:xt}=ft("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:I,!0);let wt;function Tt(){wt=void 0}const Pt={now:()=>(void 0===wt&&Pt.set(vt.isProcessing||j.useManualTiming?vt.timestamp:performance.now()),wt),set:t=>{wt=t,queueMicrotask(Tt)}},St={layout:0,mainThread:0,waapi:0},bt=t=>e=>"string"==typeof e&&e.startsWith(t),Et=bt("--"),At=bt("var(--"),Mt=t=>!!At(t)&&Vt.test(t.split("/*")[0].trim()),Vt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Ct={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Rt={...Ct,transform:t=>D(0,1,t)},Dt={...Ct,default:1},kt=t=>Math.round(1e5*t)/1e5,Lt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const jt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Bt=(t,e)=>n=>Boolean("string"==typeof n&&jt.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Ot=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(Lt);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},Ft={...Ct,transform:t=>Math.round((t=>D(0,255,t))(t))},It={test:Bt("rgb","red"),parse:Ot("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+Ft.transform(t)+", "+Ft.transform(e)+", "+Ft.transform(n)+", "+kt(Rt.transform(i))+")"};const Ut={test:Bt("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:It.transform},Wt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Nt=Wt("deg"),$t=Wt("%"),zt=Wt("px"),Ht=Wt("vh"),Xt=Wt("vw"),Yt=(()=>({...$t,parse:t=>$t.parse(t)/100,transform:t=>$t.transform(100*t)}))(),Kt={test:Bt("hsl","hue"),parse:Ot("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+$t.transform(kt(e))+", "+$t.transform(kt(n))+", "+kt(Rt.transform(i))+")"},_t={test:t=>It.test(t)||Ut.test(t)||Kt.test(t),parse:t=>It.test(t)?It.parse(t):Kt.test(t)?Kt.parse(t):Ut.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?It.transform(t):Kt.transform(t)},Gt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const qt="number",Zt="color",Jt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Qt(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Jt,(t=>(_t.test(t)?(i.color.push(o),s.push(Zt),n.push(_t.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(qt),n.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function te(t){return Qt(t).values}function ee(t){const{split:e,types:n}=Qt(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===qt?kt(t[o]):e===Zt?_t.transform(t[o]):t[o]}return s}}const ne=t=>"number"==typeof t?0:t;const ie={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Lt)?.length||0)+(t.match(Gt)?.length||0)>0},parse:te,createTransformer:ee,getAnimatableNone:function(t){const e=te(t);return ee(t)(e.map(ne))}};function se(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function oe({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=se(a,i,t+1/3),o=se(a,i,t),r=se(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}function re(t,e){return n=>n>0?e:t}const ae=(t,e,n)=>t+(e-t)*n,le=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},ue=[Ut,It,Kt];function ce(t){const e=(n=t,ue.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===Kt&&(i=oe(i)),i}const he=(t,e)=>{const n=ce(t),i=ce(e);if(!n||!i)return re(t,e);const s={...n};return t=>(s.red=le(n.red,i.red,t),s.green=le(n.green,i.green,t),s.blue=le(n.blue,i.blue,t),s.alpha=ae(n.alpha,i.alpha,t),It.transform(s))},de=new Set(["none","hidden"]);function pe(t,e){return de.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function me(t,e){return n=>ae(t,e,n)}function fe(t){return"number"==typeof t?me:"string"==typeof t?Mt(t)?re:_t.test(t)?he:ve:Array.isArray(t)?ge:"object"==typeof t?_t.test(t)?he:ye:re}function ge(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>fe(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function ye(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=fe(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const ve=(t,e)=>{const n=ie.createTransformer(e),i=Qt(t),s=Qt(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?de.has(t)&&!s.values.length||de.has(e)&&!i.values.length?pe(t,e):W(ge(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):re(t,e)};function xe(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return ae(t,e,n);return fe(t)(t,e)}const we=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>gt.update(e,t),stop:()=>yt(e),now:()=>vt.isProcessing?vt.timestamp:Pt.now()}},Te=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=t(e/(s-1))+", ";return`linear(${i.substring(0,i.length-2)})`},Pe=2e4;function Se(t){let e=0;let n=t.next(e);for(;!n.done&&e<Pe;)e+=50,n=t.next(e);return e>=Pe?1/0:e}function be(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(Se(i),Pe);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:H(s)}}function Ee(t,e,n){const i=Math.max(e-5,0);return X(n-t(i),e-i)}const Ae=100,Me=10,Ve=1,Ce=0,Re=800,De=.3,ke=.3,Le={granular:.01,default:2},je={granular:.005,default:.5},Be=.01,Oe=10,Fe=.05,Ie=1,Ue=.001;function We({duration:t=Re,bounce:e=De,velocity:n=Ce,mass:i=Ve}){let s,o,r=1-e;r=D(Fe,Ie,r),t=D(Be,Oe,H(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=$e(e,r),l=Math.exp(-s);return Ue-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=$e(Math.pow(e,2),r);return(-s(e)+Ue>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<Ne;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=z(t),isNaN(a))return{stiffness:Ae,damping:Me,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const Ne=12;function $e(t,e){return t*Math.sqrt(1-e*e)}const ze=["duration","bounce"],He=["stiffness","damping","mass"];function Xe(t,e){return e.some((e=>void 0!==t[e]))}function Ye(t=ke,e=De){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:Ce,stiffness:Ae,damping:Me,mass:Ve,isResolvedFromDuration:!1,...t};if(!Xe(t,He)&&Xe(t,ze))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*D(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:Ve,stiffness:s,damping:o}}else{const n=We(t);e={...e,...n,mass:Ve},e.isResolvedFromDuration=!0}return e}({...n,velocity:-H(n.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*c)),g=r-o,y=H(Math.sqrt(l/c)),v=Math.abs(g)<5;let x;if(i||(i=v?Le.granular:Le.default),s||(s=v?je.granular:je.default),f<1){const t=$e(y,f);x=e=>{const n=Math.exp(-f*y*e);return r-n*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-y*t)*(g+(m+y*g)*t);else{const t=y*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*y*e),i=Math.min(t*e,300);return r-n*((m+f*y*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&h||null,next:t=>{const e=x(t);if(p)a.done=t>=h;else{let n=0===t?m:0;f<1&&(n=0===t?z(m):Ee(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(Se(w),Pe),e=Te((e=>w.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Ke({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let w,T;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(w=t,T=Ye({keyframes:[d.value,p(d.value)],velocity:Ee(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==w||(e=!0,x(t),P(t)),void 0!==w&&t>=w?T.next(t-w):(!e&&x(t),d)}}}function _e(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||j.mix||xe,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||I:e;o=W(t,o)}i.push(o)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=N(t[i],t[i+1],n);return a[i](s)};return n?e=>u(D(t[0],t[o-1],e)):u}function Ge(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=N(0,e,i);t.push(ae(n,1,s))}}function qe(t){const e=[0];return Ge(e,t.length-1),e}function Ze(t,e){return t.map((t=>t*e))}function Je(t,e){return t.map((()=>e||at)).splice(0,t.length-1)}function Qe({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=lt(i)?i.map(dt):dt(i),o={done:!1,value:e[0]},r=_e(Ze(n&&n.length===e.length?n:qe(e),t),e,{ease:Array.isArray(s)?s:Je(e,s)});return{calculatedDuration:t,next:e=>(o.value=r(e),o.done=e>=t,o)}}Ye.applyToOptions=t=>{const e=be(t,100,Ye);return t.ease=e.ease,t.duration=z(e.duration),t.type="keyframes",t};const tn=t=>null!==t;function en(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(tn),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const nn={decay:Ke,inertia:Ke,tween:Qe,keyframes:Qe,spring:Ye};function sn(t){"string"==typeof t.type&&(t.type=nn[t.type])}class on{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const rn=t=>t/100;class an extends on{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){const{motionValue:t}=this.options;t&&t.updatedAt!==Pt.now()&&this.tick(Pt.now())}if(this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:e}=this.options;e&&e()},St.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;sn(t);const{type:e=Qe,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Qe;a!==Qe&&"number"!=typeof r[0]&&(this.mixKeyframes=W(rn,xe(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=Se(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let v=this.currentTime,x=n;if(c){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===h?(n=1-n,d&&(n-=d/r)):"mirror"===h&&(x=o)),v=D(0,1,n)*r}const w=y?{done:!1,value:u[0]}:x.next(v);s&&(w.value=s(w.value));let{done:T}=w;y||null===a||(T=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return P&&p!==Ke&&(w.value=en(u,this.options,f,this.speed)),m&&m(w.value),P&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return H(this.calculatedDuration)}get time(){return H(this.currentTime)}set time(t){t=z(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(Pt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=H(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=we,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=n??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Pt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,St.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function ln(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const un=t=>180*t/Math.PI,cn=t=>{const e=un(Math.atan2(t[1],t[0]));return dn(e)},hn={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:cn,rotateZ:cn,skewX:t=>un(Math.atan(t[1])),skewY:t=>un(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},dn=t=>((t%=360)<0&&(t+=360),t),pn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),mn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),fn={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:pn,scaleY:mn,scale:t=>(pn(t)+mn(t))/2,rotateX:t=>dn(un(Math.atan2(t[6],t[5]))),rotateY:t=>dn(un(Math.atan2(-t[2],t[0]))),rotateZ:cn,rotate:cn,skewX:t=>un(Math.atan(t[4])),skewY:t=>un(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function gn(t){return t.includes("scale")?1:0}function yn(t,e){if(!t||"none"===t)return gn(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=fn,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=hn,s=e}if(!s)return gn(e);const o=i[e],r=s[1].split(",").map(xn);return"function"==typeof o?o(r):r[o]}const vn=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return yn(n,e)};function xn(t){return parseFloat(t.trim())}const wn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Tn=(()=>new Set(wn))(),Pn=t=>t===Ct||t===zt,Sn=new Set(["x","y","z"]),bn=wn.filter((t=>!Sn.has(t)));const En={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>yn(e,"x"),y:(t,{transform:e})=>yn(e,"y")};En.translateX=En.x,En.translateY=En.y;const An=new Set;let Mn=!1,Vn=!1,Cn=!1;function Rn(){if(Vn){const t=Array.from(An).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return bn.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}Vn=!1,Mn=!1,An.forEach((t=>t.complete(Cn))),An.clear()}function Dn(){An.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(Vn=!0)}))}function kn(){Cn=!0,Dn(),Rn(),Cn=!1}class Ln{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(An.add(this),Mn||(Mn=!0,gt.read(Dn),gt.resolveKeyframes(Rn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}ln(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),An.delete(this)}cancel(){"scheduled"===this.state&&(An.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const jn=t=>t.startsWith("--");function Bn(t,e,n){jn(e)?t.style.setProperty(e,n):t.style[e]=n}const On=F((()=>void 0!==window.ScrollTimeline)),Fn={};function In(t,e){const n=F(t);return()=>Fn[e]??n()}const Un=In((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),Wn=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Nn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Wn([0,.65,.55,1]),circOut:Wn([.55,0,1,.45]),backIn:Wn([.31,.01,.66,-.59]),backOut:Wn([.33,1.53,.69,.99])};function $n(t,e){return t?"function"==typeof t?Un()?Te(t,e):"ease-out":ct(t)?Wn(t):Array.isArray(t)?t.map((t=>$n(t,e)||Nn.easeOut)):Nn[t]:void 0}function zn(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=$n(a,s);Array.isArray(h)&&(c.easing=h),mt.value&&St.waapi++;const d={delay:i,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};u&&(d.pseudoElement=u);const p=t.animate(c,d);return mt.value&&p.finished.finally((()=>{St.waapi--})),p}function Hn(t){return"function"==typeof t&&"applyToOptions"in t}function Xn({type:t,...e}){return Hn(t)&&Un()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Yn extends on{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=Xn(t);this.animation=zn(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=en(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):Bn(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return H(Number(t))}get time(){return H(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=z(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&On()?(this.animation.timeline=t,I):e(this)}}const Kn={anticipate:et,backInOut:tt,circInOut:st};function _n(t){"string"==typeof t.ease&&t.ease in Kn&&(t.ease=Kn[t.ease])}class Gn extends Yn{constructor(t){_n(t),sn(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new an({...o,autoplay:!1}),a=z(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const qn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!ie.test(t)&&"0"!==t||t.startsWith("url(")));const Zn=new Set(["opacity","clipPath","filter","transform"]),Jn=F((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));function Qn(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Jn()&&n&&Zn.has(n)&&("transform"!==n||!l)&&!a&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}class ti extends on{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=Pt.now();const h={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||Ln;this.keyframeResolver=new d(r,((t,e,n)=>this.onKeyframesResolved(t,e,h,!n)),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=Pt.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=qn(s,e),a=qn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||Hn(n))&&i)}(t,s,o,r)||(!j.instantAnimations&&a||u?.(en(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const c={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},h=!l&&Qn(c)?new Gn({...c,element:c.motionValue.owner.current}):new an(c);h.finished.then((()=>this.notifyFinished())).catch(I),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(this.keyframeResolver?.resume(),kn()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class ei{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>t.finished)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map((e=>e.attachTimeline(t)));return()=>{e.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class ni extends ei{then(t,e){return this.finished.finally(t).then((()=>{}))}}class ii extends Yn{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const si=new WeakMap,oi=(t,e="")=>`${t}:${e}`;function ri(t){const e=si.get(t)||new Map;return si.set(t,e),e}const ai=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function li(t){const e=ai.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}function ui(t,e,n=1){const[i,s]=li(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return B(t)?parseFloat(t):t}return Mt(s)?ui(s,e,n+1):s}function ci(t,e){return t?.[e]??t?.default??t}const hi=new Set(["width","height","top","left","right","bottom",...wn]),di=t=>e=>e.test(t),pi=[Ct,zt,$t,Nt,Xt,Ht,{test:t=>"auto"===t,parse:t=>t}],mi=t=>pi.find(di(t));const fi=new Set(["brightness","contrast","saturate","opacity"]);function gi(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(Lt)||[];if(!i)return t;const s=n.replace(i,"");let o=fi.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const yi=/\b([a-z-]*)\(.*?\)/gu,vi={...ie,getAnimatableNone:t=>{const e=t.match(yi);return e?e.map(gi).join(" "):t}},xi={...Ct,transform:Math.round},wi={rotate:Nt,rotateX:Nt,rotateY:Nt,rotateZ:Nt,scale:Dt,scaleX:Dt,scaleY:Dt,scaleZ:Dt,skew:Nt,skewX:Nt,skewY:Nt,distance:zt,translateX:zt,translateY:zt,translateZ:zt,x:zt,y:zt,z:zt,perspective:zt,transformPerspective:zt,opacity:Rt,originX:Yt,originY:Yt,originZ:zt},Ti={borderWidth:zt,borderTopWidth:zt,borderRightWidth:zt,borderBottomWidth:zt,borderLeftWidth:zt,borderRadius:zt,radius:zt,borderTopLeftRadius:zt,borderTopRightRadius:zt,borderBottomRightRadius:zt,borderBottomLeftRadius:zt,width:zt,maxWidth:zt,height:zt,maxHeight:zt,top:zt,right:zt,bottom:zt,left:zt,padding:zt,paddingTop:zt,paddingRight:zt,paddingBottom:zt,paddingLeft:zt,margin:zt,marginTop:zt,marginRight:zt,marginBottom:zt,marginLeft:zt,backgroundPositionX:zt,backgroundPositionY:zt,...wi,zIndex:xi,fillOpacity:Rt,strokeOpacity:Rt,numOctaves:xi},Pi={...Ti,color:_t,backgroundColor:_t,outlineColor:_t,fill:_t,stroke:_t,borderColor:_t,borderTopColor:_t,borderRightColor:_t,borderBottomColor:_t,borderLeftColor:_t,filter:vi,WebkitFilter:vi},Si=t=>Pi[t];function bi(t,e){let n=Si(t);return n!==vi&&(n=ie),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Ei=new Set(["auto","none","0"]);class Ai extends Ln{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),Mt(i))){const s=ui(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!hi.has(n)||2!==t.length)return;const[i,s]=t,o=mi(i),r=mi(s);if(o!==r)if(Pn(o)&&Pn(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else En[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||O(i)))&&n.push(e);var i;n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Ei.has(e)&&Qt(e).values.length&&(i=t[s]),s++}if(i&&n)for(const s of e)t[s]=bi(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=En[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,o=n[s];n[s]=En[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}}const Mi=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function Vi(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&Mi.has(e)&&(t[n]=t[n]+"px")}const Ci=F((()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0})),Ri=new Set(["opacity","clipPath","filter","transform"]);function Di(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}const ki={current:void 0};class Li{constructor(t,e={}){this.version="__VERSION__",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=Pt.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=Pt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new $);const n=this.events[t].add(e);return"change"===t?()=>{n(),gt.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return ki.current&&ki.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=Pt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return X(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ji(t,e){return new Li(t,e)}const Bi=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class Oi{constructor(){this.latest={},this.values=new Map}set(t,e,n,i){const s=this.values.get(t);s&&s.onRemove();const o=()=>{this.latest[t]=Bi(e.get(),Ti[t]),n&&gt.render(n)};o();const r=e.on("change",o);i&&e.addDependent(i);const a=()=>{r(),n&&yt(n),this.values.delete(t),i&&e.removeDependent(i)};return this.values.set(t,{value:e,onRemove:a}),a}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}const Fi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const Ii=new WeakMap;function Ui(t,e,n,i){let s,o;return Tn.has(n)?(e.get("transform")||e.set("transform",new Li("none"),(()=>{t.style.transform=function(t){let e="",n=!0;for(let i=0;i<wn.length;i++){const s=wn[i],o=t.latest[s];if(void 0===o)continue;let r=!0;r="number"==typeof o?o===(s.startsWith("scale")?1:0):0===parseFloat(o),r||(n=!1,e+=`${Fi[s]||s}(${t.latest[s]}) `)}return n?"none":e.trim()}(e)})),o=e.get("transform")):s=jn(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,i,s,o)}const{schedule:Wi,cancel:Ni}=ft(queueMicrotask,!1),$i={x:!1,y:!1};function zi(){return $i.x||$i.y}function Hi(t){return"x"===t||"y"===t?$i[t]?null:($i[t]=!0,()=>{$i[t]=!1}):$i.x||$i.y?null:($i.x=$i.y=!0,()=>{$i.x=$i.y=!1})}function Xi(t,e){const n=Di(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Yi(t){return!("touch"===t.pointerType||zi())}function Ki(t,e,n={}){const[i,s,o]=Xi(t,n),r=t=>{if(!Yi(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{Yi(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach((t=>{t.addEventListener("pointerenter",r,s)})),o}const _i=(t,e)=>!!e&&(t===e||_i(t,e.parentElement)),Gi=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,qi=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Zi=new WeakSet;function Ji(t){return e=>{"Enter"===e.key&&t(e)}}function Qi(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function ts(t){return Gi(t)&&!zi()}function es(t,e,n={}){const[i,s,o]=Xi(t,n),r=t=>{const i=t.currentTarget;if(!ts(t)||Zi.has(i))return;Zi.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Zi.has(i)&&Zi.delete(i),ts(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||_i(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),t instanceof HTMLElement&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=Ji((()=>{if(Zi.has(n))return;Qi(n,"down");const t=Ji((()=>{Qi(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>Qi(n,"cancel")),e)}));n.addEventListener("keydown",i,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",i)),e)})(t,s))),e=t,qi.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),o}function ns(t,e){const n=window.getComputedStyle(t);return jn(e)?n.getPropertyValue(e):n[e]}function is(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return gt.preUpdate(i,!0),()=>yt(i)}function ss(){const{value:t}=mt;null!==t?(t.frameloop.rate.push(vt.delta),t.animations.mainThread.push(St.mainThread),t.animations.waapi.push(St.waapi),t.animations.layout.push(St.layout)):yt(ss)}function os(t){return t.reduce(((t,e)=>t+e),0)/t.length}function rs(t,e=os){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const as=t=>Math.round(1e3/t);function ls(){mt.value=null,mt.addProjectionMetrics=null}function us(){const{value:t}=mt;if(!t)throw new Error("Stats are not being measured");ls(),yt(ss);const e={frameloop:{setup:rs(t.frameloop.setup),rate:rs(t.frameloop.rate),read:rs(t.frameloop.read),resolveKeyframes:rs(t.frameloop.resolveKeyframes),preUpdate:rs(t.frameloop.preUpdate),update:rs(t.frameloop.update),preRender:rs(t.frameloop.preRender),render:rs(t.frameloop.render),postRender:rs(t.frameloop.postRender)},animations:{mainThread:rs(t.animations.mainThread),waapi:rs(t.animations.waapi),layout:rs(t.animations.layout)},layoutProjection:{nodes:rs(t.layoutProjection.nodes),calculatedTargetDeltas:rs(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:rs(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=as(n.min),n.max=as(n.max),n.avg=as(n.avg),[n.min,n.max]=[n.max,n.min],e}function cs(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=_e(t[1+n],t[2+n],t[3+n]);return e?s(i):s}function hs(t){const e=[];ki.current=e;const n=t();ki.current=void 0;const i=ji(n);return function(t,e,n){const i=()=>e.set(n()),s=()=>gt.preRender(i,!1,!0),o=t.map((t=>t.on("change",s)));e.on("destroy",(()=>{o.forEach((t=>t())),yt(i)}))}(e,i,t),i}const ds=[...pi,_t,ie],ps=t=>ds.find(di(t));function ms(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let fs={},gs=null;const ys=(t,e)=>{fs[t]=e},vs=()=>{gs||(gs=document.createElement("style"),gs.id="motion-view");let t="";for(const e in fs){const n=fs[e];t+=`${e} {\n`;for(const[e,i]of Object.entries(n))t+=`  ${e}: ${i};\n`;t+="}\n"}gs.textContent=t,document.head.appendChild(gs),fs={}},xs=()=>{gs&&gs.parentElement&&gs.parentElement.removeChild(gs)};function ws(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function Ts(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}const Ps=["layout","enter","exit","new","old"];function Ss(t){const{update:e,targets:n,options:i}=t;if(!document.startViewTransition)return new Promise((async t=>{await e(),t(new ei([]))}));(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",n)||ys(":root",{"view-transition-name":"none"}),ys("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),vs();const s=document.startViewTransition((async()=>{await e()}));return s.finished.finally((()=>{xs()})),new Promise((t=>{s.ready.then((()=>{const e=document.getAnimations().filter(Ts),s=[];n.forEach(((t,e)=>{for(const n of Ps){if(!t[n])continue;const{keyframes:o,options:r}=t[n];for(let[t,a]of Object.entries(o)){if(!a)continue;const o={...ci(i,t),...ci(r,t)},l=ms(n);if("opacity"===t&&!Array.isArray(a)){a=["new"===l?0:1,a]}"function"==typeof o.delay&&(o.delay=o.delay(0,1)),o.duration&&(o.duration=z(o.duration)),o.delay&&(o.delay=z(o.delay));const u=new Yn({...o,element:document.documentElement,name:t,pseudoElement:`::view-transition-${l}(${e})`,keyframes:a});s.push(u)}}}));for(const t of e){if("finished"===t.playState)continue;const{effect:e}=t;if(!(e&&e instanceof KeyframeEffect))continue;const{pseudoElement:o}=e;if(!o)continue;const r=ws(o);if(!r)continue;const a=n.get(r.layer);if(a)bs(a,"enter")&&bs(a,"exit")&&e.getKeyframes().some((t=>t.mixBlendMode))?s.push(new ii(t)):t.cancel();else{const n="group"===r.type?"layout":"";let o={...ci(i,n)};o.duration&&(o.duration=z(o.duration)),o=Xn(o);const a=$n(o.ease,o.duration);e.updateTiming({delay:z(o.delay??0),duration:o.duration,easing:a}),s.push(new ii(t))}}t(new ei(s))}))}))}function bs(t,e){return t?.[e]?.keyframes.opacity}let Es=[],As=null;function Ms(){As=null;const[t]=Es;var e;t&&(C(Es,e=t),As=e,Ss(e).then((t=>{e.notifyReady(t),t.finished.finally(Ms)})))}function Vs(){for(let t=Es.length-1;t>=0;t--){const e=Es[t],{interrupt:n}=e.options;if("immediate"===n){const n=Es.slice(0,t+1).map((t=>t.update)),i=Es.slice(t+1);e.update=()=>{n.forEach((t=>t()))},Es=[e,...i];break}}As&&"immediate"!==Es[0]?.options.interrupt||Ms()}class Cs{constructor(t,e={}){var n;this.currentTarget="root",this.targets=new Map,this.notifyReady=I,this.readyPromise=new Promise((t=>{this.notifyReady=t})),this.update=t,this.options={interrupt:"wait",...e},n=this,Es.push(n),Wi.render(Vs)}get(t){return this.currentTarget=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentTarget:i,targets:s}=this;s.has(i)||s.set(i,{});s.get(i)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const Rs=gt,Ds=pt.reduce(((t,e)=>(t[e]=t=>yt(t),t)),{});function ks(t){return t.max-t.min}function Ls(t,e,n,i=.5){t.origin=i,t.originPoint=ae(e.min,e.max,t.origin),t.scale=ks(n)/ks(e),t.translate=ae(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function js(t,e,n,i){Ls(t.x,e.x,n.x,i?i.originX:void 0),Ls(t.y,e.y,n.y,i?i.originY:void 0)}function Bs(t,e,n){t.min=n.min+e.min,t.max=t.min+ks(e)}function Os(t,e,n){t.min=e.min-n.min,t.max=t.min+ks(e)}function Fs(t,e,n){Os(t.x,e.x,n.x),Os(t.y,e.y,n.y)}const Is=t=>!t.isLayoutDirty&&t.willUpdate(!1);function Us(){const t=new Set,e=new WeakMap,n=()=>t.forEach(Is);return{add:i=>{t.add(i),e.set(i,i.addEventListener("willUpdate",n))},remove:i=>{t.delete(i);const s=e.get(i);s&&(s(),e.delete(i)),n()},dirty:n}}const Ws=t=>Boolean(t&&t.getVelocity),Ns=t=>null!==t;const $s={type:"spring",stiffness:500,damping:25,restSpeed:10},zs={type:"keyframes",duration:.8},Hs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Xs=(t,{keyframes:e})=>e.length>2?zs:Tn.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:$s:Hs;const Ys=(t,e,n,i={},s,o)=>r=>{const a=ci(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=z(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||Object.assign(c,Xs(t,c)),c.duration&&(c.duration=z(c.duration)),c.repeatDelay&&(c.repeatDelay=z(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(h=!0)),(j.instantAnimations||j.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(Ns),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}(c.keyframes,a);if(void 0!==t)return void gt.update((()=>{c.onUpdate(t),c.onComplete()}))}return a.isSync?new an(c):new ti(c)};function Ks(t,e,n){const i=Ws(t)?t:ji(t);return i.start(Ys("",i,e,n)),i.animation}const _s=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Gs="framerAppearId",qs="data-"+_s(Gs);function Zs(t){return t.props[qs]}function Js(t){return t instanceof SVGElement&&"svg"!==t.tagName}const Qs=(t,e)=>t.depth-e.depth;class to{constructor(){this.children=[],this.isDirty=!1}add(t){V(this.children,t),this.isDirty=!0}remove(t){C(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Qs),this.isDirty=!1,this.children.forEach(t)}}function eo(t,e){const n=Pt.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(yt(i),t(o-e))};return gt.setup(i,!0),()=>yt(i)}function no(t){return Ws(t)?t.get():t}const io=["TopLeft","TopRight","BottomLeft","BottomRight"],so=io.length,oo=t=>"string"==typeof t?parseFloat(t):t,ro=t=>"number"==typeof t||zt.test(t);function ao(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const lo=co(0,.5,it),uo=co(.5,.95,I);function co(t,e,n){return i=>i<t?0:i>e?1:n(N(t,e,i))}function ho(t,e){t.min=e.min,t.max=e.max}function po(t,e){ho(t.x,e.x),ho(t.y,e.y)}function mo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function fo(t){return void 0===t||1===t}function go({scale:t,scaleX:e,scaleY:n}){return!fo(t)||!fo(e)||!fo(n)}function yo(t){return go(t)||vo(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function vo(t){return xo(t.x)||xo(t.y)}function xo(t){return t&&"0%"!==t}function wo(t,e,n){return n+e*(t-n)}function To(t,e,n,i,s){return void 0!==s&&(t=wo(t,s,i)),wo(t,n,i)+e}function Po(t,e=0,n=1,i,s){t.min=To(t.min,e,n,i,s),t.max=To(t.max,e,n,i,s)}function So(t,{x:e,y:n}){Po(t.x,e.translate,e.scale,e.originPoint),Po(t.y,n.translate,n.scale,n.originPoint)}const bo=.999999999999,Eo=1.0000000000001;function Ao(t,e){t.min=t.min+e,t.max=t.max+e}function Mo(t,e,n,i,s=.5){Po(t,e,n,ae(t.min,t.max,s),i)}function Vo(t,e){Mo(t.x,e.x,e.scaleX,e.scale,e.originX),Mo(t.y,e.y,e.scaleY,e.scale,e.originY)}function Co(t,e,n,i,s){return t=wo(t-=e,1/n,i),void 0!==s&&(t=wo(t,1/s,i)),t}function Ro(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){$t.test(e)&&(e=parseFloat(e),e=ae(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=ae(o.min,o.max,i);t===o&&(a-=e),t.min=Co(t.min,e,n,a,s),t.max=Co(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Do=["x","scaleX","originX"],ko=["y","scaleY","originY"];function Lo(t,e,n,i){Ro(t.x,e,Do,n?n.x:void 0,i?i.x:void 0),Ro(t.y,e,ko,n?n.y:void 0,i?i.y:void 0)}const jo=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Bo(t){return 0===t.translate&&1===t.scale}function Oo(t){return Bo(t.x)&&Bo(t.y)}function Fo(t,e){return t.min===e.min&&t.max===e.max}function Io(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Uo(t,e){return Io(t.x,e.x)&&Io(t.y,e.y)}function Wo(t){return ks(t.x)/ks(t.y)}function No(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class $o{constructor(){this.members=[]}add(t){V(this.members,t),t.scheduleRender()}remove(t){if(C(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const zo={};function Ho(t){for(const e in t)zo[e]=t[e],Et(e)&&(zo[e].isCSSVariable=!0)}function Xo(t){return[t("x"),t("y")]}const Yo={hasAnimatedSinceResize:!0,hasEverUpdated:!1},Ko={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},_o=["","X","Y","Z"],Go={visibility:"hidden"};let qo=0;function Zo(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function Jo(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Zs(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",gt,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&Jo(i)}function Qo({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=e?.()){this.id=qo++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,mt.value&&(Ko.nodes=Ko.calculatedTargetDeltas=Ko.calculatedProjections=0),this.nodes.forEach(nr),this.nodes.forEach(ur),this.nodes.forEach(cr),this.nodes.forEach(ir),mt.addProjectionMetrics&&mt.addProjectionMetrics(Ko)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new to)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new $),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=Js(e),this.instance=e;const{layoutId:n,layout:i,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||n)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=eo(i,250),Yo.hasAnimatedSinceResize&&(Yo.hasAnimatedSinceResize=!1,this.nodes.forEach(lr))}))}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||i)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const o=this.options.transition||s.getDefaultTransition()||gr,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!Uo(this.targetLayout,i),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...ci(o,"layout"),onPlay:r,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||lr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),yt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(hr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Jo(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(or);this.isUpdating||this.nodes.forEach(rr),this.isUpdating=!1,this.nodes.forEach(ar),this.nodes.forEach(tr),this.nodes.forEach(er),this.clearAllSnapshots();const t=Pt.now();vt.delta=D(0,1e3/60,t-vt.timestamp),vt.timestamp=t,vt.isProcessing=!0,xt.update.process(vt),xt.preRender.process(vt),xt.render.process(vt),vt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Wi.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sr),this.sharedNodes.forEach(dr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){gt.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ks(this.snapshot.measuredBox.x)||ks(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!Oo(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||yo(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),xr((i=n).x),xr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Tr))){const{scroll:t}=this.root;t&&(Ao(e.x,t.offset.x),Ao(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(po(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&po(e,t),Ao(e.x,s.offset.x),Ao(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};po(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&Vo(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),yo(i.latestValues)&&Vo(n,i.latestValues)}return yo(this.latestValues)&&Vo(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};po(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!yo(n.latestValues))continue;go(n.latestValues)&&n.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};po(i,n.measurePageBox()),Lo(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return yo(this.latestValues)&&Lo(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==vt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const n=Boolean(this.resumingFrom)||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:s}=this.options;if(this.layout&&(i||s)){if(this.resolvedRelativeTargetAt=vt.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Fs(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),po(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var o,r,a;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,Bs(o.x,r.x,a.x),Bs(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):po(this.target,this.layout.layoutBox),So(this.target,this.targetDelta)):po(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Fs(this.relativeTargetOrigin,this.target,t.target),po(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}mt.value&&Ko.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!go(this.parent.latestValues)&&!vo(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===vt.timestamp&&(n=!1),n)return;const{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!s)return;po(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Vo(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,So(t,r)),i&&yo(o.latestValues)&&Vo(t,o.latestValues))}e.x<Eo&&e.x>bo&&(e.x=1),e.y<Eo&&e.y>bo&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(mo(this.prevProjectionDelta.x,this.projectionDelta.x),mo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),js(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&No(this.projectionDelta.x,this.prevProjectionDelta.x)&&No(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),mt.value&&Ko.calculatedProjections++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(fr));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d;pr(o.x,t.x,n),pr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Fs(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),function(t,e,n,i){mr(t.x,e.x,n.x,i),mr(t.y,e.y,n.y,i)}(this.relativeTarget,this.relativeTargetOrigin,r,n),h&&(l=this.relativeTarget,d=h,Fo(l.x,d.x)&&Fo(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),po(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=ae(0,n.opacity??1,lo(i)),t.opacityExit=ae(e.opacity??1,0,uo(i))):o&&(t.opacity=ae(e.opacity??1,n.opacity??1,i));for(let s=0;s<so;s++){const o=`border${io[s]}Radius`;let r=ao(e,o),a=ao(n,o);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||ro(r)===ro(a)?(t[o]=Math.max(ae(oo(r),oo(a),i),0),($t.test(a)||$t.test(r))&&(t[o]+="%")):t[o]=a)}(e.rotate||n.rotate)&&(t.rotate=ae(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(yt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=gt.update((()=>{Yo.hasAnimatedSinceResize=!0,St.layout++,this.motionValue||(this.motionValue=ji(0)),this.currentAnimation=Ks(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{St.layout--},onComplete:()=>{St.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&wr(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=ks(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=ks(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}po(e,n),Vo(e,s),js(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new $o);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&Zo("z",t,i,this.animationValues);for(let e=0;e<_o.length;e++)Zo(`rotate${_o[e]}`,t,i,this.animationValues),Zo(`skew${_o[e]}`,t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return Go;const e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=no(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=no(t?.pointerEvents)||""),this.hasProjected&&!yo(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}const s=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),n&&(e.transform=n(s,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,i.animationValues?e.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const t in zo){if(void 0===s[t])continue;const{correct:n,applyTo:o,isCSSVariable:r}=zo[t],a="none"===e.transform?s[t]:n(s[t],i);if(o){const t=o.length;for(let n=0;n<t;n++)e[o[n]]=a}else r?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=i===this?no(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>t.currentAnimation?.stop(!1))),this.root.nodes.forEach(or),this.root.sharedNodes.clear()}}}function tr(t){t.updateLayout()}function er(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:i}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?Xo((t=>{const i=o?e.measuredBox[t]:e.layoutBox[t],s=ks(i);i.min=n[t].min,i.max=i.min+s})):wr(s,e.layoutBox,n)&&Xo((i=>{const s=o?e.measuredBox[i]:e.layoutBox[i],r=ks(n[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};js(r,n,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?js(a,t.applyTransform(i,!0),e.measuredBox):js(a,n,e.layoutBox);const l=!Oo(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Fs(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Fs(a,n,o.layoutBox),Uo(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nr(t){mt.value&&Ko.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ir(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sr(t){t.clearSnapshot()}function or(t){t.clearMeasurements()}function rr(t){t.isLayoutDirty=!1}function ar(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function lr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ur(t){t.resolveTargetDelta()}function cr(t){t.calcProjection()}function hr(t){t.resetSkewAndRotation()}function dr(t){t.removeLeadSnapshot()}function pr(t,e,n){t.translate=ae(e.translate,0,n),t.scale=ae(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function mr(t,e,n,i){t.min=ae(e.min,n.min,i),t.max=ae(e.max,n.max,i)}function fr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const gr={duration:.45,ease:[.4,0,.1,1]},yr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),vr=yr("applewebkit/")&&!yr("chrome/")?Math.round:I;function xr(t){t.min=vr(t.min),t.max=vr(t.max)}function wr(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=Wo(e),s=Wo(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Tr(t){return t!==t.root&&t.scroll?.wasRoot}function Pr(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}const Sr=Qo({attachResizeListener:(t,e)=>Pr(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),br={current:void 0},Er=Qo({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!br.current){const t=new Sr({});t.mount(window),t.setOptions({layoutScroll:!0}),br.current=t}return br.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function Ar(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Mr={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!zt.test(t))return t;t=parseFloat(t)}return`${Ar(t,e.target.x)}% ${Ar(t,e.target.y)}%`}},Vr={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=ie.parse(t);if(s.length>5)return i;const o=ie.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=ae(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};function Cr({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Rr(t,e){return Cr(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const Dr={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},kr={};for(const t in Dr)kr[t]={isEnabled:e=>Dr[t].some((t=>!!e[t]))};const Lr={current:null},jr={current:!1};function Br(){if(jr.current=!0,g)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Lr.current=t.matches;t.addListener(e),e()}else Lr.current=!1}const Or=new WeakMap;function Fr(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function Ir(t){return"string"==typeof t||Array.isArray(t)}const Ur=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Wr=["initial",...Ur];function Nr(t){return Fr(t.animate)||Wr.some((e=>Ir(t[e])))}function $r(t){return Boolean(Nr(t)||t.variants)}function zr(t){const e=[{},{}];return t?.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function Hr(t,e,n,i){if("function"==typeof e){const[s,o]=zr(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=zr(i);e=e(void 0!==n?n:t.custom,s,o)}return e}const Xr=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Yr{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ln,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=Pt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,gt.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=Nr(e),this.isVariantNode=$r(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in c){const e=c[t];void 0!==a[t]&&Ws(e)&&e.set(a[t],!1)}}mount(t){this.current=t,Or.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),jr.current||Br(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Lr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),yt(this.notifyUpdate),yt(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Tn.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{i(),s(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in kr){const e=kr[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Xr.length;e++){const n=Xr[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(Ws(s))t.addValue(i,s);else if(Ws(o))t.addValue(i,ji(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,ji(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=ji(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(B(n)||O(n))?n=parseFloat(n):!ps(n)&&ie.test(e)&&(n=bi(t,e)),this.setBaseTarget(t,Ws(n)?n.get():n)),Ws(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const i=Hr(this.props,e,this.presenceContext?.custom);i&&(n=i[t])}if(e&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,t);return void 0===i||Ws(i)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new $),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Kr extends Yr{constructor(){super(...arguments),this.KeyframeResolver=Ai}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Ws(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}const _r={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Gr=wn.length;function qr(t,e,n){let i="",s=!0;for(let o=0;o<Gr;o++){const r=wn[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=Bi(a,Ti[r]);if(!l){s=!1;i+=`${_r[r]||r}(${t}) `}n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}function Zr(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const n=e[t];if(Tn.has(t))r=!0;else if(Et(t))s[t]=n;else{const e=Bi(n,Ti[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(e.transform||(r||n?i.transform=qr(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}function Jr(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const e in n)t.style.setProperty(e,n[e])}function Qr(t,{layout:e,layoutId:n}){return Tn.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!zo[t]||"opacity"===t)}function ta(t,e,n){const{style:i}=t,s={};for(const o in i)(Ws(i[o])||e.style&&Ws(e.style[o])||Qr(o,t)||void 0!==n?.getValue(o)?.liveStyle)&&(s[o]=i[o]);return s}class ea extends Kr{constructor(){super(...arguments),this.type="html",this.renderInstance=Jr}readValueFromInstance(t,e){if(Tn.has(e))return vn(t,e);{const i=(n=t,window.getComputedStyle(n)),s=(Et(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return Rr(t,e)}build(t,e,n){Zr(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return ta(t,e,n)}}function na(){const t=function(){const t=e.useRef(!1);return y((()=>(t.current=!0,()=>{t.current=!1})),[]),t}(),[n,i]=e.useState(0),s=e.useCallback((()=>{t.current&&i(n+1)}),[n]);return[e.useCallback((()=>gt.postRender(s)),[s]),n]}const ia=t=>!0===t,sa=({children:t,id:n,inherit:i=!0})=>{const s=e.useContext(m),o=e.useContext(M),[r,a]=na(),l=e.useRef(null),u=s.id||o;null===l.current&&((t=>ia(!0===t)||"id"===t)(i)&&u&&(n=n?u+"-"+n:u),l.current={id:n,group:ia(i)&&s.group||Us()});const c=e.useMemo((()=>({...l.current,forceRender:r})),[a]);return d(m.Provider,{value:c,children:t})},oa=e.createContext({strict:!1});function ra(t){for(const e in t)kr[e]={...kr[e],...t[e]}}function aa(t){return"function"==typeof t}const la=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ua(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||la.has(t)}let ca=t=>!ua(t);function ha(t){t&&(ca=e=>e.startsWith("on")?!ua(e):t(e))}try{ha(require("@emotion/is-prop-valid").default)}catch{}function da(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(ca(s)||!0===n&&ua(s)||!e&&!ua(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}const pa=e.createContext(null);function ma(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}function fa(t,e,n){const i=t.getProps();return Hr(i,e,void 0!==n?n:i.custom,t)}const ga=t=>Array.isArray(t);function ya(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ji(n))}function va(t,e){const n=fa(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o){ya(t,e,(r=o[e],ga(r)?r[r.length-1]||0:r))}var r}function xa(t,e){const n=t.getValue("willChange");if(i=n,Boolean(Ws(i)&&i.add))return n.add(e);if(!n&&j.WillChange){const n=new j.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}function wa({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function Ta(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const i=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||u&&wa(u,e))continue;const r={delay:n,...ci(o||{},e)},c=i.get();if(void 0!==c&&!i.isAnimating&&!Array.isArray(s)&&s===c&&!r.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const n=Zs(t);if(n){const t=window.MotionHandoffAnimation(n,e,gt);null!==t&&(r.startTime=t,h=!0)}}xa(t,e),i.start(Ys(e,i,s,t.shouldReduceMotion&&hi.has(e)?{type:!1}:r,t,h));const d=i.animation;d&&l.push(d)}return r&&Promise.all(l).then((()=>{gt.update((()=>{r&&va(t,r)}))})),l}function Pa(t,e,n={}){const i=fa(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Ta(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(Sa).forEach(((t,i)=>{t.notify("AnimationStart",e),r.push(Pa(t,e,{...o,delay:n+l(i)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,o+i,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then((()=>e()))}return Promise.all([o(),r(n.delay)])}function Sa(t,e){return t.sortNodePosition(e)}function ba(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map((e=>Pa(t,e,n)));i=Promise.all(s)}else if("string"==typeof e)i=Pa(t,e,n);else{const s="function"==typeof e?fa(t,e,n.custom):e;i=Promise.all(Ta(t,s,n))}return i.then((()=>{t.notify("AnimationComplete",e)}))}function Ea(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const Aa=Wr.length;function Ma(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Ma(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Aa;n++){const i=Wr[n],s=t.props[i];(Ir(s)||!1===s)&&(e[i]=s)}return e}const Va=[...Ur].reverse(),Ca=Ur.length;function Ra(t){let e=function(t){return e=>Promise.all(e.map((({animation:e,options:n})=>ba(t,e,n))))}(t),n=La(),i=!0;const s=e=>(n,i)=>{const s=fa(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...i}=s;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=Ma(t.parent)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<Ca;e++){const d=Va[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=Ir(m),g=d===o?p.isActive:null;!1===g&&(h=e);let y=m===a[d]&&m!==r[d]&&f;if(y&&i&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...c},!p.isActive&&null===g||!m&&!p.prevProp||Fr(m)||"boolean"==typeof m)continue;const v=Da(p.prevProp,m);let x=v||d===o&&p.isActive&&!y&&f||e>h&&f,w=!1;const T=Array.isArray(m)?m:[m];let P=T.reduce(s(d),{});!1===g&&(P={});const{prevResolvedValues:S={}}=p,b={...S,...P},E=e=>{x=!0,u.has(e)&&(w=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=P[t],n=S[t];if(c.hasOwnProperty(t))continue;let i=!1;i=ga(e)&&ga(n)?!Ea(e,n):e!==n,i?null!=e?E(t):u.add(t):void 0!==e&&u.has(t)?E(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=P,p.isActive&&(c={...c,...P}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(y&&v)||w)&&l.push(...T.map((t=>({animation:t,options:{type:d}}))))}if(u.size){const e={};if("boolean"!=typeof r.initial){const n=fa(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach((n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null})),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){if(n[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach((t=>t.animationState?.setActive(e,i))),n[e].isActive=i;const s=o(e);for(const t in n)n[t].protectedKeys={};return s},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=La(),i=!0}}}function Da(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Ea(e,t)}function ka(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function La(){return{animate:ka(!0),whileInView:ka(),whileHover:ka(),whileTap:ka(),whileDrag:ka(),whileFocus:ka(),exit:ka()}}class ja{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Ba=0;const Oa={animation:{Feature:class extends ja{constructor(t){super(t),t.animationState||(t.animationState=Ra(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Fr(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends ja{constructor(){super(...arguments),this.id=Ba++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then((()=>{e(this.id)}))}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Fa(t){return{point:{x:t.pageX,y:t.pageY}}}const Ia=t=>e=>Gi(e)&&t(e,Fa(e));function Ua(t,e,n,i){return Pr(t,e,Ia(n),i)}const Wa=({current:t})=>t?t.ownerDocument.defaultView:null;function Na(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}const $a=(t,e)=>Math.abs(t-e);function za(t,e){const n=$a(t.x,e.x),i=$a(t.y,e.y);return Math.sqrt(n**2+i**2)}class Ha{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Ka(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=za(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=vt;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Xa(e,this.transformPagePoint),gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Ka("pointercancel"===t.type?this.lastMoveEventInfo:Xa(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!Gi(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=Xa(Fa(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=vt;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Ka(o,this.history)),this.removeListeners=W(Ua(this.contextWindow,"pointermove",this.handlePointerMove),Ua(this.contextWindow,"pointerup",this.handlePointerUp),Ua(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),yt(this.updatePoint)}}function Xa(t,e){return e?{point:e(t.point)}:t}function Ya(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ka({point:t},e){return{point:t,delta:Ya(t,Ga(e)),offset:Ya(t,_a(e)),velocity:qa(e,.1)}}function _a(t){return t[0]}function Ga(t){return t[t.length-1]}function qa(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=Ga(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>z(e)));)n--;if(!i)return{x:0,y:0};const o=H(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Za(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Ja(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Qa=.35;function tl(t,e,n){return{min:el(t,e),max:el(t,n)}}function el(t,e){return"number"==typeof t?t:t[e]||0}const nl=new WeakMap;class il{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new Ha(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Fa(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Hi(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Xo((t=>{let e=this.getAxisMotionValue(t).get()||0;if($t.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=ks(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),s&&gt.postRender((()=>s(t,e))),xa(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Xo((t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:Wa(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&gt.postRender((()=>s(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!sl(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?ae(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?ae(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&Na(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!n)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Za(t.x,n,s),y:Za(t.y,e,i)}}(n.layoutBox,t),this.elastic=function(t=Qa){return!1===t?t=0:!0===t&&(t=Qa),{x:tl(t,"left","right"),y:tl(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&Xo((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Na(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=Rr(t,n),{scroll:s}=e;return s&&(Ao(i.x,s.offset.x),Ao(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Ja(t.x,e.x),y:Ja(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Cr(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Xo((r=>{if(!sl(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)}));return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return xa(this.visualElement,t),n.start(Ys(t,n,0,e,this.visualElement,!1))}stopAnimation(){Xo((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){Xo((t=>this.getAxisMotionValue(t).animation?.pause()))}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Xo((e=>{const{drag:n}=this.getProps();if(!sl(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-ae(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!Na(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Xo((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=ks(t),s=ks(e);return s>i?n=N(e.min,e.max-i,t.min):i>s&&(n=N(t.min,t.max-s,e.min)),D(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Xo((e=>{if(!sl(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(ae(s,o,i[e]))}))}addListeners(){if(!this.visualElement.current)return;nl.set(this.visualElement,this);const t=Ua(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();Na(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),gt.read(e);const s=Pr(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Xo((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Qa,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function sl(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const ol=t=>(e,n)=>{t&&gt.postRender((()=>t(e,n)))};const rl=e.createContext({});class al extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;Ho(ul),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",(()=>{this.safeToRemove()})),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Yo.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||gt.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Wi.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ll(t){const[n,i]=b(),s=e.useContext(m);return d(al,{...t,layoutGroup:s,switchLayoutGroup:e.useContext(rl),isPresent:n,safeToRemove:i})}const ul={borderRadius:{...Mr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Mr,borderTopRightRadius:Mr,borderBottomLeftRadius:Mr,borderBottomRightRadius:Mr,boxShadow:Vr},cl={pan:{Feature:class extends ja{constructor(){super(...arguments),this.removePointerDownListener=I}onPointerDown(t){this.session=new Ha(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Wa(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:ol(t),onStart:ol(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&gt.postRender((()=>i(t,e)))}}}mount(){this.removePointerDownListener=Ua(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends ja{constructor(t){super(t),this.removeGroupControls=I,this.removeListeners=I,this.controls=new il(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||I}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Er,MeasureLayout:ll}};function hl(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&gt.postRender((()=>s(e,Fa(e))))}function dl(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&gt.postRender((()=>s(e,Fa(e))))}const pl=new WeakMap,ml=new WeakMap,fl=t=>{const e=pl.get(t.target);e&&e(t)},gl=t=>{t.forEach(fl)};function yl(t,e,n){const i=function({root:t,...e}){const n=t||document;ml.has(n)||ml.set(n,{});const i=ml.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(gl,{root:t,...e})),i[s]}(e);return pl.set(t,n),i.observe(t),()=>{pl.delete(t),i.unobserve(t)}}const vl={some:0,all:1};const xl={inView:{Feature:class extends ja{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:vl[i]};return yl(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends ja{mount(){const{current:t}=this.node;t&&(this.unmount=es(t,((t,e)=>(dl(this.node,e,"Start"),(t,{success:e})=>dl(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends ja{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=W(Pr(this.node.current,"focus",(()=>this.onFocus())),Pr(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends ja{mount(){const{current:t}=this.node;t&&(this.unmount=Ki(t,((t,e)=>(hl(this.node,e,"Start"),t=>hl(this.node,t,"End")))))}unmount(){}}}},wl={layout:{ProjectionNode:Er,MeasureLayout:ll}},Tl=e.createContext({});function Pl(t){const{initial:n,animate:i}=function(t,e){if(Nr(t)){const{initial:e,animate:n}=t;return{initial:!1===e||Ir(e)?e:void 0,animate:Ir(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(Tl));return e.useMemo((()=>({initial:n,animate:i})),[Sl(n),Sl(i)])}function Sl(t){return Array.isArray(t)?t.join(" "):t}const bl=Symbol.for("motionComponentSymbol");function El(t,n,i){return e.useCallback((e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):Na(i)&&(i.current=e))}),[n])}function Al(t,n,i,s,o){const{visualElement:r}=e.useContext(Tl),a=e.useContext(oa),l=e.useContext(v),u=e.useContext(x).reducedMotion,c=e.useRef(null);s=s||a.renderer,!c.current&&s&&(c.current=s(t,{visualState:n,parent:r,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));const h=c.current,d=e.useContext(rl);!h||h.projection||!o||"html"!==h.type&&"svg"!==h.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Ml(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&Na(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:c,layoutScroll:l,layoutRoot:u})}(c.current,i,o,d);const p=e.useRef(!1);e.useInsertionEffect((()=>{h&&p.current&&h.update(i,l)}));const m=i[qs],f=e.useRef(Boolean(m)&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return y((()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),Wi.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())})),e.useEffect((()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask((()=>{window.MotionHandoffMarkAsComplete?.(m)})),f.current=!1))})),h}function Ml(t){if(t)return!1!==t.options.allowProjection?t.projection:Ml(t.parent)}function Vl({preloadedFeatures:t,createVisualElement:n,useRender:i,useVisualState:s,Component:o}){function r(t,r){let a;const l={...e.useContext(x),...t,layoutId:Cl(t)},{isStatic:u}=l,c=Pl(t),h=s(t,u);if(!u&&g){e.useContext(oa).strict;const t=function(t){const{drag:e,layout:n}=kr;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(l);a=t.MeasureLayout,c.visualElement=Al(o,h,l,n,t.ProjectionNode)}return p(Tl.Provider,{value:c,children:[a&&c.visualElement?d(a,{visualElement:c.visualElement,...l}):null,i(o,t,El(h,c.visualElement,r),h,u,c.visualElement)]})}t&&ra(t),r.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const a=e.forwardRef(r);return a[bl]=o,a}function Cl({layoutId:t}){const n=e.useContext(m).id;return n&&void 0!==t?n+"-"+t:t}const Rl=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Dl(t,e,n){for(const i in e)Ws(e[i])||Qr(i,n)||(t[i]=e[i])}function kl(t,n){const i={};return Dl(i,t.style||{},t),Object.assign(i,function({transformTemplate:t},n){return e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return Zr(e,n,t),Object.assign({},e.vars,e.style)}),[n])}(t,n)),i}function Ll(t,e){const n={},i=kl(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const jl=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Bl(t){return"string"==typeof t&&!t.includes("-")&&!!(jl.indexOf(t)>-1||/[A-Z]/u.test(t))}const Ol={offset:"stroke-dashoffset",array:"stroke-dasharray"},Fl={offset:"strokeDashoffset",array:"strokeDasharray"};function Il(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,u,c){if(Zr(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==n&&(h.y=n),void 0!==i&&(h.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Ol:Fl;t[o.offset]=zt.transform(-i);const r=zt.transform(e),a=zt.transform(n);t[o.array]=`${r} ${a}`}(h,s,o,r,!1)}const Ul=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Wl=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Nl(t,n,i,s){const o=e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Il(e,n,Wl(s),t.transformTemplate,t.style),{...e.attrs,style:{...e.style}}}),[n]);if(t.style){const e={};Dl(e,t.style,t),o.style={...e,...o.style}}return o}function $l(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(Bl(n)?Nl:Ll)(i,o,r,n),l=da(i,"string"==typeof n,t),u=n!==e.Fragment?{...l,...a,ref:s}:{},{children:c}=i,h=e.useMemo((()=>Ws(c)?c.get():c),[c]);return e.createElement(n,{...u,children:h})}}const zl=t=>(n,i)=>{const s=e.useContext(Tl),o=e.useContext(v),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:Hl(n,i,s,t),renderState:e()}}(t,n,s,o);return i?r():f(r)};function Hl(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=no(o[t]);let{initial:r,animate:a}=t;const l=Nr(t),u=$r(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!Fr(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const i=Hr(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}const Xl={useVisualState:zl({scrapeMotionValuesFromProps:ta,createRenderState:Rl})};function Yl(t,e,n){const i=ta(t,e,n);for(const n in t)if(Ws(t[n])||Ws(e[n])){i[-1!==wn.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}const Kl={useVisualState:zl({scrapeMotionValuesFromProps:Yl,createRenderState:Ul})};function _l(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return Vl({...Bl(n)?Kl:Xl,preloadedFeatures:t,useRender:$l(i),createVisualElement:e,Component:n})}}const Gl=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ql extends Kr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=jo}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Tn.has(e)){const t=Si(e);return t&&t.default||0}return e=Gl.has(e)?e:_s(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return Yl(t,e,n)}build(t,e,n){Il(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){Jr(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(Gl.has(n)?n:_s(n),e.attrs[n])}(t,e,0,i)}mount(t){this.isSVGTag=Wl(t.tagName),super.mount(t)}}const Zl=(t,n)=>Bl(t)?new ql(n):new ea(n,{allowProjection:t!==e.Fragment}),Jl=ma(_l({...Oa,...xl,...cl,...wl},Zl));function Ql({children:t,as:n="ul",axis:i="y",onReorder:s,values:o,...r},a){const l=f((()=>Jl[n])),u=[],c=e.useRef(!1),h={axis:i,registerItem:(t,e)=>{const n=u.findIndex((e=>t===e.value));-1!==n?u[n].layout=e[i]:u.push({value:t,layout:e[i]}),u.sort(nu)},updateOrder:(t,e,n)=>{if(c.current)return;const i=function(t,e,n,i){if(!i)return t;const s=t.findIndex((t=>t.value===e));if(-1===s)return t;const o=i>0?1:-1,r=t[s+o];if(!r)return t;const a=t[s],l=r.layout,u=ae(l.min,l.max,.5);return 1===o&&a.layout.max+n>u||-1===o&&a.layout.min+n<u?R(t,s,s+o):t}(u,t,e,n);u!==i&&(c.current=!0,s(i.map(eu).filter((t=>-1!==o.indexOf(t)))))}};return e.useEffect((()=>{c.current=!1})),d(l,{...r,ref:a,ignoreStrict:!0,children:d(pa.Provider,{value:h,children:t})})}const tu=e.forwardRef(Ql);function eu(t){return t.value}function nu(t,e){return t.layout.min-e.layout.min}function iu(t){const n=f((()=>ji(t))),{isStatic:i}=e.useContext(x);if(i){const[,i]=e.useState(t);e.useEffect((()=>n.on("change",i)),[])}return n}function su(t,e){const n=iu(e()),i=()=>n.set(e());return i(),y((()=>{const e=()=>gt.preRender(i,!1,!0),n=t.map((t=>t.on("change",e)));return()=>{n.forEach((t=>t())),yt(i)}})),n}function ou(t,e,n,i){if("function"==typeof t)return function(t){ki.current=[],t();const e=su(ki.current,t);return ki.current=void 0,e}(t);const s="function"==typeof e?e:cs(e,n,i);return Array.isArray(t)?ru(t,s):ru([t],(([t])=>s(t)))}function ru(t,e){const n=f((()=>[]));return su(t,(()=>{n.length=0;const i=t.length;for(let e=0;e<i;e++)n[e]=t[e].get();return e(n)}))}function au(t,e=0){return Ws(t)?t:iu(e)}function lu({children:t,style:n={},value:i,as:s="li",onDrag:o,layout:r=!0,...a},l){const u=f((()=>Jl[s])),c=e.useContext(pa),h={x:au(n.x),y:au(n.y)},p=ou([h.x,h.y],(([t,e])=>t||e?1:"unset")),{axis:m,registerItem:g,updateOrder:y}=c;return d(u,{drag:m,...a,dragSnapToOrigin:!0,style:{...n,x:h.x,y:h.y,zIndex:p},layout:r,onDrag:(t,e)=>{const{velocity:n}=e;n[m]&&y(i,h[m].get(),n[m]),o&&o(t,e)},onLayoutMeasure:t=>g(i,t),ref:l,ignoreStrict:!0,children:t})}const uu=e.forwardRef(lu);var cu=Object.freeze({__proto__:null,Group:tu,Item:uu});function hu(t){return"object"==typeof t&&!Array.isArray(t)}function du(t,e,n,i){return"string"==typeof t&&hu(e)?Di(t,n,i):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function pu(t,e,n){return t*(e+1)}function mu(t,e,n,i){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:i.get(e)??t}function fu(t,e,n,i,s,o){!function(t,e,n){for(let i=0;i<t.length;i++){const s=t[i];s.at>e&&s.at<n&&(C(t,s),i--)}}(t,s,o);for(let r=0;r<e.length;r++)t.push({value:e[r],at:ae(s,o,i[r]),easing:ut(n,r)})}function gu(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function yu(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function vu(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function xu(t,e){return e[t]||(e[t]=[]),e[t]}function wu(t){return Array.isArray(t)?t:[t]}function Tu(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const Pu=t=>"number"==typeof t,Su=t=>t.every(Pu);class bu extends Yr{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}function Eu(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=Js(t)?new ql(e):new ea(e);n.mount(t),Or.set(t,n)}function Au(t){const e=new bu({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),Or.set(t,e)}function Mu(t,e,n,i){const s=[];if(function(t,e){return Ws(t)||"number"==typeof t||"string"==typeof t&&!hu(e)}(t,e))s.push(Ks(t,hu(e)&&e.default||e,n&&n.default||n));else{const o=du(t,e,i),r=o.length;for(let t=0;t<r;t++){const i=o[t],a=i instanceof Element?Eu:Au;Or.has(i)||a(i);const l=Or.get(i),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,r)),s.push(...Ta(l,{...e,transition:u},{}))}}return s}function Vu(t,e,n){const i=[],s=function(t,{defaultTransition:e={},...n}={},i,s){const o=e.duration||.3,r=new Map,a=new Map,l={},u=new Map;let c=0,h=0,d=0;for(let n=0;n<t.length;n++){const r=t[n];if("string"==typeof r){u.set(r,h);continue}if(!Array.isArray(r)){u.set(r.name,mu(h,r.at,c,u));continue}let[p,m,f={}]=r;void 0!==f.at&&(h=mu(h,f.at,c,u));let g=0;const y=(t,n,i,r=0,a=0)=>{const l=wu(t),{delay:u=0,times:c=qe(l),type:p="keyframes",repeat:m,repeatType:f,repeatDelay:y=0,...v}=n;let{ease:x=e.ease||"easeOut",duration:w}=n;const T="function"==typeof u?u(r,a):u,P=l.length,S=Hn(p)?p:s?.[p];if(P<=2&&S){let t=100;if(2===P&&Su(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...v};void 0!==w&&(e.duration=z(w));const n=be(e,t,S);x=n.ease,w=n.duration}w??(w=o);const b=h+T;1===c.length&&0===c[0]&&(c[1]=1);const E=c.length-l.length;if(E>0&&Ge(c,E),1===l.length&&l.unshift(null),m){w=pu(w,m);const t=[...l],e=[...c];x=Array.isArray(x)?[...x]:[x];const n=[...x];for(let i=0;i<m;i++){l.push(...t);for(let s=0;s<t.length;s++)c.push(e[s]+(i+1)),x.push(0===s?"linear":ut(n,s-1))}gu(c,m)}const A=b+w;fu(i,l,x,c,b,A),g=Math.max(T+w,g),d=Math.max(A,d)};if(Ws(p))y(m,f,xu("default",vu(p,a)));else{const t=du(p,m,i,l),e=t.length;for(let n=0;n<e;n++){const i=vu(t[n],a);for(const t in m)y(m[t],Tu(f,t),xu(t,i),n,e)}}c=h,h+=g}return a.forEach(((t,i)=>{for(const s in t){const o=t[s];o.sort(yu);const a=[],l=[],u=[];for(let t=0;t<o.length;t++){const{at:e,value:n,easing:i}=o[t];a.push(n),l.push(N(0,d,e)),u.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),r.has(i)||r.set(i,{keyframes:{},transition:{}});const c=r.get(i);c.keyframes[s]=a,c.transition[s]={...e,duration:d,ease:u,times:l,...n}}})),r}(t,e,n,{spring:Ye});return s.forEach((({keyframes:t,transition:e},n)=>{i.push(...Mu(n,t,e))})),i}function Cu(t){return function(e,n,i){let s=[];var o;o=e,s=Array.isArray(o)&&o.some(Array.isArray)?Vu(e,n,t):Mu(e,n,i,t);const r=new ni(s);return t&&t.animations.push(r),r}}const Ru=Cu();const Du=t=>function(e,n,i){return new ni(function(t,e,n,i){const s=Di(t,i),o=s.length,r=[];for(let t=0;t<o;t++){const i=s[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,o));for(const t in e){let n=e[t];Array.isArray(n)||(n=[n]);const s={...ci(a,t)};s.duration&&(s.duration=z(s.duration)),s.delay&&(s.delay=z(s.delay));const o=ri(i),l=oi(t,s.pseudoElement||""),u=o.get(l);u&&u.stop(),r.push({map:o,key:l,unresolvedKeyframes:n,options:{...s,element:i,name:t,allowFlatten:!a.type&&!a.ease}})}}for(let t=0;t<r.length;t++){const{unresolvedKeyframes:e,options:n}=r[t],{element:i,name:s,pseudoElement:o}=n;o||null!==e[0]||(e[0]=ns(i,s)),ln(e),Vi(e,s),!o&&e.length<2&&e.unshift(ns(i,s)),n.keyframes=e}const a=[];for(let t=0;t<r.length;t++){const{map:e,key:n,options:i}=r[t],s=new Yn(i);e.set(n,s),s.finished.finally((()=>e.delete(n))),a.push(s)}return a}(e,n,i,t))},ku=Du(),Lu=new WeakMap;let ju;function Bu({target:t,contentRect:e,borderBoxSize:n}){Lu.get(t)?.forEach((i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function Ou(t){t.forEach(Bu)}function Fu(t,e){ju||"undefined"!=typeof ResizeObserver&&(ju=new ResizeObserver(Ou));const n=Di(t);return n.forEach((t=>{let n=Lu.get(t);n||(n=new Set,Lu.set(t,n)),n.add(e),ju?.observe(t)})),()=>{n.forEach((t=>{const n=Lu.get(t);n?.delete(e),n?.size||ju?.unobserve(t)}))}}const Iu=new Set;let Uu;function Wu(t){return Iu.add(t),Uu||(Uu=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Iu.forEach((t=>t(e)))},window.addEventListener("resize",Uu)),()=>{Iu.delete(t),!Iu.size&&Uu&&(Uu=void 0)}}const Nu={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function $u(t,e,n,i){const s=n[e],{length:o,position:r}=Nu[e],a=s.current,l=n.time;s.current=t[`scroll${r}`],s.scrollLength=t[`scroll${o}`]-t[`client${o}`],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=N(0,s.scrollLength,s.current);const u=i-l;s.velocity=u>50?0:X(s.current-a,u)}const zu={start:0,center:.5,end:1};function Hu(t,e,n=0){let i=0;if(t in zu&&(t=zu[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(i=e*t),n+i}const Xu=[0,0];function Yu(t,e,n,i){let s=Array.isArray(t)?t:Xu,o=0,r=0;return"number"==typeof t?s=[t,t]:"string"==typeof t&&(s=(t=t.trim()).includes(" ")?t.split(" "):[t,zu[t]?t:"0"]),o=Hu(s[0],n,i),r=Hu(s[1],e),o-r}const Ku={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},_u={x:0,y:0};function Gu(t,e,n){const{offset:i=Ku.All}=n,{target:s=t,axis:o="y"}=n,r="y"===o?"height":"width",a=s!==t?function(t,e){const n={x:0,y:0};let i=t;for(;i&&i!==e;)if(i instanceof HTMLElement)n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if("svg"===i.tagName){const t=i.getBoundingClientRect();i=i.parentElement;const e=i.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(i instanceof SVGGraphicsElement))break;{const{x:t,y:e}=i.getBBox();n.x+=t,n.y+=e;let s=null,o=i.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=i.parentNode;i=s}}return n}(s,t):_u,l=s===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(s),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=i.length;for(let t=0;t<h;t++){const n=Yu(i[t],u[r],l[r],a[o]);c||n===e[o].interpolatorOffsets[t]||(c=!0),e[o].offset[t]=n}c&&(e[o].interpolate=_e(e[o].offset,qe(i),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=D(0,1,e[o].interpolate(e[o].current))}function qu(t,e,n,i={}){return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!==t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),update:e=>{!function(t,e,n){$u(t,"x",e,n),$u(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&Gu(t,n,i)},notify:()=>e(n)}}const Zu=new WeakMap,Ju=new WeakMap,Qu=new WeakMap,tc=t=>t===document.documentElement?window:t;function ec(t,{container:e=document.documentElement,...n}={}){let i=Qu.get(e);i||(i=new Set,Qu.set(e,i));const s=qu(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(i.add(s),!Zu.has(e)){const t=()=>{for(const t of i)t.measure()},n=()=>{for(const t of i)t.update(vt.timestamp)},s=()=>{for(const t of i)t.notify()},a=()=>{gt.read(t),gt.read(n),gt.preUpdate(s)};Zu.set(e,a);const l=tc(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&Ju.set(e,(r=a,"function"==typeof(o=e)?Wu(o):Fu(o,r))),l.addEventListener("scroll",a,{passive:!0}),a()}var o,r;const a=Zu.get(e);return gt.read(a,!1,!0),()=>{yt(a);const t=Qu.get(e);if(!t)return;if(t.delete(s),t.size)return;const n=Zu.get(e);Zu.delete(e),n&&(tc(e).removeEventListener("scroll",n),Ju.get(e)?.(),window.removeEventListener("resize",n))}}const nc=new Map;function ic({source:t,container:e,...n}){const{axis:i}=n;t&&(e=t);const s=nc.get(e)??new Map;nc.set(e,s);const o=n.target??"self",r=s.get(o)??{},a=i+(n.offset??[]).join(",");return r[a]||(r[a]=!n.target&&On()?new ScrollTimeline({source:e,axis:i}):function(t){const e={value:0},n=ec((n=>{e.value=100*n[t.axis].progress}),t);return{currentTime:e,cancel:n}}({container:e,...n})),r[a]}function sc(t,{axis:e="y",container:n=document.documentElement,...i}={}){n===document.documentElement&&("y"===e&&n.scrollHeight===n.clientHeight||"x"===e&&n.scrollWidth===n.clientWidth)&&(n=document.body);const s={axis:e,container:n,...i};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)?ec((n=>{t(n[e.axis].progress,n)}),e):is(t,ic(e))}(t,s):function(t,e){const n=ic(e);return t.attachTimeline({timeline:e.target?void 0:n,observe:t=>(t.pause(),is((e=>{t.time=t.duration*e}),n))})}(t,s)}const oc={some:0,all:1};function rc(t,e,{root:n,margin:i,amount:s="some"}={}){const o=Di(t),r=new WeakMap,a=new IntersectionObserver((t=>{t.forEach((t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),r.delete(t.target))}))}),{root:n,rootMargin:i,threshold:"number"==typeof s?s:oc[s]});return o.forEach((t=>a.observe(t))),()=>a.disconnect()}const ac=ma(_l());function lc(t){return e.useEffect((()=>()=>t()),[])}const uc={renderer:Zl,...Oa,...xl},cc={...uc,...cl,...wl},hc={renderer:Zl,...Oa};function dc(t,n,i){e.useInsertionEffect((()=>t.on(n,i)),[t,n,i])}function pc(t,e){Boolean(!e||e.current)}const mc=()=>({scrollX:ji(0),scrollY:ji(0),scrollXProgress:ji(0),scrollYProgress:ji(0)});function fc({container:t,target:n,layoutEffect:i=!0,...s}={}){const o=f(mc);return(i?y:e.useEffect)((()=>(pc(0,n),pc(0,t),sc(((t,{x:e,y:n})=>{o.scrollX.set(e.current),o.scrollXProgress.set(e.progress),o.scrollY.set(n.current),o.scrollYProgress.set(n.progress)}),{...s,container:t?.current||void 0,target:n?.current||void 0}))),[t,n,JSON.stringify(s.offset)]),o}function gc(t,e){return e?t+e:t}function yc(t){return"number"==typeof t?t:parseFloat(t)}function vc(t){const n=e.useRef(0),{isStatic:i}=e.useContext(x);e.useEffect((()=>{if(i)return;const e=({timestamp:e,delta:i})=>{n.current||(n.current=e),t(e-n.current,i)};return gt.update(e,!0),()=>yt(e)}),[t])}class xc extends Li{constructor(){super(...arguments),this.values=[]}add(t){const e=function(t){return Tn.has(t)?"transform":Ri.has(t)?_s(t):void 0}(t);e&&(V(this.values,e),this.update())}update(){this.set(this.values.length?this.values.join(", "):"auto")}}function wc(){!jr.current&&Br();const[t]=e.useState(Lr.current);return t}function Tc(t,e){[...e].reverse().forEach((n=>{const i=t.getVariant(n);i&&va(t,i),t.variantChildren&&t.variantChildren.forEach((t=>{Tc(t,e)}))}))}function Pc(){const t=new Set,e={subscribe:e=>(t.add(e),()=>{t.delete(e)}),start(e,n){const i=[];return t.forEach((t=>{i.push(ba(t,e,{transitionOverride:n}))})),Promise.all(i)},set:e=>t.forEach((t=>{!function(t,e){Array.isArray(e)?Tc(t,e):"string"==typeof e?Tc(t,[e]):va(t,e)}(t,e)})),stop(){t.forEach((t=>{!function(t){t.values.forEach((t=>t.stop()))}(t)}))},mount:()=>()=>{e.stop()}};return e}function Sc(){const t=f(Pc);return y(t.mount,[]),t}const bc=Sc;class Ec{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach((n=>{n.start(t.nativeEvent||t,e)}))}}const Ac=()=>new Ec;function Mc(t){return null!==t&&"object"==typeof t&&bl in t}function Vc(){return Cc}function Cc(t){br.current&&(br.current.isUpdating=!1,br.current.blockUpdate(),t&&t())}const Rc=new Map,Dc=new Map,kc=(t,e)=>`${t}: ${Tn.has(e)?"transform":e}`;function Lc(t,e,n){const i=kc(t,e),s=Rc.get(i);if(!s)return null;const{animation:o,startTime:r}=s;function a(){window.MotionCancelOptimisedAnimation?.(t,e,n)}return o.onfinish=a,null===r||window.MotionHandoffIsComplete?.(t)?(a(),null):r}let jc,Bc;const Oc=new Set;function Fc(){Oc.forEach((t=>{t.animation.play(),t.animation.startTime=t.startTime})),Oc.clear()}const Ic=()=>({});class Uc extends Yr{constructor(){super(...arguments),this.measureInstanceViewportBox=jo}build(){}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return{}}getBaseTargetFromProps(){}readValueFromInstance(t,e,n){return n.initialState[e]||0}sortInstanceNodePosition(){return 0}}const Wc=zl({scrapeMotionValuesFromProps:Ic,createRenderState:Ic});let Nc=0;const $c=t=>t>.001?1/t:1e5;t.AnimatePresence=({children:t,custom:n,initial:i=!0,onExitComplete:s,presenceAffectsLayout:o=!0,mode:r="sync",propagate:a=!1,anchorX:l="left"})=>{const[u,c]=b(a),p=e.useMemo((()=>A(t)),[t]),g=a&&!u?[]:p.map(E),v=e.useRef(!0),x=e.useRef(p),w=f((()=>new Map)),[T,S]=e.useState(p),[M,V]=e.useState(p);y((()=>{v.current=!1,x.current=p;for(let t=0;t<M.length;t++){const e=E(M[t]);g.includes(e)?w.delete(e):!0!==w.get(e)&&w.set(e,!1)}}),[M,g.length,g.join("-")]);const C=[];if(p!==T){let t=[...p];for(let e=0;e<M.length;e++){const n=M[e],i=E(n);g.includes(i)||(t.splice(e,0,n),C.push(n))}return"wait"===r&&C.length&&(t=C),V(A(t)),S(p),null}const{forceRender:R}=e.useContext(m);return d(h,{children:M.map((t=>{const e=E(t),h=!(a&&!u)&&(p===M||g.includes(e));return d(P,{isPresent:h,initial:!(v.current&&!i)&&void 0,custom:n,presenceAffectsLayout:o,mode:r,onExitComplete:h?void 0:()=>{if(!w.has(e))return;w.set(e,!0);let t=!0;w.forEach((e=>{e||(t=!1)})),t&&(R?.(),V(x.current),a&&c?.(),s&&s())},anchorX:l,children:t},e)}))})},t.AnimateSharedLayout=({children:t})=>(i.useEffect((()=>{}),[]),d(sa,{id:f((()=>"asl-"+Nc++)),children:t})),t.AsyncMotionValueAnimation=ti,t.DOMKeyframesResolver=Ai,t.DeprecatedLayoutGroupContext=M,t.DragControls=Ec,t.FlatTree=to,t.GroupAnimation=ei,t.GroupAnimationWithThen=ni,t.JSAnimation=an,t.KeyframeResolver=Ln,t.LayoutGroup=sa,t.LayoutGroupContext=m,t.LazyMotion=function({children:t,features:n,strict:i=!1}){const[,s]=e.useState(!aa(n)),o=e.useRef(void 0);if(!aa(n)){const{renderer:t,...e}=n;o.current=t,ra(e)}return e.useEffect((()=>{aa(n)&&n().then((({renderer:t,...e})=>{ra(e),o.current=t,s(!0)}))}),[]),d(oa.Provider,{value:{renderer:o.current,strict:i},children:t})},t.MotionConfig=function({children:t,isValidProp:n,...i}){n&&ha(n),(i={...e.useContext(x),...i}).isStatic=f((()=>i.isStatic));const s=e.useMemo((()=>i),[JSON.stringify(i.transition),i.transformPagePoint,i.reducedMotion]);return d(x.Provider,{value:s,children:t})},t.MotionConfigContext=x,t.MotionContext=Tl,t.MotionGlobalConfig=j,t.MotionValue=Li,t.NativeAnimation=Yn,t.NativeAnimationExtended=Gn,t.NativeAnimationWrapper=ii,t.PresenceContext=v,t.Reorder=cu,t.SubscriptionManager=$,t.SwitchLayoutGroupContext=rl,t.ViewTransitionBuilder=Cs,t.VisualElement=Yr,t.WillChangeMotionValue=xc,t.acceleratedValues=Ri,t.activeAnimations=St,t.addPointerEvent=Ua,t.addPointerInfo=Ia,t.addScaleCorrector=Ho,t.addUniqueItem=V,t.alpha=Rt,t.analyseComplexValue=Qt,t.animate=Ru,t.animateMini=ku,t.animateValue=function(t){return new an(t)},t.animateView=function(t,e={}){return new Cs(t,e)},t.animateVisualElement=ba,t.animationControls=Pc,t.animationMapKey=oi,t.animations=Oa,t.anticipate=et,t.applyPxDefaults=Vi,t.backIn=Q,t.backInOut=tt,t.backOut=J,t.buildTransform=qr,t.calcGeneratorDuration=Se,t.calcLength=ks,t.cancelFrame=yt,t.cancelMicrotask=Ni,t.cancelSync=Ds,t.circIn=nt,t.circInOut=st,t.circOut=it,t.clamp=D,t.collectMotionValues=ki,t.color=_t,t.complex=ie,t.convertOffsetToTimes=Ze,t.createBox=jo,t.createGeneratorEasing=be,t.createRenderBatcher=ft,t.createRendererMotionComponent=Vl,t.createScopedAnimate=Cu,t.cubicBezier=G,t.cubicBezierAsString=Wn,t.defaultEasing=Je,t.defaultOffset=qe,t.defaultValueTypes=Pi,t.degrees=Nt,t.delay=eo,t.dimensionValueTypes=pi,t.disableInstantTransitions=function(){j.instantAnimations=!1},t.distance=$a,t.distance2D=za,t.domAnimation=uc,t.domMax=cc,t.domMin=hc,t.easeIn=ot,t.easeInOut=at,t.easeOut=rt,t.easingDefinitionToFunction=dt,t.fillOffset=Ge,t.fillWildcards=ln,t.filterProps=da,t.findDimensionValueType=mi,t.findValueType=ps,t.flushKeyframeResolvers=kn,t.frame=gt,t.frameData=vt,t.frameSteps=xt,t.generateLinearEasing=Te,t.getAnimatableNone=bi,t.getAnimationMap=ri,t.getComputedStyle=ns,t.getDefaultValueType=Si,t.getEasingForSegment=ut,t.getMixer=fe,t.getValueAsType=Bi,t.getValueTransition=ci,t.getVariableValue=ui,t.hasWarned=function(t){return Y.has(t)},t.hex=Ut,t.hover=Ki,t.hsla=Kt,t.hslaToRgba=oe,t.inView=rc,t.inertia=Ke,t.interpolate=_e,t.invariant=L,t.invisibleValues=de,t.isBezierDefinition=ct,t.isBrowser=g,t.isCSSVariableName=Et,t.isCSSVariableToken=Mt,t.isDragActive=zi,t.isDragging=$i,t.isEasingArray=lt,t.isGenerator=Hn,t.isMotionComponent=Mc,t.isMotionValue=Ws,t.isNodeOrChild=_i,t.isNumericalString=B,t.isPrimaryPointer=Gi,t.isValidMotionProp=ua,t.isWaapiSupportedEasing=function t(e){return Boolean("function"==typeof e&&Un()||!e||"string"==typeof e&&(e in Nn||Un())||ct(e)||Array.isArray(e)&&e.every(t))},t.isZeroValueString=O,t.keyframes=Qe,t.m=ac,t.makeUseVisualState=zl,t.mapEasingToNativeEasing=$n,t.mapValue=function(t,e,n,i){const s=cs(e,n,i);return hs((()=>s(t.get())))},t.maxGeneratorDuration=Pe,t.memo=F,t.microtask=Wi,t.millisecondsToSeconds=H,t.mirrorEasing=q,t.mix=xe,t.mixArray=ge,t.mixColor=he,t.mixComplex=ve,t.mixImmediate=re,t.mixLinearColor=le,t.mixNumber=ae,t.mixObject=ye,t.mixVisibility=pe,t.motion=Jl,t.motionValue=ji,t.moveItem=R,t.noop=I,t.number=Ct,t.numberValueTypes=Ti,t.observeTimeline=is,t.optimizedAppearDataAttribute=qs,t.parseCSSVariable=li,t.parseValueFromTransform=yn,t.percent=$t,t.pipe=W,t.positionalKeys=hi,t.press=es,t.progress=N,t.progressPercentage=Yt,t.px=zt,t.readTransformValue=vn,t.recordStats=function(){if(mt.value)throw ls(),new Error("Stats are already being measured");const t=mt;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},gt.postRender(ss,!0),us},t.removeItem=C,t.resolveElements=Di,t.resolveMotionValue=no,t.reverseEasing=Z,t.rgbUnit=Ft,t.rgba=It,t.scale=Dt,t.scroll=sc,t.scrollInfo=ec,t.secondsToMilliseconds=z,t.setDragLock=Hi,t.setStyle=Bn,t.spring=Ye,t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:i}={}){return(s,o)=>{const r="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,o),a=Math.abs(r-s);let l=t*a;if(i){const e=o*t;l=dt(i)(l/e)*e}return e+l}},t.startOptimizedAppearAnimation=function(t,e,n,i,s){if(window.MotionIsMounted)return;const o=t.dataset[Gs];if(!o)return;window.MotionHandoffAnimation=Lc;const r=kc(o,e);Bc||(Bc=zn(t,e,[n[0],n[0]],{duration:1e4,ease:"linear"}),Rc.set(r,{animation:Bc,startTime:null}),window.MotionHandoffAnimation=Lc,window.MotionHasOptimisedAnimation=(t,e)=>{if(!t)return!1;if(!e)return Dc.has(t);const n=kc(t,e);return Boolean(Rc.get(n))},window.MotionHandoffMarkAsComplete=t=>{Dc.has(t)&&Dc.set(t,!0)},window.MotionHandoffIsComplete=t=>!0===Dc.get(t),window.MotionCancelOptimisedAnimation=(t,e,n,i)=>{const s=kc(t,e),o=Rc.get(s);o&&(n&&void 0===i?n.postRender((()=>{n.postRender((()=>{o.animation.cancel()}))})):o.animation.cancel(),n&&i?(Oc.add(o),n.render(Fc)):(Rc.delete(s),Rc.size||(window.MotionCancelOptimisedAnimation=void 0)))},window.MotionCheckAppearSync=(t,e,n)=>{const i=Zs(t);if(!i)return;const s=window.MotionHasOptimisedAnimation?.(i,e),o=t.props.values?.[e];if(!s||!o)return;const r=n.on("change",(t=>{o.get()!==t&&(window.MotionCancelOptimisedAnimation?.(i,e),r())}));return r});const a=()=>{Bc.cancel();const o=zn(t,e,n,i);void 0===jc&&(jc=performance.now()),o.startTime=jc,Rc.set(r,{animation:o,startTime:jc}),s&&s(o)};Dc.set(o,!1),Bc.ready?Bc.ready.then(a).catch(I):a()},t.startWaapiAnimation=zn,t.statsBuffer=mt,t.steps=function(t,e="end"){return n=>{const i=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,s="end"===e?Math.floor(i):Math.ceil(i);return D(0,1,s/t)}},t.styleEffect=function(t,e){const n=Di(t),i=[];for(let t=0;t<n.length;t++){const s=n[t],o=Ii.get(s)??new Oi;Ii.set(s,o);for(const t in e){const n=Ui(s,o,t,e[t]);i.push(n)}}return()=>{for(const t of i)t()}},t.supportedWaapiEasing=Nn,t.supportsBrowserAnimation=Qn,t.supportsFlags=Fn,t.supportsLinearEasing=Un,t.supportsPartialKeyframes=Ci,t.supportsScrollTimeline=On,t.sync=Rs,t.testValueType=di,t.time=Pt,t.transform=cs,t.transformPropOrder=wn,t.transformProps=Tn,t.transformValue=hs,t.transformValueTypes=wi,t.unwrapMotionComponent=function(t){if(Mc(t))return t[bl]},t.useAnimate=function(){const t=f((()=>({current:null,animations:[]}))),e=f((()=>Cu(t)));return lc((()=>{t.animations.forEach((t=>t.stop()))})),[t,e]},t.useAnimateMini=function(){const t=f((()=>({current:null,animations:[]}))),e=f((()=>Du(t)));return lc((()=>{t.animations.forEach((t=>t.stop()))})),[t,e]},t.useAnimation=bc,t.useAnimationControls=Sc,t.useAnimationFrame=vc,t.useCycle=function(...t){const n=e.useRef(0),[i,s]=e.useState(t[n.current]),o=e.useCallback((e=>{n.current="number"!=typeof e?K(0,t.length,n.current+1):e,s(t[n.current])}),[t.length,...t]);return[i,o]},t.useDeprecatedAnimatedState=function(t){const[n,i]=e.useState(t),s=Wc({},!1),o=f((()=>new Uc({props:{onUpdate:t=>{i({...t})}},visualState:s,presenceContext:null},{initialState:t})));return e.useLayoutEffect((()=>(o.mount({}),()=>o.unmount())),[o]),[n,f((()=>t=>ba(o,t)))]},t.useDeprecatedInvertedScale=function(t){let n=iu(1),i=iu(1);const{visualElement:s}=e.useContext(Tl);return t?(n=t.scaleX||n,i=t.scaleY||i):s&&(n=s.getValue("scaleX",1),i=s.getValue("scaleY",1)),{scaleX:ou(n,$c),scaleY:ou(i,$c)}},t.useDomEvent=function(t,n,i,s){e.useEffect((()=>{const e=t.current;if(i&&e)return Pr(e,n,i,s)}),[t,n,i,s])},t.useDragControls=function(){return f(Ac)},t.useElementScroll=function(t){return fc({container:t})},t.useForceUpdate=na,t.useInView=function(t,{root:n,margin:i,amount:s,once:o=!1,initial:r=!1}={}){const[a,l]=e.useState(r);return e.useEffect((()=>{if(!t.current||o&&a)return;const e={root:n&&n.current||void 0,margin:i,amount:s};return rc(t.current,(()=>(l(!0),o?void 0:()=>l(!1))),e)}),[n,t,i,o,s]),a},t.useInstantLayoutTransition=Vc,t.useInstantTransition=function(){const[t,n]=na(),i=Vc(),s=e.useRef(-1);return e.useEffect((()=>{gt.postRender((()=>gt.postRender((()=>{n===s.current&&(j.instantAnimations=!1)}))))}),[n]),e=>{i((()=>{j.instantAnimations=!0,t(),e(),s.current=n+1}))}},t.useIsPresent=function(){return null===(t=e.useContext(v))||t.isPresent;var t},t.useIsomorphicLayoutEffect=y,t.useMotionTemplate=function(t,...e){const n=t.length;return su(e.filter(Ws),(function(){let i="";for(let s=0;s<n;s++){i+=t[s];const n=e[s];n&&(i+=Ws(n)?n.get():n)}return i}))},t.useMotionValue=iu,t.useMotionValueEvent=dc,t.usePresence=b,t.usePresenceData=function(){const t=e.useContext(v);return t?t.custom:void 0},t.useReducedMotion=wc,t.useReducedMotionConfig=function(){const t=wc(),{reducedMotion:n}=e.useContext(x);return"never"!==n&&("always"===n||t)},t.useResetProjection=function(){return e.useCallback((()=>{const t=br.current;t&&t.resetTree()}),[])},t.useScroll=fc,t.useSpring=function(t,n={}){const{isStatic:i}=e.useContext(x),s=e.useRef(null),o=f((()=>Ws(t)?t.get():t)),r=f((()=>"string"==typeof o?o.replace(/[\d.-]/g,""):void 0)),a=iu(o),l=e.useRef(o),u=e.useRef(I),c=()=>{h(),s.current=new an({keyframes:[yc(a.get()),yc(l.current)],velocity:a.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:u.current})},h=()=>{s.current&&s.current.stop()};return e.useInsertionEffect((()=>a.attach(((t,e)=>i?e(t):(l.current=t,u.current=t=>e(gc(t,r)),gt.postRender(c),a.get())),h)),[JSON.stringify(n)]),y((()=>{if(Ws(t))return t.on("change",(t=>a.set(gc(t,r))))}),[a,r]),a},t.useTime=function(){const t=iu(0);return vc((e=>t.set(e))),t},t.useTransform=ou,t.useUnmountEffect=lc,t.useVelocity=function(t){const e=iu(t.getVelocity()),n=()=>{const i=t.getVelocity();e.set(i),i&&gt.update(n)};return dc(t,"change",(()=>{gt.update(n,!1,!0)})),e},t.useViewportScroll=function(){return fc()},t.useWillChange=function(){return f((()=>new xc("auto")))},t.velocityPerSecond=X,t.vh=Ht,t.visualElementStore=Or,t.vw=Xt,t.warnOnce=function(t,e,n){t||Y.has(e)||(console.warn(e),n&&console.warn(n),Y.add(e))},t.warning=k,t.wrap=K}));
