/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      /* ── fonts you already had ───────────────────────────── */
      fontFamily: {
        sans: ['var(--font-inter)'],
        inknut: ['var(--font-inknut-antiqua)'],
      },

      /* ── new utilities for the backdrop effect ───────────── */
      blur: {
        120: '120px',
        160: '160px'   // adding larger blur value
      },

      colors: {
        // Original Kairos colors
        kairosBlue:   '#3f6bfd',
        kairosYellow: '#fff68d',
        kairosGreen:  '#46fcb0',
        kairosBg:     '#0f0f10',

        // YOUR BEAUTIFUL COLOR PALETTE - Now easily accessible!
        kairos: {
          red:    '#f2e9e4',  // Primary brand color
          beige:  '#c9ada7',  // Secondary warm tone
          purple: '#9a8c98',  // Accent purple-gray
          blue:   '#4a4e69',  // Deep blue-gray
          dark:   '#22223b',  // Darkest tone
        },

        // Theme-aware colors using CSS variables
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        primary: 'var(--primary)',
        secondary: 'var(--secondary)',
        accent: 'var(--accent)',
        muted: 'var(--muted)',
        border: 'var(--border)',

        // Direct color access for creative use
        'kairos-red': '#f2e9e4',
        'kairos-beige': '#c9ada7',
        'kairos-purple': '#9a8c98',
        'kairos-blue': '#4a4e69',
        'kairos-dark': '#22223b',
      },

      keyframes: {
        blob: {
          '0%,100%': { transform: 'translate(0,0) scale(1)' },
          '33%':     { transform: 'translate(40px,-60px) scale(1.05)' },
          '66%':     { transform: 'translate(-30px,40px) scale(.95)' },
        },
        'gradient-shift': {
          '0%, 100%': {
            'background-position': '0% 50%'
          },
          '50%': {
            'background-position': '100% 50%'
          },
        },
        'color-cycle': {
          '0%': { color: '#f2e9e4' },
          '25%': { color: '#c9ada7' },
          '50%': { color: '#9a8c98' },
          '75%': { color: '#4a4e69' },
          '100%': { color: '#f2e9e4' },
        },
        'border-dance': {
          '0%, 100%': { 'border-color': '#f2e9e4' },
          '25%': { 'border-color': '#c9ada7' },
          '50%': { 'border-color': '#9a8c98' },
          '75%': { 'border-color': '#4a4e69' },
        },
      },
      animation: {
        blob: 'blob 22s ease-in-out infinite',
        'gradient-shift': 'gradient-shift 6s ease-in-out infinite',
        'color-cycle': 'color-cycle 8s ease-in-out infinite',
        'border-dance': 'border-dance 4s ease-in-out infinite',
      },
      // Removed gradient backgrounds - using solid colors instead
      textShadow: {
        'glow': '0 0 15px rgba(255, 255, 255, 0.5)',
        'subtle': '0 2px 4px rgba(0, 0, 0, 0.3)',
      },
    },
  },
  plugins: [require('@tailwindcss/typography'),
    function ({ addUtilities }) {
      const newUtilities = {
        '.text-shadow-glow': {
          textShadow: '0 0 15px rgba(255, 255, 255, 0.5)',
        },
        '.text-shadow-subtle': {
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
        },
      }
      addUtilities(newUtilities)
    }
  ],
};


