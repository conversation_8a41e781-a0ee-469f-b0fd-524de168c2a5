import{t,M as e,u as i,v as s,w as n,x as o,y as r,z as a,A as h,B as l,C as u,D as c,E as d,F as p,d as m,b as f,o as y,m as g,r as v,G as x,c as T,i as P,l as S,f as w,p as A,H as b,g as V,s as E,P as M,L as D,S as C,I as k,a as R,J as L,n as j,K as B,N as F,q as O,h as I,j as U,k as N}from"./size-rollup-dom-max-assets.js";import{jsx as K}from"react/jsx-runtime";import{useContext as $,useId as W,useEffect as z,useCallback as Y,Component as X,Fragment as H}from"react";function G(t,e){-1===t.indexOf(e)&&t.push(e)}function q(t,e){const i=t.indexOf(e);i>-1&&t.splice(i,1)}const _=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Z=t=>/^0[^.\s]+$/u.test(t);function J(t){let e;return()=>(void 0===e&&(e=t()),e)}const Q=t=>t,tt=(t,e)=>i=>e(t(i)),et=(...t)=>t.reduce(tt),it=(t,e,i)=>{const s=e-t;return 0===s?1:(i-t)/s};class st{constructor(){this.subscriptions=[]}add(t){return G(this.subscriptions,t),()=>q(this.subscriptions,t)}notify(t,e,i){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){const s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const nt=t=>1e3*t,ot=t=>t/1e3;function rt(t,e){return e?t*(1e3/e):0}const at=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function ht(t,e,i,s){if(t===e&&i===s)return Q;const n=e=>function(t,e,i,s,n){let o,r,a=0;do{r=e+(i-e)/2,o=at(r,s,n)-t,o>0?i=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,i);return t=>0===t||1===t?t:at(n(t),e,s)}const lt=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ut=t=>e=>1-t(1-e),ct=ht(.33,1.53,.69,.99),dt=ut(ct),pt=lt(dt),mt=t=>(t*=2)<1?.5*dt(t):.5*(2-Math.pow(2,-10*(t-1))),ft=t=>1-Math.sin(Math.acos(t)),yt=ut(ft),gt=lt(ft),vt=ht(.42,0,1,1),xt=ht(0,0,.58,1),Tt=ht(.42,0,.58,1),Pt=t=>Array.isArray(t)&&"number"==typeof t[0],St={linear:Q,easeIn:vt,easeInOut:Tt,easeOut:xt,circIn:ft,circInOut:gt,circOut:yt,backIn:dt,backInOut:pt,backOut:ct,anticipate:mt},wt=t=>{if(Pt(t)){t.length;const[e,i,s,n]=t;return ht(e,i,s,n)}return"string"==typeof t?St[t]:t},{schedule:At,cancel:bt,state:Vt,steps:Et}=t("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:Q,!0);let Mt;function Dt(){Mt=void 0}const Ct={now:()=>(void 0===Mt&&Ct.set(Vt.isProcessing||e.useManualTiming?Vt.timestamp:performance.now()),Mt),set:t=>{Mt=t,queueMicrotask(Dt)}},kt=t=>Math.round(1e5*t)/1e5,Rt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const Lt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,jt=(t,e)=>i=>Boolean("string"==typeof i&&Lt.test(i)&&i.startsWith(t)||e&&!function(t){return null==t}(i)&&Object.prototype.hasOwnProperty.call(i,e)),Bt=(t,e,i)=>s=>{if("string"!=typeof s)return s;const[n,o,r,a]=s.match(Rt);return{[t]:parseFloat(n),[e]:parseFloat(o),[i]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},Ft={...i,transform:t=>Math.round((t=>n(0,255,t))(t))},Ot={test:jt("rgb","red"),parse:Bt("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+Ft.transform(t)+", "+Ft.transform(e)+", "+Ft.transform(i)+", "+kt(s.transform(n))+")"};const It={test:jt("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:Ot.transform},Ut={test:jt("hsl","hue"),parse:Bt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+o.transform(kt(e))+", "+o.transform(kt(i))+", "+kt(s.transform(n))+")"},Nt={test:t=>Ot.test(t)||It.test(t)||Ut.test(t),parse:t=>Ot.test(t)?Ot.parse(t):Ut.test(t)?Ut.parse(t):It.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Ot.transform(t):Ut.transform(t)},Kt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const $t="number",Wt="color",zt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Yt(t){const e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[];let o=0;const r=e.replace(zt,(t=>(Nt.test(t)?(s.color.push(o),n.push(Wt),i.push(Nt.parse(t))):t.startsWith("var(")?(s.var.push(o),n.push("var"),i.push(t)):(s.number.push(o),n.push($t),i.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:i,split:r,indexes:s,types:n}}function Xt(t){return Yt(t).values}function Ht(t){const{split:e,types:i}=Yt(t),s=e.length;return t=>{let n="";for(let o=0;o<s;o++)if(n+=e[o],void 0!==t[o]){const e=i[o];n+=e===$t?kt(t[o]):e===Wt?Nt.transform(t[o]):t[o]}return n}}const Gt=t=>"number"==typeof t?0:t;const qt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Rt)?.length||0)+(t.match(Kt)?.length||0)>0},parse:Xt,createTransformer:Ht,getAnimatableNone:function(t){const e=Xt(t);return Ht(t)(e.map(Gt))}};function _t(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function Zt(t,e){return i=>i>0?e:t}const Jt=(t,e,i)=>t+(e-t)*i,Qt=(t,e,i)=>{const s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},te=[It,Ot,Ut];function ee(t){const e=(i=t,te.find((t=>t.test(i))));var i;if(!Boolean(e))return!1;let s=e.parse(t);return e===Ut&&(s=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,o=0,r=0;if(e/=100){const s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;n=_t(a,s,t+1/3),o=_t(a,s,t),r=_t(a,s,t-1/3)}else n=o=r=i;return{red:Math.round(255*n),green:Math.round(255*o),blue:Math.round(255*r),alpha:s}}(s)),s}const ie=(t,e)=>{const i=ee(t),s=ee(e);if(!i||!s)return Zt(t,e);const n={...i};return t=>(n.red=Qt(i.red,s.red,t),n.green=Qt(i.green,s.green,t),n.blue=Qt(i.blue,s.blue,t),n.alpha=Jt(i.alpha,s.alpha,t),Ot.transform(n))},se=new Set(["none","hidden"]);function ne(t,e){return i=>Jt(t,e,i)}function oe(t){return"number"==typeof t?ne:"string"==typeof t?r(t)?Zt:Nt.test(t)?ie:he:Array.isArray(t)?re:"object"==typeof t?Nt.test(t)?ie:ae:Zt}function re(t,e){const i=[...t],s=i.length,n=t.map(((t,i)=>oe(t)(t,e[i])));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function ae(t,e){const i={...t,...e},s={};for(const n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=oe(t[n])(t[n],e[n]));return t=>{for(const e in s)i[e]=s[e](t);return i}}const he=(t,e)=>{const i=qt.createTransformer(e),s=Yt(t),n=Yt(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?se.has(t)&&!n.values.length||se.has(e)&&!s.values.length?function(t,e){return se.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):et(re(function(t,e){const i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){const o=e.types[n],r=t.indexes[o][s[o]],a=t.values[r]??0;i[n]=a,s[o]++}return i}(s,n),n.values),i):Zt(t,e)};function le(t,e,i){if("number"==typeof t&&"number"==typeof e&&"number"==typeof i)return Jt(t,e,i);return oe(t)(t,e)}const ue=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>At.update(e,t),stop:()=>bt(e),now:()=>Vt.isProcessing?Vt.timestamp:Ct.now()}},ce=(t,e,i=10)=>{let s="";const n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(e/(n-1))+", ";return`linear(${s.substring(0,s.length-2)})`},de=2e4;function pe(t){let e=0;let i=t.next(e);for(;!i.done&&e<de;)e+=50,i=t.next(e);return e>=de?1/0:e}function me(t,e,i){const s=Math.max(e-5,0);return rt(i-t(s),e-s)}const fe=100,ye=10,ge=1,ve=0,xe=800,Te=.3,Pe=.3,Se={granular:.01,default:2},we={granular:.005,default:.5},Ae=.01,be=10,Ve=.05,Ee=1,Me=.001;function De({duration:t=xe,bounce:e=Te,velocity:i=ve,mass:s=ge}){let o,r,a=1-e;a=n(Ve,Ee,a),t=n(Ae,be,ot(t)),a<1?(o=e=>{const s=e*a,n=s*t,o=s-i,r=ke(e,a),h=Math.exp(-n);return Me-o/r*h},r=e=>{const s=e*a*t,n=s*i+i,r=Math.pow(a,2)*Math.pow(e,2)*t,h=Math.exp(-s),l=ke(Math.pow(e,2),a);return(-o(e)+Me>0?-1:1)*((n-r)*h)/l}):(o=e=>Math.exp(-e*t)*((e-i)*t+1)-.001,r=e=>Math.exp(-e*t)*(t*t*(i-e)));const h=function(t,e,i){let s=i;for(let i=1;i<Ce;i++)s-=t(s)/e(s);return s}(o,r,5/t);if(t=nt(t),isNaN(h))return{stiffness:fe,damping:ye,duration:t};{const e=Math.pow(h,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}const Ce=12;function ke(t,e){return t*Math.sqrt(1-e*e)}const Re=["duration","bounce"],Le=["stiffness","damping","mass"];function je(t,e){return e.some((e=>void 0!==t[e]))}function Be(t=Pe,e=Te){const i="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:o}=i;const r=i.keyframes[0],a=i.keyframes[i.keyframes.length-1],h={done:!1,value:r},{stiffness:l,damping:u,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:ve,stiffness:fe,damping:ye,mass:ge,isResolvedFromDuration:!1,...t};if(!je(t,Le)&&je(t,Re))if(t.visualDuration){const i=t.visualDuration,s=2*Math.PI/(1.2*i),o=s*s,r=2*n(.05,1,1-(t.bounce||0))*Math.sqrt(o);e={...e,mass:ge,stiffness:o,damping:r}}else{const i=De(t);e={...e,...i,mass:ge},e.isResolvedFromDuration=!0}return e}({...i,velocity:-ot(i.velocity||0)}),f=p||0,y=u/(2*Math.sqrt(l*c)),g=a-r,v=ot(Math.sqrt(l/c)),x=Math.abs(g)<5;let T;if(s||(s=x?Se.granular:Se.default),o||(o=x?we.granular:we.default),y<1){const t=ke(v,y);T=e=>{const i=Math.exp(-y*v*e);return a-i*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===y)T=t=>a-Math.exp(-v*t)*(g+(f+v*g)*t);else{const t=v*Math.sqrt(y*y-1);T=e=>{const i=Math.exp(-y*v*e),s=Math.min(t*e,300);return a-i*((f+y*v*g)*Math.sinh(s)+t*g*Math.cosh(s))/t}}const P={calculatedDuration:m&&d||null,next:t=>{const e=T(t);if(m)h.done=t>=d;else{let i=0===t?f:0;y<1&&(i=0===t?nt(f):me(T,t,e));const n=Math.abs(i)<=s,r=Math.abs(a-e)<=o;h.done=n&&r}return h.value=h.done?a:e,h},toString:()=>{const t=Math.min(pe(P),de),e=ce((e=>P.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return P}function Fe({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:h,restDelta:l=.5,restSpeed:u}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?h:void 0===h||Math.abs(a-t)<Math.abs(h-t)?a:h;let m=i*e;const f=c+m,y=void 0===r?f:r(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/s),v=t=>y+g(t),x=t=>{const e=g(t),i=v(t);d.done=Math.abs(e)<=l,d.value=d.done?y:i};let T,P;const S=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==h&&e>h)&&(T=t,P=Be({keyframes:[d.value,p(d.value)],velocity:me(v,t,d.value),damping:n,stiffness:o,restDelta:l,restSpeed:u}))};return S(0),{calculatedDuration:null,next:t=>{let e=!1;return P||void 0!==T||(e=!0,x(t),S(t)),void 0!==T&&t>=T?P.next(t-T):(!e&&x(t),d)}}}function Oe(t,i,{clamp:s=!0,ease:o,mixer:r}={}){const a=t.length;if(i.length,1===a)return()=>i[0];if(2===a&&i[0]===i[1])return()=>i[1];const h=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),i=[...i].reverse());const l=function(t,i,s){const n=[],o=s||e.mix||le,r=t.length-1;for(let e=0;e<r;e++){let s=o(t[e],t[e+1]);if(i){const t=Array.isArray(i)?i[e]||Q:i;s=et(t,s)}n.push(s)}return n}(i,o,r),u=l.length,c=e=>{if(h&&e<t[0])return i[0];let s=0;if(u>1)for(;s<t.length-2&&!(e<t[s+1]);s++);const n=it(t[s],t[s+1],e);return l[s](n)};return s?e=>c(n(t[0],t[a-1],e)):c}function Ie(t){const e=[0];return function(t,e){const i=t[t.length-1];for(let s=1;s<=e;s++){const n=it(0,e,s);t.push(Jt(i,1,n))}}(e,t.length-1),e}function Ue({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){const n=(t=>Array.isArray(t)&&"number"!=typeof t[0])(s)?s.map(wt):wt(s),o={done:!1,value:e[0]},r=function(t,e){return t.map((t=>t*e))}(i&&i.length===e.length?i:Ie(e),t),a=Oe(r,e,{ease:Array.isArray(n)?n:(h=e,l=n,h.map((()=>l||Tt)).splice(0,h.length-1))});var h,l;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}Be.applyToOptions=t=>{const e=function(t,e=100,i){const s=i({...t,keyframes:[0,e]}),n=Math.min(pe(s),de);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:ot(n)}}(t,100,Be);return t.ease=e.ease,t.duration=nt(e.duration),t.type="keyframes",t};const Ne=t=>null!==t;function Ke(t,{repeat:e,repeatType:i="loop"},s,n=1){const o=t.filter(Ne),r=n<0||e&&"loop"!==i&&e%2==1?0:o.length-1;return r&&void 0!==s?s:o[r]}const $e={decay:Fe,inertia:Fe,tween:Ue,keyframes:Ue,spring:Be};function We(t){"string"==typeof t.type&&(t.type=$e[t.type])}class ze{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ye=t=>t/100;class Xe extends ze{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){const{motionValue:t}=this.options;t&&t.updatedAt!==Ct.now()&&this.tick(Ct.now())}if(this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:e}=this.options;e&&e()},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;We(t);const{type:e=Ue,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Ue;a!==Ue&&"number"!=typeof r[0]&&(this.mixKeyframes=et(Ye,le(r[0],r[1])),r=[0,100]);const h=a({...t,keyframes:r});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===h.calculatedDuration&&(h.calculatedDuration=pe(h));const{calculatedDuration:l}=h;this.calculatedDuration=l,this.resolvedDuration=l+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=h}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:i,totalDuration:s,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:h}=this;if(null===this.startTime)return i.next(0);const{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let x=this.currentTime,T=i;if(c){const t=Math.min(this.currentTime,s)/a;let e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===d?(i=1-i,p&&(i-=p/a)):"mirror"===d&&(T=r)),x=n(0,1,i)*a}const P=v?{done:!1,value:u[0]}:T.next(x);o&&(P.value=o(P.value));let{done:S}=P;v||null===h||(S=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&S);return w&&m!==Fe&&(P.value=Ke(u,this.options,y,this.speed)),f&&f(P.value),w&&this.finish(),P}then(t,e){return this.finished.then(t,e)}get duration(){return ot(this.calculatedDuration)}get time(){return ot(this.currentTime)}set time(t){t=nt(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(Ct.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=ot(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=ue,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=i??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Ct.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const He=t=>180*t/Math.PI,Ge=t=>{const e=He(Math.atan2(t[1],t[0]));return _e(e)},qe={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Ge,rotateZ:Ge,skewX:t=>He(Math.atan(t[1])),skewY:t=>He(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},_e=t=>((t%=360)<0&&(t+=360),t),Ze=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Je=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Qe={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ze,scaleY:Je,scale:t=>(Ze(t)+Je(t))/2,rotateX:t=>_e(He(Math.atan2(t[6],t[5]))),rotateY:t=>_e(He(Math.atan2(-t[2],t[0]))),rotateZ:Ge,rotate:Ge,skewX:t=>He(Math.atan(t[4])),skewY:t=>He(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ti(t){return t.includes("scale")?1:0}function ei(t,e){if(!t||"none"===t)return ti(e);const i=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,n;if(i)s=Qe,n=i;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=qe,n=e}if(!n)return ti(e);const o=s[e],r=n[1].split(",").map(ii);return"function"==typeof o?o(r):r[o]}function ii(t){return parseFloat(t.trim())}const si=t=>t===i||t===a,ni=new Set(["x","y","z"]),oi=h.filter((t=>!ni.has(t)));const ri={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ei(e,"x"),y:(t,{transform:e})=>ei(e,"y")};ri.translateX=ri.x,ri.translateY=ri.y;const ai=new Set;let hi=!1,li=!1,ui=!1;function ci(){if(li){const t=Array.from(ai).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),i=new Map;e.forEach((t=>{const e=function(t){const e=[];return oi.forEach((i=>{const s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(i.startsWith("scale")?1:0))})),e}(t);e.length&&(i.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=i.get(t);e&&e.forEach((([e,i])=>{t.getValue(e)?.set(i)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}li=!1,hi=!1,ai.forEach((t=>t.complete(ui))),ai.clear()}function di(){ai.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(li=!0)}))}class pi{constructor(t,e,i,s,n,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(ai.add(this),hi||(hi=!0,At.read(di),At.resolveKeyframes(ci))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){const n=s?.get(),o=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){const s=i.readValue(e,o);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=o),s&&void 0===n&&s.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),ai.delete(this)}cancel(){"scheduled"===this.state&&(ai.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const mi=J((()=>void 0!==window.ScrollTimeline)),fi={};function yi(t,e){const i=J(t);return()=>fi[e]??i()}const gi=yi((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),vi=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,xi={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:vi([0,.65,.55,1]),circOut:vi([.55,0,1,.45]),backIn:vi([.31,.01,.66,-.59]),backOut:vi([.33,1.53,.69,.99])};function Ti(t,e){return t?"function"==typeof t?gi()?ce(t,e):"ease-out":Pt(t)?vi(t):Array.isArray(t)?t.map((t=>Ti(t,e)||xi.easeOut)):xi[t]:void 0}function Pi(t,e,i,{delay:s=0,duration:n=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:h}={},l=void 0){const u={[e]:i};h&&(u.offset=h);const c=Ti(a,n);Array.isArray(c)&&(u.easing=c);const d={delay:s,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};l&&(d.pseudoElement=l);return t.animate(u,d)}function Si(t){return"function"==typeof t&&"applyToOptions"in t}class wi extends ze{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(n),this.allowFlatten=o,this.options=t,t.type;const h=function({type:t,...e}){return Si(t)&&gi()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=Pi(e,i,s,h,n),!1===h.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){const t=Ke(s,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return ot(Number(t))}get time(){return ot(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=nt(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&mi()?(this.animation.timeline=t,Q):e(this)}}const Ai={anticipate:mt,backInOut:pt,circInOut:gt};function bi(t){"string"==typeof t.ease&&t.ease in Ai&&(t.ease=Ai[t.ease])}class Vi extends wi{constructor(t){bi(t),We(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:i,onComplete:s,element:n,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new Xe({...o,autoplay:!1}),a=nt(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const Ei=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!qt.test(t)&&"0"!==t||t.startsWith("url(")));const Mi=new Set(["opacity","clipPath","filter","transform"]),Di=J((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));class Ci extends ze{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:o="loop",keyframes:r,name:a,motionValue:h,element:l,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=Ct.now();const c={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:o,name:a,motionValue:h,element:l,...u},d=l?.KeyframeResolver||pi;this.keyframeResolver=new d(r,((t,e,i)=>this.onKeyframesResolved(t,e,c,!i)),a,h,l),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,i,s,n){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:h,isHandoff:l,onUpdate:u}=s;this.resolvedAt=Ct.now(),function(t,e,i,s){const n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=Ei(n,e),a=Ei(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||Si(i))&&s)}(t,o,r,a)||(!e.instantAnimations&&h||u?.(Ke(t,s,i)),t[0]=t[t.length-1],s.duration=0,s.repeat=0);const c={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:i,...s,keyframes:t},d=!l&&function(t){const{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:o,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:h}=e.owner.getProps();return Di()&&i&&Mi.has(i)&&("transform"!==i||!h)&&!a&&!s&&"mirror"!==n&&0!==o&&"inertia"!==r}(c)?new Vi({...c,element:c.motionValue.owner.current}):new Xe(c);d.finished.then((()=>this.notifyFinished())).catch(Q),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(this.keyframeResolver?.resume(),ui=!0,di(),ci(),ui=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const ki=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Ri(t,e,i=1){const[s,n]=function(t){const e=ki.exec(t);if(!e)return[,];const[,i,s,n]=e;return[`--${i??s}`,n]}(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const t=o.trim();return _(t)?parseFloat(t):t}return r(n)?Ri(n,e,i+1):n}function Li(t,e){return t?.[e]??t?.default??t}const ji=new Set(["width","height","top","left","right","bottom",...h]),Bi=t=>e=>e.test(t),Fi=[i,a,o,l,u,c,{test:t=>"auto"===t,parse:t=>t}],Oi=t=>Fi.find(Bi(t));const Ii=new Set(["brightness","contrast","saturate","opacity"]);function Ui(t){const[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=i.match(Rt)||[];if(!s)return t;const n=i.replace(s,"");let o=Ii.has(e)?1:0;return s!==i&&(o*=100),e+"("+o+n+")"}const Ni=/\b([a-z-]*)\(.*?\)/gu,Ki={...qt,getAnimatableNone:t=>{const e=t.match(Ni);return e?e.map(Ui).join(" "):t}},$i={...d,color:Nt,backgroundColor:Nt,outlineColor:Nt,fill:Nt,stroke:Nt,borderColor:Nt,borderTopColor:Nt,borderRightColor:Nt,borderBottomColor:Nt,borderLeftColor:Nt,filter:Ki,WebkitFilter:Ki},Wi=t=>$i[t];function zi(t,e){let i=Wi(t);return i!==Ki&&(i=qt),i.getAnimatableNone?i.getAnimatableNone(e):void 0}const Yi=new Set(["auto","none","0"]);class Xi extends pi{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&(s=s.trim(),r(s))){const n=Ri(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!ji.has(i)||2!==t.length)return;const[s,n]=t,o=Oi(s),a=Oi(n);if(o!==a)if(si(o)&&si(a))for(let e=0;e<t.length;e++){const i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else ri[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++)(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||Z(s)))&&i.push(e);var s;i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){const e=t[n];"string"==typeof e&&!Yi.has(e)&&Yt(e).values.length&&(s=t[n]),n++}if(s&&i)for(const n of e)t[n]=zi(i,s)}(t,i,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ri[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const n=i.length-1,o=i[n];i[n]=ri[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,i])=>{t.getValue(e).set(i)})),this.resolveNoneKeyframes()}}class Hi{constructor(t,e={}){this.version="__VERSION__",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const i=Ct.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=Ct.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new st);const i=this.events[t].add(e);return"change"===t?()=>{i(),At.read((()=>{this.events.change.getSize()||this.stop()}))}:i}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Ct.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return rt(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Gi(t,e){return new Hi(t,e)}const qi={x:!1,y:!1};function _i(){return qi.x||qi.y}function Zi(t,e){const i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const n=i?.[t]??s.querySelectorAll(t);return n?Array.from(n):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function Ji(t){return!("touch"===t.pointerType||_i())}const Qi=(t,e)=>!!e&&(t===e||Qi(t,e.parentElement)),ts=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,es=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const is=new WeakSet;function ss(t){return e=>{"Enter"===e.key&&t(e)}}function ns(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function os(t){return ts(t)&&!_i()}function rs(t,e,i={}){const[s,n,o]=Zi(t,i),r=t=>{const s=t.currentTarget;if(!os(t)||is.has(s))return;is.add(s);const o=e(s,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",h),is.has(s)&&is.delete(s),os(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,s===window||s===document||i.useGlobalTarget||Qi(s,t.target))},h=t=>{r(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",h,n)};return s.forEach((t=>{var e;(i.useGlobalTarget?window:t).addEventListener("pointerdown",r,n),t instanceof HTMLElement&&(t.addEventListener("focus",(t=>((t,e)=>{const i=t.currentTarget;if(!i)return;const s=ss((()=>{if(is.has(i))return;ns(i,"down");const t=ss((()=>{ns(i,"up")}));i.addEventListener("keyup",t,e),i.addEventListener("blur",(()=>ns(i,"cancel")),e)}));i.addEventListener("keydown",s,e),i.addEventListener("blur",(()=>i.removeEventListener("keydown",s)),e)})(t,n))),e=t,es.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),o}const as=[...Fi,Nt,qt];class hs{constructor(t){this.isMounted=!1,this.node=t}update(){}}const ls=t=>null!==t;const us={type:"spring",stiffness:500,damping:25,restSpeed:10},cs={type:"keyframes",duration:.8},ds={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ps=(t,{keyframes:e})=>e.length>2?cs:p.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:us:ds;const ms=(t,i,s,n={},o,r)=>a=>{const h=Li(n,t)||{},l=h.delay||n.delay||0;let{elapsed:u=0}=n;u-=nt(l);const c={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:i.getVelocity(),...h,delay:-u,onUpdate:t=>{i.set(t),h.onUpdate&&h.onUpdate(t)},onComplete:()=>{a(),h.onComplete&&h.onComplete()},name:t,motionValue:i,element:r?void 0:o};(function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:o,repeatType:r,repeatDelay:a,from:h,elapsed:l,...u}){return!!Object.keys(u).length})(h)||Object.assign(c,ps(t,c)),c.duration&&(c.duration=nt(c.duration)),c.repeatDelay&&(c.repeatDelay=nt(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(d=!0)),(e.instantAnimations||e.skipAnimations)&&(d=!0,c.duration=0,c.delay=0),c.allowFlatten=!h.type&&!h.ease,d&&!r&&void 0!==i.get()){const t=function(t,{repeat:e,repeatType:i="loop"},s){const n=t.filter(ls),o=e&&"loop"!==i&&e%2==1?0:n.length-1;return o&&void 0!==s?s:n[o]}(c.keyframes,h);if(void 0!==t)return void At.update((()=>{c.onUpdate(t),c.onComplete()}))}return h.isSync?new Xe(c):new Ci(c)};function fs(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}function ys(t){return{point:{x:t.pageX,y:t.pageY}}}function gs(t,e,i,s){return fs(t,e,(t=>e=>ts(e)&&t(e,ys(e)))(i),s)}function vs({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function xs(t){return t.max-t.min}function Ts(t,e,i,s=.5){t.origin=s,t.originPoint=Jt(e.min,e.max,t.origin),t.scale=xs(i)/xs(e),t.translate=Jt(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Ps(t,e,i,s){Ts(t.x,e.x,i.x,s?s.originX:void 0),Ts(t.y,e.y,i.y,s?s.originY:void 0)}function Ss(t,e,i){t.min=i.min+e.min,t.max=t.min+xs(e)}function ws(t,e,i){t.min=e.min-i.min,t.max=t.min+xs(e)}function As(t,e,i){ws(t.x,e.x,i.x),ws(t.y,e.y,i.y)}const bs=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Vs(t){return[t("x"),t("y")]}function Es(t){return void 0===t||1===t}function Ms({scale:t,scaleX:e,scaleY:i}){return!Es(t)||!Es(e)||!Es(i)}function Ds(t){return Ms(t)||Cs(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Cs(t){return ks(t.x)||ks(t.y)}function ks(t){return t&&"0%"!==t}function Rs(t,e,i){return i+e*(t-i)}function Ls(t,e,i,s,n){return void 0!==n&&(t=Rs(t,n,s)),Rs(t,i,s)+e}function js(t,e=0,i=1,s,n){t.min=Ls(t.min,e,i,s,n),t.max=Ls(t.max,e,i,s,n)}function Bs(t,{x:e,y:i}){js(t.x,e.translate,e.scale,e.originPoint),js(t.y,i.translate,i.scale,i.originPoint)}const Fs=.999999999999,Os=1.0000000000001;function Is(t,e){t.min=t.min+e,t.max=t.max+e}function Us(t,e,i,s,n=.5){js(t,e,i,Jt(t.min,t.max,n),s)}function Ns(t,e){Us(t.x,e.x,e.scaleX,e.scale,e.originX),Us(t.y,e.y,e.scaleY,e.scale,e.originY)}function Ks(t,e){return vs(function(t,e){if(!e)return t;const i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}const $s=({current:t})=>t?t.ownerDocument.defaultView:null;function Ws(t,i){const s=t.getValue("willChange");if(n=s,Boolean(m(n)&&n.add))return s.add(i);if(!s&&e.WillChange){const s=new e.WillChange("auto");t.addValue("willChange",s),s.add(i)}var n}const zs=(t,e)=>Math.abs(t-e);class Ys{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Gs(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){const i=zs(t.x,e.x),s=zs(t.y,e.y);return Math.sqrt(i**2+s**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;const{point:s}=t,{timestamp:n}=Vt;this.history.push({...s,timestamp:n});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Xs(e,this.transformPagePoint),At.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Gs("pointercancel"===t.type?this.lastMoveEventInfo:Xs(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,o),s&&s(t,o)},!ts(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;const o=Xs(ys(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=Vt;this.history=[{...r,timestamp:a}];const{onSessionStart:h}=e;h&&h(t,Gs(o,this.history)),this.removeListeners=et(gs(this.contextWindow,"pointermove",this.handlePointerMove),gs(this.contextWindow,"pointerup",this.handlePointerUp),gs(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),bt(this.updatePoint)}}function Xs(t,e){return e?{point:e(t.point)}:t}function Hs(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Gs({point:t},e){return{point:t,delta:Hs(t,_s(e)),offset:Hs(t,qs(e)),velocity:Zs(e,.1)}}function qs(t){return t[0]}function _s(t){return t[t.length-1]}function Zs(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null;const n=_s(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>nt(e)));)i--;if(!s)return{x:0,y:0};const o=ot(n.timestamp-s.timestamp);if(0===o)return{x:0,y:0};const r={x:(n.x-s.x)/o,y:(n.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Js(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function Qs(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}const tn=.35;function en(t,e,i){return{min:sn(t,e),max:sn(t,i)}}function sn(t,e){return"number"==typeof t?t:t[e]||0}const nn=new WeakMap;class on{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;const{dragSnapToOrigin:s}=this.getProps();this.panSession=new Ys(t,{onSessionStart:t=>{const{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ys(t).point)},onStart:(t,e)=>{const{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(r=i)||"y"===r?qi[r]?null:(qi[r]=!0,()=>{qi[r]=!1}):qi.x||qi.y?null:(qi.x=qi.y=!0,()=>{qi.x=qi.y=!1}),!this.openDragLock))return;var r;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Vs((t=>{let e=this.getAxisMotionValue(t).get()||0;if(o.test(e)){const{projection:i}=this.visualElement;if(i&&i.layout){const s=i.layout.layoutBox[t];if(s){e=xs(s)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),n&&At.postRender((()=>n(t,e))),Ws(this.visualElement,"transform");const{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:o}=this.getProps();if(!i&&!this.openDragLock)return;const{offset:r}=e;if(s&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let i=null;Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x");return i}(r),void(null!==this.currentDirection&&n&&n(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Vs((t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:$s(this.visualElement)})}stop(t,e){const i=this.isDragging;if(this.cancel(),!i)return;const{velocity:s}=e;this.startAnimation(s);const{onDragEnd:n}=this.getProps();n&&At.postRender((()=>n(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){const{drag:s}=this.getProps();if(!i||!rn(t,s,this.currentDirection))return;const n=this.getAxisMotionValue(t);let o=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?Jt(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?Jt(i,t,s.max):Math.min(t,i)),t}(o,this.constraints[t],this.elastic[t])),n.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&f(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!i)&&function(t,{top:e,left:i,bottom:s,right:n}){return{x:Js(t.x,i,n),y:Js(t.y,e,s)}}(i.layoutBox,t),this.elastic=function(t=tn){return!1===t?t=0:!0===t&&(t=tn),{x:en(t,"left","right"),y:en(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Vs((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!f(t))return!1;const i=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const n=function(t,e,i){const s=Ks(t,i),{scroll:n}=e;return n&&(Is(s.x,n.offset.x),Is(s.y,n.offset.y)),s}(i,s.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Qs(t.x,e.x),y:Qs(t.y,e.y)}}(s.layout.layoutBox,n);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=vs(t))}return o}startAnimation(t){const{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},h=Vs((r=>{if(!rn(r,e,this.currentDirection))return;let h=a&&a[r]||{};o&&(h={min:0,max:0});const l=s?200:1e6,u=s?40:1e7,c={type:"inertia",velocity:i?t[r]:0,bounceStiffness:l,bounceDamping:u,timeConstant:750,restDelta:1,restSpeed:10,...n,...h};return this.startAxisValueAnimation(r,c)}));return Promise.all(h).then(r)}startAxisValueAnimation(t,e){const i=this.getAxisMotionValue(t);return Ws(this.visualElement,t),i.start(ms(t,i,0,e,this.visualElement,!1))}stopAnimation(){Vs((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){Vs((t=>this.getAxisMotionValue(t).animation?.pause()))}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps(),s=i[e];return s||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){Vs((e=>{const{drag:i}=this.getProps();if(!rn(e,i,this.currentDirection))return;const{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){const{min:i,max:o}=s.layout.layoutBox[e];n.set(t[e]-Jt(i,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!f(e)||!i||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};Vs((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const i=e.get();s[t]=function(t,e){let i=.5;const s=xs(t),o=xs(e);return o>s?i=it(e.min,e.max-s,t.min):s>o&&(i=it(t.min,t.max-o,e.min)),n(0,1,i)}({min:i,max:i},this.constraints[t])}}));const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),Vs((e=>{if(!rn(e,t,null))return;const i=this.getAxisMotionValue(e),{min:n,max:o}=this.constraints[e];i.set(Jt(n,o,s[e]))}))}addListeners(){if(!this.visualElement.current)return;nn.set(this.visualElement,this);const t=gs(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();f(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),At.read(e);const n=fs(window,"resize",(()=>this.scalePositionWithinConstraints())),o=i.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Vs((e=>{const i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))})),this.visualElement.render())}));return()=>{n(),t(),s(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:o=tn,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:o,dragMomentum:r}}}function rn(t,e,i){return!(!0!==e&&e!==t||null!==i&&i!==t)}const an=t=>(e,i)=>{t&&At.postRender((()=>t(e,i)))};function hn(t){return t.props[y]}const ln=(t,e)=>t.depth-e.depth;class un{constructor(){this.children=[],this.isDirty=!1}add(t){G(this.children,t),this.isDirty=!0}remove(t){q(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(ln),this.isDirty=!1,this.children.forEach(t)}}const cn=["TopLeft","TopRight","BottomLeft","BottomRight"],dn=cn.length,pn=t=>"string"==typeof t?parseFloat(t):t,mn=t=>"number"==typeof t||a.test(t);function fn(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const yn=vn(0,.5,yt),gn=vn(.5,.95,Q);function vn(t,e,i){return s=>s<t?0:s>e?1:i(it(t,e,s))}function xn(t,e){t.min=e.min,t.max=e.max}function Tn(t,e){xn(t.x,e.x),xn(t.y,e.y)}function Pn(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Sn(t,e,i,s,n){return t=Rs(t-=e,1/i,s),void 0!==n&&(t=Rs(t,1/n,s)),t}function wn(t,e,[i,s,n],r,a){!function(t,e=0,i=1,s=.5,n,r=t,a=t){o.test(e)&&(e=parseFloat(e),e=Jt(a.min,a.max,e/100)-a.min);if("number"!=typeof e)return;let h=Jt(r.min,r.max,s);t===r&&(h-=e),t.min=Sn(t.min,e,i,h,n),t.max=Sn(t.max,e,i,h,n)}(t,e[i],e[s],e[n],e.scale,r,a)}const An=["x","scaleX","originX"],bn=["y","scaleY","originY"];function Vn(t,e,i,s){wn(t.x,e,An,i?i.x:void 0,s?s.x:void 0),wn(t.y,e,bn,i?i.y:void 0,s?s.y:void 0)}function En(t){return 0===t.translate&&1===t.scale}function Mn(t){return En(t.x)&&En(t.y)}function Dn(t,e){return t.min===e.min&&t.max===e.max}function Cn(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function kn(t,e){return Cn(t.x,e.x)&&Cn(t.y,e.y)}function Rn(t){return xs(t.x)/xs(t.y)}function Ln(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class jn{constructor(){this.members=[]}add(t){G(this.members,t),t.scheduleRender()}remove(t){if(q(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let i;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){i=e;break}}return!!i&&(this.promote(i),!0)}promote(t,e){const i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const Bn={hasAnimatedSinceResize:!0,hasEverUpdated:!1},Fn=["","X","Y","Z"],On={visibility:"hidden"};let In=0;function Un(t,e,i,s){const{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function Nn(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const i=hn(e);if(window.MotionHasOptimisedAnimation(i,"transform")){const{layout:e,layoutId:s}=t.options;window.MotionCancelOptimisedAnimation(i,"transform",At,!(e||s))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&Nn(s)}function Kn({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=In++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(zn),this.nodes.forEach(Zn),this.nodes.forEach(Jn),this.nodes.forEach(Yn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new un)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new st),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var i;this.isSVG=(i=e)instanceof SVGElement&&"svg"!==i.tagName,this.instance=e;const{layoutId:s,layout:n,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||s)&&(this.isLayoutDirty=!0),t){let i;const s=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){const i=Ct.now(),s=({timestamp:n})=>{const o=n-i;o>=e&&(bt(s),t(o-e))};return At.setup(s,!0),()=>bt(s)}(s,250),Bn.hasAnimatedSinceResize&&(Bn.hasAnimatedSinceResize=!1,this.nodes.forEach(_n))}))}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&o&&(s||n)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const n=this.options.transition||o.getDefaultTransition()||no,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),h=!this.targetLayout||!kn(this.targetLayout,s),l=!e&&i;if(this.options.layoutRoot||this.resumeFrom||l||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,l);const e={...Li(n,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||_n(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),bt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Qn),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Nn(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;const s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Hn);this.isUpdating||this.nodes.forEach(Gn),this.isUpdating=!1,this.nodes.forEach(qn),this.nodes.forEach($n),this.nodes.forEach(Wn),this.clearAllSnapshots();const t=Ct.now();Vt.delta=n(0,1e3/60,t-Vt.timestamp),Vt.timestamp=t,Vt.isProcessing=!0,Et.update.process(Vt),Et.preRender.process(Vt),Et.render.process(Vt),Vt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,g.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Xn),this.sharedNodes.forEach(to)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,At.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){At.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||xs(this.snapshot.measuredBox.x)||xs(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!Mn(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||Ds(this.latestValues)||n)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let i=this.removeElementScroll(e);var s;return t&&(i=this.removeTransform(i)),ao((s=i).x),ao(s.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(lo))){const{scroll:t}=this.root;t&&(Is(e.x,t.offset.x),Is(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(Tn(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){const s=this.path[i],{scroll:n,options:o}=s;s!==this.root&&n&&o.layoutScroll&&(n.wasRoot&&Tn(e,t),Is(e.x,n.offset.x),Is(e.y,n.offset.y))}return e}applyTransform(t,e=!1){const i={x:{min:0,max:0},y:{min:0,max:0}};Tn(i,t);for(let t=0;t<this.path.length;t++){const s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&Ns(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),Ds(s.latestValues)&&Ns(i,s.latestValues)}return Ds(this.latestValues)&&Ns(i,this.latestValues),i}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Tn(e,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];if(!i.instance)continue;if(!Ds(i.latestValues))continue;Ms(i.latestValues)&&i.updateSnapshot();const s={x:{min:0,max:0},y:{min:0,max:0}};Tn(s,i.measurePageBox()),Vn(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return Ds(this.latestValues)&&Vn(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Vt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=Vt.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},As(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Tn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var o,r,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,Ss(o.x,r.x,a.x),Ss(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Tn(this.target,this.layout.layoutBox),Bs(this.target,this.targetDelta)):Tn(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},As(this.relativeTargetOrigin,this.target,t.target),Tn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!Ms(this.parent.latestValues)&&!Cs(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===Vt.timestamp&&(i=!1),i)return;const{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!n)return;Tn(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,i,s=!1){const n=i.length;if(!n)return;let o,r;e.x=e.y=1;for(let a=0;a<n;a++){o=i[a],r=o.projectionDelta;const{visualElement:n}=o.options;n&&n.props.style&&"contents"===n.props.style.display||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Ns(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Bs(t,r)),s&&Ds(o.latestValues)&&Ns(t,o.latestValues))}e.x<Os&&e.x>Fs&&(e.x=1),e.y<Os&&e.y>Fs&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(Pn(this.prevProjectionDelta.x,this.projectionDelta.x),Pn(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Ps(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&Ln(this.projectionDelta.x,this.prevProjectionDelta.x)&&Ln(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const i=this.snapshot,s=i?i.latestValues:{},n={...this.latestValues},r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const a={x:{min:0,max:0},y:{min:0,max:0}},h=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(h&&!u&&!0===this.options.crossfade&&!this.path.some(so));let d;this.animationProgress=0,this.mixTargetDelta=e=>{const i=e/1e3;var l,p,m,f,y,g;eo(r.x,t.x,i),eo(r.y,t.y,i),this.setTargetDelta(r),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(As(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,f=this.relativeTargetOrigin,y=a,g=i,io(m.x,f.x,y.x,g),io(m.y,f.y,y.y,g),d&&(l=this.relativeTarget,p=d,Dn(l.x,p.x)&&Dn(l.y,p.y))&&(this.isProjectionDirty=!1),d||(d={x:{min:0,max:0},y:{min:0,max:0}}),Tn(d,this.relativeTarget)),h&&(this.animationValues=n,function(t,e,i,s,n,r){n?(t.opacity=Jt(0,i.opacity??1,yn(s)),t.opacityExit=Jt(e.opacity??1,0,gn(s))):r&&(t.opacity=Jt(e.opacity??1,i.opacity??1,s));for(let n=0;n<dn;n++){const r=`border${cn[n]}Radius`;let a=fn(e,r),h=fn(i,r);void 0===a&&void 0===h||(a||(a=0),h||(h=0),0===a||0===h||mn(a)===mn(h)?(t[r]=Math.max(Jt(pn(a),pn(h),s),0),(o.test(h)||o.test(a))&&(t[r]+="%")):t[r]=h)}(e.rotate||i.rotate)&&(t.rotate=Jt(e.rotate||0,i.rotate||0,s))}(n,s,this.latestValues,i,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(bt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=At.update((()=>{Bn.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Gi(0)),this.currentAnimation=function(t,e,i){const s=m(t)?t:Gi(t);return s.start(ms("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&ho(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=xs(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;const s=xs(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}Tn(e,i),Ns(e,n),Ps(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new jn);this.sharedNodes.get(t).add(e);const i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){const s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;const s={};i.z&&Un("z",t,s,this.animationValues);for(let e=0;e<Fn.length;e++)Un(`rotate${Fn[e]}`,t,s,this.animationValues),Un(`skew${Fn[e]}`,t,s,this.animationValues);t.render();for(const e in s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return On;const e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=v(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;const s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=v(t?.pointerEvents)||""),this.hasProjected&&!Ds(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}const n=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="";const n=t.x.translate/e.x,o=t.y.translate/e.y,r=i?.z||0;if((n||o||r)&&(s=`translate3d(${n}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(s+=`scale(${1/e.x}, ${1/e.y}) `),i){const{transformPerspective:t,rotate:e,rotateX:n,rotateY:o,skewX:r,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),o&&(s+=`rotateY(${o}deg) `),r&&(s+=`skewX(${r}deg) `),a&&(s+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,h=t.y.scale*e.y;return 1===a&&1===h||(s+=`scale(${a}, ${h})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,s.animationValues?e.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0;for(const t in x){if(void 0===n[t])continue;const{correct:i,applyTo:o,isCSSVariable:r}=x[t],a="none"===e.transform?n[t]:i(n[t],s);if(o){const t=o.length;for(let i=0;i<t;i++)e[o[i]]=a}else r?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=s===this?v(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>t.currentAnimation?.stop(!1))),this.root.nodes.forEach(Hn),this.root.sharedNodes.clear()}}}function $n(t){t.updateLayout()}function Wn(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,o=e.source!==t.layout.source;"size"===n?Vs((t=>{const s=o?e.measuredBox[t]:e.layoutBox[t],n=xs(s);s.min=i[t].min,s.max=s.min+n})):ho(n,e.layoutBox,i)&&Vs((s=>{const n=o?e.measuredBox[s]:e.layoutBox[s],r=xs(i[s]);n.max=n.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Ps(r,i,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Ps(a,t.applyTransform(s,!0),e.measuredBox):Ps(a,i,e.layoutBox);const h=!Mn(r);let l=!1;if(!t.resumeFrom){const s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){const{snapshot:n,layout:o}=s;if(n&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};As(r,e.layoutBox,n.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};As(a,i,o.layoutBox),kn(r,a)||(l=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:h,hasRelativeLayoutChanged:l})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function zn(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Yn(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Xn(t){t.clearSnapshot()}function Hn(t){t.clearMeasurements()}function Gn(t){t.isLayoutDirty=!1}function qn(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function _n(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Zn(t){t.resolveTargetDelta()}function Jn(t){t.calcProjection()}function Qn(t){t.resetSkewAndRotation()}function to(t){t.removeLeadSnapshot()}function eo(t,e,i){t.translate=Jt(e.translate,0,i),t.scale=Jt(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function io(t,e,i,s){t.min=Jt(e.min,i.min,s),t.max=Jt(e.max,i.max,s)}function so(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const no={duration:.45,ease:[.4,0,.1,1]},oo=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ro=oo("applewebkit/")&&!oo("chrome/")?Math.round:Q;function ao(t){t.min=ro(t.min),t.max=ro(t.max)}function ho(t,e,i){return"position"===t||"preserve-aspect"===t&&(s=Rn(e),n=Rn(i),o=.2,!(Math.abs(s-n)<=o));var s,n,o}function lo(t){return t!==t.root&&t.scroll?.wasRoot}const uo=Kn({attachResizeListener:(t,e)=>fs(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),co={current:void 0},po=Kn({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!co.current){const t=new uo({});t.mount(window),t.setOptions({layoutScroll:!0}),co.current=t}return co.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function mo(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const fo={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!a.test(t))return t;t=parseFloat(t)}return`${mo(t,e.target.x)}% ${mo(t,e.target.y)}%`}},yo={correct:(t,{treeScale:e,projectionDelta:i})=>{const s=t,n=qt.parse(t);if(n.length>5)return s;const o=qt.createTransformer(t),r="number"!=typeof n[0]?1:0,a=i.x.scale*e.x,h=i.y.scale*e.y;n[0+r]/=a,n[1+r]/=h;const l=Jt(a,h,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),o(n)}},go={current:null},vo={current:!1};const xo=new WeakMap;const To=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Po{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=pi,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=Ct.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,At.render(this.render,!1,!0))};const{latestValues:a,renderState:h}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=h,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=r,this.blockInitialAnimation=Boolean(n),this.isControllingVariants=P(e),this.isVariantNode=S(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in u){const e=u[t];void 0!==a[t]&&m(e)&&e.set(a[t],!1)}}mount(t){this.current=t,xo.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),vo.current||function(){if(vo.current=!0,T)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>go.current=t.matches;t.addListener(e),e()}else go.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||go.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),bt(this.notifyUpdate),bt(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const i=p.has(t);i&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&At.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)})),n=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{s(),n(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in w){const e=w[t];if(!e)continue;const{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<To.length;e++){const i=To[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(const s in e){const n=e[s],o=i[s];if(m(n))t.addValue(s,n);else if(m(o))t.addValue(s,Gi(n,{owner:t}));else if(o!==n)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{const e=t.getStaticValue(s);t.addValue(s,Gi(void 0!==e?e:n,{owner:t}))}}for(const s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=Gi(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=i&&("string"==typeof i&&(_(i)||Z(i))?i=parseFloat(i):(s=i,!as.find(Bi(s))&&qt.test(e)&&(i=zi(t,e))),this.setBaseTarget(t,m(i)?i.get():i)),m(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let i;if("string"==typeof e||"object"==typeof e){const s=A(this.props,e,this.presenceContext?.custom);s&&(i=s[t])}if(e&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||m(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new st),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class So extends Po{constructor(){super(...arguments),this.KeyframeResolver=Xi}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;m(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}function wo(t,{style:e,vars:i},s,n){Object.assign(t.style,e,n&&n.getProjectionStyles(s));for(const e in i)t.style.setProperty(e,i[e])}class Ao extends So{constructor(){super(...arguments),this.type="html",this.renderInstance=wo}readValueFromInstance(t,e){if(p.has(e))return((t,e)=>{const{transform:i="none"}=getComputedStyle(t);return ei(i,e)})(t,e);{const s=(i=t,window.getComputedStyle(i)),n=(b(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof n?n.trim():n}var i}measureInstanceViewportBox(t,{transformPagePoint:e}){return Ks(t,e)}build(t,e,i){V(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return E(t,e,i)}}class bo extends X{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;k(Eo),n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",(()=>{this.safeToRemove()})),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),Bn.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,{projection:o}=i;return o?(o.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?o.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?o.promote():o.relegate()||At.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),g.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Vo(t){const[e,i]=function(t=!0){const e=$(M);if(null===e)return[!0,null];const{isPresent:i,onExitComplete:s,register:n}=e,o=W();z((()=>{if(t)return n(o)}),[t]);const r=Y((()=>t&&s&&s(o)),[o,s,t]);return!i&&s?[!1,r]:[!0]}(),s=$(D);return K(bo,{...t,layoutGroup:s,switchLayoutGroup:$(C),isPresent:e,safeToRemove:i})}const Eo={borderRadius:{...fo,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:fo,borderTopRightRadius:fo,borderBottomLeftRadius:fo,borderBottomRightRadius:fo,boxShadow:yo},Mo={pan:{Feature:class extends hs{constructor(){super(...arguments),this.removePointerDownListener=Q}onPointerDown(t){this.session=new Ys(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:$s(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:an(t),onStart:an(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&At.postRender((()=>s(t,e)))}}}mount(){this.removePointerDownListener=gs(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends hs{constructor(t){super(t),this.removeGroupControls=Q,this.removeListeners=Q,this.controls=new on(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Q}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:po,MeasureLayout:Vo}},Do={layout:{ProjectionNode:po,MeasureLayout:Vo}};function Co(t,e,i){const s=t.getProps();return A(s,e,void 0!==i?i:s.custom,t)}const ko=t=>Array.isArray(t);function Ro(t,e,i){t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,Gi(i))}function Lo({protectedKeys:t,needsAnimating:e},i){const s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}function jo(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;s&&(o=s);const h=[],l=n&&t.animationState&&t.animationState.getState()[n];for(const e in a){const s=t.getValue(e,t.latestValues[e]??null),n=a[e];if(void 0===n||l&&Lo(l,e))continue;const r={delay:i,...Li(o||{},e)},u=s.get();if(void 0!==u&&!s.isAnimating&&!Array.isArray(n)&&n===u&&!r.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const i=hn(t);if(i){const t=window.MotionHandoffAnimation(i,e,At);null!==t&&(r.startTime=t,c=!0)}}Ws(t,e),s.start(ms(e,s,n,t.shouldReduceMotion&&ji.has(e)?{type:!1}:r,t,c));const d=s.animation;d&&h.push(d)}return r&&Promise.all(h).then((()=>{At.update((()=>{r&&function(t,e){const i=Co(t,e);let{transitionEnd:s={},transition:n={},...o}=i||{};o={...o,...s};for(const e in o)Ro(t,e,(r=o[e],ko(r)?r[r.length-1]||0:r));var r}(t,r)}))})),h}function Bo(t,e,i={}){const s=Co(t,e,"exit"===i.type?t.presenceContext?.custom:void 0);let{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);const o=s?()=>Promise.all(jo(t,s,i)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(s=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=n;return function(t,e,i=0,s=0,n=1,o){const r=[],a=(t.variantChildren.size-1)*s,h=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(Fo).forEach(((t,s)=>{t.notify("AnimationStart",e),r.push(Bo(t,e,{...o,delay:i+h(s)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,o+s,r,a,i)}:()=>Promise.resolve(),{when:a}=n;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then((()=>e()))}return Promise.all([o(),r(i.delay)])}function Fo(t,e){return t.sortNodePosition(e)}function Oo(t,e){if(!Array.isArray(e))return!1;const i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}const Io=L.length;function Uo(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Uo(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let i=0;i<Io;i++){const s=L[i],n=t.props[s];(R(n)||!1===n)&&(e[s]=n)}return e}const No=[...B].reverse(),Ko=B.length;function $o(t){return e=>Promise.all(e.map((({animation:e,options:i})=>function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e)){const n=e.map((e=>Bo(t,e,i)));s=Promise.all(n)}else if("string"==typeof e)s=Bo(t,e,i);else{const n="function"==typeof e?Co(t,e,i.custom):e;s=Promise.all(jo(t,n,i))}return s.then((()=>{t.notify("AnimationComplete",e)}))}(t,e,i))))}function Wo(t){let e=$o(t),i=Xo(),s=!0;const n=e=>(i,s)=>{const n=Co(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){const{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function o(o){const{props:r}=t,a=Uo(t.parent)||{},h=[],l=new Set;let u={},c=1/0;for(let e=0;e<Ko;e++){const d=No[e],p=i[d],m=void 0!==r[d]?r[d]:a[d],f=R(m),y=d===o?p.isActive:null;!1===y&&(c=e);let g=m===a[d]&&m!==r[d]&&f;if(g&&s&&t.manuallyAnimateOnMount&&(g=!1),p.protectedKeys={...u},!p.isActive&&null===y||!m&&!p.prevProp||j(m)||"boolean"==typeof m)continue;const v=zo(p.prevProp,m);let x=v||d===o&&p.isActive&&!g&&f||e>c&&f,T=!1;const P=Array.isArray(m)?m:[m];let S=P.reduce(n(d),{});!1===y&&(S={});const{prevResolvedValues:w={}}=p,A={...w,...S},b=e=>{x=!0,l.has(e)&&(T=!0,l.delete(e)),p.needsAnimating[e]=!0;const i=t.getValue(e);i&&(i.liveStyle=!1)};for(const t in A){const e=S[t],i=w[t];if(u.hasOwnProperty(t))continue;let s=!1;s=ko(e)&&ko(i)?!Oo(e,i):e!==i,s?null!=e?b(t):l.add(t):void 0!==e&&l.has(t)?b(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=S,p.isActive&&(u={...u,...S}),s&&t.blockInitialAnimation&&(x=!1);x&&(!(g&&v)||T)&&h.push(...P.map((t=>({animation:t,options:{type:d}}))))}if(l.size){const e={};if("boolean"!=typeof r.initial){const i=Co(t,Array.isArray(r.initial)?r.initial[0]:r.initial);i&&i.transition&&(e.transition=i.transition)}l.forEach((i=>{const s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null})),h.push({animation:e})}let d=Boolean(h.length);return!s||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),s=!1,d?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach((t=>t.animationState?.setActive(e,s))),i[e].isActive=s;const n=o(e);for(const t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=Xo(),s=!0}}}function zo(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Oo(e,t)}function Yo(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Xo(){return{animate:Yo(!0),whileInView:Yo(),whileHover:Yo(),whileTap:Yo(),whileDrag:Yo(),whileFocus:Yo(),exit:Yo()}}let Ho=0;const Go={animation:{Feature:class extends hs{constructor(t){super(t),t.animationState||(t.animationState=Wo(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();j(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends hs{constructor(){super(...arguments),this.id=Ho++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then((()=>{e(this.id)}))}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function qo(t,e,i){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);const n=s["onHover"+i];n&&At.postRender((()=>n(e,ys(e))))}function _o(t,e,i){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);const n=s["onTap"+("End"===i?"":i)];n&&At.postRender((()=>n(e,ys(e))))}const Zo=new WeakMap,Jo=new WeakMap,Qo=t=>{const e=Zo.get(t.target);e&&e(t)},tr=t=>{t.forEach(Qo)};function er(t,e,i){const s=function({root:t,...e}){const i=t||document;Jo.has(i)||Jo.set(i,{});const s=Jo.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(tr,{root:t,...e})),s[n]}(e);return Zo.set(t,i),s.observe(t),()=>{Zo.delete(t),s.unobserve(t)}}const ir={some:0,all:1};const sr={inView:{Feature:class extends hs{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,o={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:ir[s]};return er(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,n&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),o=e?i:s;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends hs{mount(){const{current:t}=this.node;t&&(this.unmount=rs(t,((t,e)=>(_o(this.node,e,"Start"),(t,{success:e})=>_o(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends hs{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=et(fs(this.node.current,"focus",(()=>this.onFocus())),fs(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends hs{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){const[s,n,o]=Zi(t,i),r=t=>{if(!Ji(t))return;const{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;const o=t=>{Ji(t)&&(s(t),i.removeEventListener("pointerleave",o))};i.addEventListener("pointerleave",o,n)};return s.forEach((t=>{t.addEventListener("pointerenter",r,n)})),o}(t,((t,e)=>(qo(this.node,e,"Start"),t=>qo(this.node,t,"End")))))}unmount(){}}}},nr=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class or extends So{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=bs}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(p.has(e)){const t=Wi(e);return t&&t.default||0}return e=nr.has(e)?e:F(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return O(t,e,i)}build(t,e,i){I(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){!function(t,e,i,s){wo(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(nr.has(i)?i:F(i),e.attrs[i])}(t,e,0,s)}mount(t){this.isSVGTag=U(t.tagName),super.mount(t)}}const rr={renderer:(t,e)=>N(t)?new or(e):new Ao(e,{allowProjection:t!==H}),...Go,...sr},ar={...rr,...Mo,...Do};export{ar as domMax};
