const e=(e,t,n)=>n>t?t:n<e?e:n;const t={};function n(e){let t;return()=>(void 0===t&&(t=e()),t)}const r=e=>e,s=(e,t)=>n=>t(e(n)),o=(...e)=>e.reduce(s),i=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r};const a=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function u(e,n){let r=!1,s=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=()=>r=!0,u=a.reduce(((e,t)=>(e[t]=function(e,t){let n=new Set,r=new Set,s=!1,o=!1;const i=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},u=0;function l(t){i.has(t)&&(f.schedule(t),e()),u++,t(a)}const f={schedule:(e,t=!1,o=!1)=>{const a=o&&s?n:r;return t&&i.add(e),a.has(e)||a.add(e),e},cancel:e=>{r.delete(e),i.delete(e)},process:e=>{a=e,s?o=!0:(s=!0,[n,r]=[r,n],n.forEach(l),t&&c.value&&c.value.frameloop[t].push(u),u=0,n.clear(),s=!1,o&&(o=!1,f.process(e)))}};return f}(i,n?t:void 0),e)),{}),{setup:l,read:f,resolveKeyframes:d,preUpdate:h,update:g,preRender:p,render:m,postRender:y}=u,v=()=>{const i=t.useManualTiming?o.timestamp:performance.now();r=!1,t.useManualTiming||(o.delta=s?1e3/60:Math.max(Math.min(i-o.timestamp,40),1)),o.timestamp=i,o.isProcessing=!0,l.process(o),f.process(o),d.process(o),h.process(o),g.process(o),p.process(o),m.process(o),y.process(o),o.isProcessing=!1,r&&n&&(s=!1,e(v))};return{schedule:a.reduce(((t,n)=>{const i=u[n];return t[n]=(t,n=!1,a=!1)=>(r||(r=!0,s=!0,o.isProcessing||e(v)),i.schedule(t,n,a)),t}),{}),cancel:e=>{for(let t=0;t<a.length;t++)u[a[t]].cancel(e)},state:o,steps:u}}const{schedule:l,cancel:f,state:d,steps:h}=u("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:r,!0),g=(e=>t=>"string"==typeof t&&t.startsWith(e))("var(--"),p=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,m={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},y={...m,transform:t=>e(0,1,t)},v=e=>Math.round(1e5*e)/1e5,b=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const w=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,x=(e,t)=>n=>Boolean("string"==typeof n&&w.test(n)&&n.startsWith(e)||t&&!function(e){return null==e}(n)&&Object.prototype.hasOwnProperty.call(n,t)),E=(e,t,n)=>r=>{if("string"!=typeof r)return r;const[s,o,i,a]=r.match(b);return{[e]:parseFloat(s),[t]:parseFloat(o),[n]:parseFloat(i),alpha:void 0!==a?parseFloat(a):1}},W={...m,transform:t=>Math.round((t=>e(0,255,t))(t))},L={test:x("rgb","red"),parse:E("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+W.transform(e)+", "+W.transform(t)+", "+W.transform(n)+", "+v(y.transform(r))+")"};const M={test:x("#"),parse:function(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}},transform:L.transform},S=(e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}))("%"),z={test:x("hsl","hue"),parse:E("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+S.transform(v(t))+", "+S.transform(v(n))+", "+v(y.transform(r))+")"},A=e=>L.test(e)||M.test(e)||z.test(e),B=e=>L.test(e)?L.parse(e):z.test(e)?z.parse(e):M.parse(e),T=e=>"string"==typeof e?e:e.hasOwnProperty("red")?L.transform(e):z.transform(e),H=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const O="number",F="color",P=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function R(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},s=[];let o=0;const i=t.replace(P,(e=>(A(e)?(r.color.push(o),s.push(F),n.push(B(e))):e.startsWith("var(")?(r.var.push(o),s.push("var"),n.push(e)):(r.number.push(o),s.push(O),n.push(parseFloat(e))),++o,"${}"))).split("${}");return{values:n,split:i,indexes:r,types:s}}function $(e){return R(e).values}function N(e){const{split:t,types:n}=R(e),r=t.length;return e=>{let s="";for(let o=0;o<r;o++)if(s+=t[o],void 0!==e[o]){const t=n[o];s+=t===O?v(e[o]):t===F?T(e[o]):e[o]}return s}}const k=e=>"number"==typeof e?0:e;const j={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(b)?.length||0)+(e.match(H)?.length||0)>0},parse:$,createTransformer:N,getAnimatableNone:function(e){const t=$(e);return N(e)(t.map(k))}};function q(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function I(e,t){return n=>n>0?t:e}const U=(e,t,n)=>e+(t-e)*n,G=(e,t,n)=>{const r=e*e,s=n*(t*t-r)+r;return s<0?0:Math.sqrt(s)},C=[M,L,z];function K(e){const t=(n=e,C.find((e=>e.test(n))));var n;if(!Boolean(t))return!1;let r=t.parse(e);return t===z&&(r=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let s=0,o=0,i=0;if(t/=100){const r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;s=q(a,r,e+1/3),o=q(a,r,e),i=q(a,r,e-1/3)}else s=o=i=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*i),alpha:r}}(r)),r}const V=(e,t)=>{const n=K(e),r=K(t);if(!n||!r)return I(e,t);const s={...n};return e=>(s.red=G(n.red,r.red,e),s.green=G(n.green,r.green,e),s.blue=G(n.blue,r.blue,e),s.alpha=U(n.alpha,r.alpha,e),L.transform(s))},D=new Set(["none","hidden"]);function J(e,t){return n=>U(e,t,n)}function Q(e){return"number"==typeof e?J:"string"==typeof e?g(t=e)&&p.test(t.split("/*")[0].trim())?I:A(e)?V:Z:Array.isArray(e)?X:"object"==typeof e?A(e)?V:Y:I;var t}function X(e,t){const n=[...e],r=n.length,s=e.map(((e,n)=>Q(e)(e,t[n])));return e=>{for(let t=0;t<r;t++)n[t]=s[t](e);return n}}function Y(e,t){const n={...e,...t},r={};for(const s in n)void 0!==e[s]&&void 0!==t[s]&&(r[s]=Q(e[s])(e[s],t[s]));return e=>{for(const t in r)n[t]=r[t](e);return n}}const Z=(e,t)=>{const n=j.createTransformer(t),r=R(e),s=R(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?D.has(e)&&!s.values.length||D.has(t)&&!r.values.length?function(e,t){return D.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):o(X(function(e,t){const n=[],r={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const o=t.types[s],i=e.indexes[o][r[o]],a=e.values[i]??0;n[s]=a,r[o]++}return n}(r,s),s.values),n):I(e,t)};function _(e,t,n){if("number"==typeof e&&"number"==typeof t&&"number"==typeof n)return U(e,t,n);return Q(e)(e,t)}function ee(n,s,{clamp:a=!0,ease:c,mixer:u}={}){const l=n.length;if(s.length,1===l)return()=>s[0];if(2===l&&s[0]===s[1])return()=>s[1];const f=n[0]===n[1];n[0]>n[l-1]&&(n=[...n].reverse(),s=[...s].reverse());const d=function(e,n,s){const i=[],a=s||t.mix||_,c=e.length-1;for(let t=0;t<c;t++){let s=a(e[t],e[t+1]);if(n){const e=Array.isArray(n)?n[t]||r:n;s=o(e,s)}i.push(s)}return i}(s,c,u),h=d.length,g=e=>{if(f&&e<n[0])return s[0];let t=0;if(h>1)for(;t<n.length-2&&!(e<n[t+1]);t++);const r=i(n[t],n[t+1],e);return d[t](r)};return a?t=>g(e(n[0],n[l-1],t)):g}function te(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=i(0,t,r);e.push(U(n,1,s))}}(t,e.length-1),t}const ne=n((()=>void 0!==window.ScrollTimeline));function re(e,t){let n;const r=()=>{const{currentTime:r}=t,s=(null===r?0:r.value)/100;n!==s&&e(s),n=s};return l.preUpdate(r,!0),()=>f(r)}const se=new WeakMap;let oe;function ie({target:e,contentRect:t,borderBoxSize:n}){se.get(e)?.forEach((r=>{r({target:e,contentSize:t,get size(){return function(e,t){if(t){const{inlineSize:e,blockSize:n}=t[0];return{width:e,height:n}}return e instanceof SVGElement&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight}}(e,n)}})}))}function ae(e){e.forEach(ie)}function ce(e,t){oe||"undefined"!=typeof ResizeObserver&&(oe=new ResizeObserver(ae));const n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let r=document;t&&(r=t.current);const s=n?.[e]??r.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}(e);return n.forEach((e=>{let n=se.get(e);n||(n=new Set,se.set(e,n)),n.add(t),oe?.observe(e)})),()=>{n.forEach((e=>{const n=se.get(e);n?.delete(t),n?.size||oe?.unobserve(e)}))}}const ue=new Set;let le;function fe(e){return ue.add(e),le||(le=()=>{const e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};ue.forEach((e=>e(t)))},window.addEventListener("resize",le)),()=>{ue.delete(e),!ue.size&&le&&(le=void 0)}}const de={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function he(e,t,n,r){const s=n[t],{length:o,position:a}=de[t],c=s.current,u=n.time;s.current=e[`scroll${a}`],s.scrollLength=e[`scroll${o}`]-e[`client${o}`],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=i(0,s.scrollLength,s.current);const l=r-u;var f,d;s.velocity=l>50?0:(f=s.current-c,(d=l)?f*(1e3/d):0)}const ge={start:0,center:.5,end:1};function pe(e,t,n=0){let r=0;if(e in ge&&(e=ge[e]),"string"==typeof e){const t=parseFloat(e);e.endsWith("px")?r=t:e.endsWith("%")?e=t/100:e.endsWith("vw")?r=t/100*document.documentElement.clientWidth:e.endsWith("vh")?r=t/100*document.documentElement.clientHeight:e=t}return"number"==typeof e&&(r=t*e),n+r}const me=[0,0];function ye(e,t,n,r){let s=Array.isArray(e)?e:me,o=0,i=0;return"number"==typeof e?s=[e,e]:"string"==typeof e&&(s=(e=e.trim()).includes(" ")?e.split(" "):[e,ge[e]?e:"0"]),o=pe(s[0],n,r),i=pe(s[1],t),o-i}const ve={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},be={x:0,y:0};function we(t,n,r){const{offset:s=ve.All}=r,{target:o=t,axis:i="y"}=r,a="y"===i?"height":"width",c=o!==t?function(e,t){const n={x:0,y:0};let r=e;for(;r&&r!==t;)if(r instanceof HTMLElement)n.x+=r.offsetLeft,n.y+=r.offsetTop,r=r.offsetParent;else if("svg"===r.tagName){const e=r.getBoundingClientRect();r=r.parentElement;const t=r.getBoundingClientRect();n.x+=e.left-t.left,n.y+=e.top-t.top}else{if(!(r instanceof SVGGraphicsElement))break;{const{x:e,y:t}=r.getBBox();n.x+=e,n.y+=t;let s=null,o=r.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=r.parentNode;r=s}}return n}(o,t):be,u=o===t?{width:t.scrollWidth,height:t.scrollHeight}:function(e){return"getBBox"in e&&"svg"!==e.tagName?e.getBBox():{width:e.clientWidth,height:e.clientHeight}}(o),l={width:t.clientWidth,height:t.clientHeight};n[i].offset.length=0;let f=!n[i].interpolate;const d=s.length;for(let e=0;e<d;e++){const t=ye(s[e],l[a],u[a],c[i]);f||t===n[i].interpolatorOffsets[e]||(f=!0),n[i].offset[e]=t}f&&(n[i].interpolate=ee(n[i].offset,te(s),{clamp:!1}),n[i].interpolatorOffsets=[...n[i].offset]),n[i].progress=e(0,1,n[i].interpolate(n[i].current))}function xe(e,t,n,r={}){return{measure:()=>function(e,t=e,n){if(n.x.targetOffset=0,n.y.targetOffset=0,t!==e){let r=t;for(;r&&r!==e;)n.x.targetOffset+=r.offsetLeft,n.y.targetOffset+=r.offsetTop,r=r.offsetParent}n.x.targetLength=t===e?t.scrollWidth:t.clientWidth,n.y.targetLength=t===e?t.scrollHeight:t.clientHeight,n.x.containerLength=e.clientWidth,n.y.containerLength=e.clientHeight}(e,r.target,n),update:t=>{!function(e,t,n){he(e,"x",t,n),he(e,"y",t,n),t.time=n}(e,n,t),(r.offset||r.target)&&we(e,n,r)},notify:()=>t(n)}}const Ee=new WeakMap,We=new WeakMap,Le=new WeakMap,Me=e=>e===document.documentElement?window:e;function Se(e,{container:t=document.documentElement,...n}={}){let r=Le.get(t);r||(r=new Set,Le.set(t,r));const s=xe(t,e,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(r.add(s),!Ee.has(t)){const e=()=>{for(const e of r)e.measure()},n=()=>{for(const e of r)e.update(d.timestamp)},s=()=>{for(const e of r)e.notify()},a=()=>{l.read(e),l.read(n),l.preUpdate(s)};Ee.set(t,a);const c=Me(t);window.addEventListener("resize",a,{passive:!0}),t!==document.documentElement&&We.set(t,(i=a,"function"==typeof(o=t)?fe(o):ce(o,i))),c.addEventListener("scroll",a,{passive:!0}),a()}var o,i;const a=Ee.get(t);return l.read(a,!1,!0),()=>{f(a);const e=Le.get(t);if(!e)return;if(e.delete(s),e.size)return;const n=Ee.get(t);Ee.delete(t),n&&(Me(t).removeEventListener("scroll",n),We.get(t)?.(),window.removeEventListener("resize",n))}}const ze=new Map;function Ae({source:e,container:t,...n}){const{axis:r}=n;e&&(t=e);const s=ze.get(t)??new Map;ze.set(t,s);const o=n.target??"self",i=s.get(o)??{},a=r+(n.offset??[]).join(",");return i[a]||(i[a]=!n.target&&ne()?new ScrollTimeline({source:t,axis:r}):function(e){const t={value:0},n=Se((n=>{t.value=100*n[e.axis].progress}),e);return{currentTime:t,cancel:n}}({container:t,...n})),i[a]}function Be(e,{axis:t="y",container:n=document.documentElement,...r}={}){n===document.documentElement&&("y"===t&&n.scrollHeight===n.clientHeight||"x"===t&&n.scrollWidth===n.clientWidth)&&(n=document.body);const s={axis:t,container:n,...r};return"function"==typeof e?function(e,t){return function(e){return 2===e.length}(e)?Se((n=>{e(n[t.axis].progress,n)}),t):re(e,Ae(t))}(e,s):function(e,t){const n=Ae(t);return e.attachTimeline({timeline:t.target?void 0:n,observe:e=>(e.pause(),re((t=>{e.time=e.duration*t}),n))})}(e,s)}export{Be as scroll};
