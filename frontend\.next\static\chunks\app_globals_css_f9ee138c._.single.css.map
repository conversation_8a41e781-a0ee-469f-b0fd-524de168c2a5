{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-sans: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\",\n      \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\",\n      \"Courier New\", monospace;\n    --color-red-50: oklch(97.1% 0.013 17.38);\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-300: oklch(80.8% 0.114 19.571);\n    --color-red-400: oklch(70.4% 0.191 22.216);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-800: oklch(44.4% 0.177 26.899);\n    --color-red-900: oklch(39.6% 0.141 25.723);\n    --color-amber-50: oklch(98.7% 0.022 95.277);\n    --color-amber-100: oklch(96.2% 0.059 95.617);\n    --color-amber-200: oklch(92.4% 0.12 95.746);\n    --color-amber-800: oklch(47.3% 0.137 46.201);\n    --color-amber-900: oklch(41.4% 0.112 45.904);\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-lime-500: oklch(76.8% 0.233 130.85);\n    --color-green-400: oklch(79.2% 0.209 151.711);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-indigo-200: oklch(87% 0.065 274.039);\n    --color-indigo-300: oklch(78.5% 0.115 274.713);\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\n    --color-indigo-900: oklch(35.9% 0.144 278.697);\n    --color-purple-50: oklch(97.7% 0.014 308.299);\n    --color-purple-100: oklch(94.6% 0.033 307.174);\n    --color-purple-200: oklch(90.2% 0.063 306.703);\n    --color-purple-800: oklch(43.8% 0.218 303.724);\n    --color-purple-900: oklch(38.1% 0.176 304.987);\n    --color-gray-50: oklch(98.5% 0.002 247.839);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-xl: 36rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-5xl: 64rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --text-7xl: 4.5rem;\n    --text-7xl--line-height: 1;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --leading-tight: 1.25;\n    --leading-relaxed: 1.625;\n    --radius-sm: 0.25rem;\n    --radius-md: 0.375rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --radius-2xl: 1rem;\n    --radius-3xl: 1.5rem;\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-sm: 8px;\n    --blur-md: 12px;\n    --blur-xl: 24px;\n    --blur-2xl: 40px;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-sans);\n    --default-mono-font-family: var(--font-mono);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .pointer-events-auto {\n    pointer-events: auto;\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .visible {\n    visibility: visible;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-x-0 {\n    inset-inline: calc(var(--spacing) * 0);\n  }\n  .inset-x-4 {\n    inset-inline: calc(var(--spacing) * 4);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-2 {\n    top: calc(var(--spacing) * 2);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-20 {\n    top: calc(var(--spacing) * 20);\n  }\n  .top-full {\n    top: 100%;\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-6 {\n    right: calc(var(--spacing) * 6);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-10 {\n    bottom: calc(var(--spacing) * 10);\n  }\n  .bottom-full {\n    bottom: 100%;\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1 {\n    left: calc(var(--spacing) * 1);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .left-4 {\n    left: calc(var(--spacing) * 4);\n  }\n  .left-\\[2px\\] {\n    left: 2px;\n  }\n  .left-\\[calc\\(100\\%-26px\\)\\] {\n    left: calc(100% - 26px);\n  }\n  .-z-10 {\n    z-index: calc(10 * -1);\n  }\n  .-z-40 {\n    z-index: calc(40 * -1);\n  }\n  .-z-50 {\n    z-index: calc(50 * -1);\n  }\n  .z-5 {\n    z-index: 5;\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-60 {\n    z-index: 60;\n  }\n  .z-\\[100\\] {\n    z-index: 100;\n  }\n  .order-1 {\n    order: 1;\n  }\n  .order-2 {\n    order: 2;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .m-1 {\n    margin: calc(var(--spacing) * 1);\n  }\n  .m-2 {\n    margin: calc(var(--spacing) * 2);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .my-3 {\n    margin-block: calc(var(--spacing) * 3);\n  }\n  .my-auto {\n    margin-block: auto;\n  }\n  .prose {\n    color: var(--tw-prose-body);\n    max-width: 65ch;\n    :where(p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n    }\n    :where([class~=\"lead\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-lead);\n      font-size: 1.25em;\n      line-height: 1.6;\n      margin-top: 1.2em;\n      margin-bottom: 1.2em;\n    }\n    :where(a):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-links);\n      text-decoration: underline;\n      font-weight: 500;\n    }\n    :where(strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-bold);\n      font-weight: 600;\n    }\n    :where(a strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(blockquote strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(thead th strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: decimal;\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n      padding-inline-start: 1.625em;\n    }\n    :where(ol[type=\"A\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-alpha;\n    }\n    :where(ol[type=\"a\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-alpha;\n    }\n    :where(ol[type=\"A\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-alpha;\n    }\n    :where(ol[type=\"a\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-alpha;\n    }\n    :where(ol[type=\"I\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-roman;\n    }\n    :where(ol[type=\"i\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-roman;\n    }\n    :where(ol[type=\"I\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-roman;\n    }\n    :where(ol[type=\"i\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-roman;\n    }\n    :where(ol[type=\"1\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: decimal;\n    }\n    :where(ul):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: disc;\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n      padding-inline-start: 1.625em;\n    }\n    :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n      font-weight: 400;\n      color: var(--tw-prose-counters);\n    }\n    :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n      color: var(--tw-prose-bullets);\n    }\n    :where(dt):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      margin-top: 1.25em;\n    }\n    :where(hr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-color: var(--tw-prose-hr);\n      border-top-width: 1;\n      margin-top: 3em;\n      margin-bottom: 3em;\n    }\n    :where(blockquote):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 500;\n      font-style: italic;\n      color: var(--tw-prose-quotes);\n      border-inline-start-width: 0.25rem;\n      border-inline-start-color: var(--tw-prose-quote-borders);\n      quotes: \"\\201C\"\"\\201D\"\"\\2018\"\"\\2019\";\n      margin-top: 1.6em;\n      margin-bottom: 1.6em;\n      padding-inline-start: 1em;\n    }\n    :where(blockquote p:first-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: open-quote;\n    }\n    :where(blockquote p:last-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: close-quote;\n    }\n    :where(h1):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 800;\n      font-size: 2.25em;\n      margin-top: 0;\n      margin-bottom: 0.8888889em;\n      line-height: 1.1111111;\n    }\n    :where(h1 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 900;\n      color: inherit;\n    }\n    :where(h2):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 700;\n      font-size: 1.5em;\n      margin-top: 2em;\n      margin-bottom: 1em;\n      line-height: 1.3333333;\n    }\n    :where(h2 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 800;\n      color: inherit;\n    }\n    :where(h3):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      font-size: 1.25em;\n      margin-top: 1.6em;\n      margin-bottom: 0.6em;\n      line-height: 1.6;\n    }\n    :where(h3 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 700;\n      color: inherit;\n    }\n    :where(h4):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      margin-top: 1.5em;\n      margin-bottom: 0.5em;\n      line-height: 1.5;\n    }\n    :where(h4 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 700;\n      color: inherit;\n    }\n    :where(img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(picture):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      display: block;\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(video):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(kbd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 500;\n      font-family: inherit;\n      color: var(--tw-prose-kbd);\n      box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);\n      font-size: 0.875em;\n      border-radius: 0.3125rem;\n      padding-top: 0.1875em;\n      padding-inline-end: 0.375em;\n      padding-bottom: 0.1875em;\n      padding-inline-start: 0.375em;\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-code);\n      font-weight: 600;\n      font-size: 0.875em;\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: \"`\";\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: \"`\";\n    }\n    :where(a code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(h1 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(h2 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n      font-size: 0.875em;\n    }\n    :where(h3 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n      font-size: 0.9em;\n    }\n    :where(h4 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(blockquote code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(thead th code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(pre):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-pre-code);\n      background-color: var(--tw-prose-pre-bg);\n      overflow-x: auto;\n      font-weight: 400;\n      font-size: 0.875em;\n      line-height: 1.7142857;\n      margin-top: 1.7142857em;\n      margin-bottom: 1.7142857em;\n      border-radius: 0.375rem;\n      padding-top: 0.8571429em;\n      padding-inline-end: 1.1428571em;\n      padding-bottom: 0.8571429em;\n      padding-inline-start: 1.1428571em;\n    }\n    :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      background-color: transparent;\n      border-width: 0;\n      border-radius: 0;\n      padding: 0;\n      font-weight: inherit;\n      color: inherit;\n      font-size: inherit;\n      font-family: inherit;\n      line-height: inherit;\n    }\n    :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: none;\n    }\n    :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: none;\n    }\n    :where(table):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      width: 100%;\n      table-layout: auto;\n      margin-top: 2em;\n      margin-bottom: 2em;\n      font-size: 0.875em;\n      line-height: 1.7142857;\n    }\n    :where(thead):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 1px;\n      border-bottom-color: var(--tw-prose-th-borders);\n    }\n    :where(thead th):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      vertical-align: bottom;\n      padding-inline-end: 0.5714286em;\n      padding-bottom: 0.5714286em;\n      padding-inline-start: 0.5714286em;\n    }\n    :where(tbody tr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 1px;\n      border-bottom-color: var(--tw-prose-td-borders);\n    }\n    :where(tbody tr:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 0;\n    }\n    :where(tbody td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      vertical-align: baseline;\n    }\n    :where(tfoot):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-top-width: 1px;\n      border-top-color: var(--tw-prose-th-borders);\n    }\n    :where(tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      vertical-align: top;\n    }\n    :where(th, td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      text-align: start;\n    }\n    :where(figure > *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(figcaption):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-captions);\n      font-size: 0.875em;\n      line-height: 1.4285714;\n      margin-top: 0.8571429em;\n    }\n    --tw-prose-body: oklch(37.3% 0.034 259.733);\n    --tw-prose-headings: oklch(21% 0.034 264.665);\n    --tw-prose-lead: oklch(44.6% 0.03 256.802);\n    --tw-prose-links: oklch(21% 0.034 264.665);\n    --tw-prose-bold: oklch(21% 0.034 264.665);\n    --tw-prose-counters: oklch(55.1% 0.027 264.364);\n    --tw-prose-bullets: oklch(87.2% 0.01 258.338);\n    --tw-prose-hr: oklch(92.8% 0.006 264.531);\n    --tw-prose-quotes: oklch(21% 0.034 264.665);\n    --tw-prose-quote-borders: oklch(92.8% 0.006 264.531);\n    --tw-prose-captions: oklch(55.1% 0.027 264.364);\n    --tw-prose-kbd: oklch(21% 0.034 264.665);\n    --tw-prose-kbd-shadows: NaN NaN NaN;\n    --tw-prose-code: oklch(21% 0.034 264.665);\n    --tw-prose-pre-code: oklch(92.8% 0.006 264.531);\n    --tw-prose-pre-bg: oklch(27.8% 0.033 256.848);\n    --tw-prose-th-borders: oklch(87.2% 0.01 258.338);\n    --tw-prose-td-borders: oklch(92.8% 0.006 264.531);\n    --tw-prose-invert-body: oklch(87.2% 0.01 258.338);\n    --tw-prose-invert-headings: #fff;\n    --tw-prose-invert-lead: oklch(70.7% 0.022 261.325);\n    --tw-prose-invert-links: #fff;\n    --tw-prose-invert-bold: #fff;\n    --tw-prose-invert-counters: oklch(70.7% 0.022 261.325);\n    --tw-prose-invert-bullets: oklch(44.6% 0.03 256.802);\n    --tw-prose-invert-hr: oklch(37.3% 0.034 259.733);\n    --tw-prose-invert-quotes: oklch(96.7% 0.003 264.542);\n    --tw-prose-invert-quote-borders: oklch(37.3% 0.034 259.733);\n    --tw-prose-invert-captions: oklch(70.7% 0.022 261.325);\n    --tw-prose-invert-kbd: #fff;\n    --tw-prose-invert-kbd-shadows: 255 255 255;\n    --tw-prose-invert-code: #fff;\n    --tw-prose-invert-pre-code: oklch(87.2% 0.01 258.338);\n    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);\n    --tw-prose-invert-th-borders: oklch(44.6% 0.03 256.802);\n    --tw-prose-invert-td-borders: oklch(37.3% 0.034 259.733);\n    font-size: 1rem;\n    line-height: 1.75;\n    :where(picture > img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5em;\n      margin-bottom: 0.5em;\n    }\n    :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0.375em;\n    }\n    :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0.375em;\n    }\n    :where(.prose > ul > li p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.75em;\n      margin-bottom: 0.75em;\n    }\n    :where(.prose > ul > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n    }\n    :where(.prose > ul > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.25em;\n    }\n    :where(.prose > ol > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n    }\n    :where(.prose > ol > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.25em;\n    }\n    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.75em;\n      margin-bottom: 0.75em;\n    }\n    :where(dl):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n    }\n    :where(dd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5em;\n      padding-inline-start: 1.625em;\n    }\n    :where(hr + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h2 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h3 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h4 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(thead th:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0;\n    }\n    :where(thead th:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-end: 0;\n    }\n    :where(tbody td, tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-top: 0.5714286em;\n      padding-inline-end: 0.5714286em;\n      padding-bottom: 0.5714286em;\n      padding-inline-start: 0.5714286em;\n    }\n    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0;\n    }\n    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-end: 0;\n    }\n    :where(figure):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(.prose > :first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(.prose > :last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 0;\n    }\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-3 {\n    margin-top: calc(var(--spacing) * 3);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mt-10 {\n    margin-top: calc(var(--spacing) * 10);\n  }\n  .mt-12 {\n    margin-top: calc(var(--spacing) * 12);\n  }\n  .mt-40 {\n    margin-top: calc(var(--spacing) * 40);\n  }\n  .mt-auto {\n    margin-top: auto;\n  }\n  .mr-1 {\n    margin-right: calc(var(--spacing) * 1);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-12 {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n  .mb-16 {\n    margin-bottom: calc(var(--spacing) * 16);\n  }\n  .mb-20 {\n    margin-bottom: calc(var(--spacing) * 20);\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-3 {\n    margin-left: calc(var(--spacing) * 3);\n  }\n  .ml-4 {\n    margin-left: calc(var(--spacing) * 4);\n  }\n  .ml-10 {\n    margin-left: calc(var(--spacing) * 10);\n  }\n  .ml-12 {\n    margin-left: calc(var(--spacing) * 12);\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .h-1 {\n    height: calc(var(--spacing) * 1);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-3\\.5 {\n    height: calc(var(--spacing) * 3.5);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-96 {\n    height: calc(var(--spacing) * 96);\n  }\n  .h-\\[120px\\] {\n    height: 120px;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-screen {\n    height: 100vh;\n  }\n  .min-h-\\[24px\\] {\n    min-height: 24px;\n  }\n  .min-h-\\[80vh\\] {\n    min-height: 80vh;\n  }\n  .min-h-\\[300px\\] {\n    min-height: 300px;\n  }\n  .min-h-\\[400px\\] {\n    min-height: 400px;\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-7 {\n    width: calc(var(--spacing) * 7);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-14 {\n    width: calc(var(--spacing) * 14);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-36 {\n    width: calc(var(--spacing) * 36);\n  }\n  .w-40 {\n    width: calc(var(--spacing) * 40);\n  }\n  .w-\\[95vw\\] {\n    width: 95vw;\n  }\n  .w-\\[180px\\] {\n    width: 180px;\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-3xl {\n    max-width: var(--container-3xl);\n  }\n  .max-w-5xl {\n    max-width: var(--container-5xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-7xl {\n    max-width: var(--container-7xl);\n  }\n  .max-w-\\[90\\%\\] {\n    max-width: 90%;\n  }\n  .max-w-\\[100px\\] {\n    max-width: 100px;\n  }\n  .max-w-\\[150px\\] {\n    max-width: 150px;\n  }\n  .max-w-lg {\n    max-width: var(--container-lg);\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .max-w-xl {\n    max-width: var(--container-xl);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .flex-grow {\n    flex-grow: 1;\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .scale-110 {\n    --tw-scale-x: 110%;\n    --tw-scale-y: 110%;\n    --tw-scale-z: 110%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-200 {\n    --tw-scale-x: 200%;\n    --tw-scale-y: 200%;\n    --tw-scale-z: 200%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-x-\\[-1\\] {\n    --tw-scale-x: -1;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .rotate-90 {\n    rotate: 90deg;\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .cursor-not-allowed {\n    cursor: not-allowed;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .touch-manipulation {\n    touch-action: manipulation;\n  }\n  .touch-none {\n    touch-action: none;\n  }\n  .resize {\n    resize: both;\n  }\n  .resize-none {\n    resize: none;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .gap-12 {\n    gap: calc(var(--spacing) * 12);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .self-start {\n    align-self: flex-start;\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-2xl {\n    border-radius: var(--radius-2xl);\n  }\n  .rounded-3xl {\n    border-radius: var(--radius-3xl);\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-md {\n    border-radius: var(--radius-md);\n  }\n  .rounded-sm {\n    border-radius: var(--radius-sm);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-t-2 {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 2px;\n  }\n  .border-r-2 {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 2px;\n  }\n  .border-l-4 {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 4px;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .border-amber-200 {\n    border-color: var(--color-amber-200);\n  }\n  .border-black {\n    border-color: var(--color-black);\n  }\n  .border-black\\/10 {\n    border-color: color-mix(in srgb, #000 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-black) 10%, transparent);\n    }\n  }\n  .border-black\\/20 {\n    border-color: color-mix(in srgb, #000 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-black) 20%, transparent);\n    }\n  }\n  .border-gray-200 {\n    border-color: var(--color-gray-200);\n  }\n  .border-gray-300 {\n    border-color: var(--color-gray-300);\n  }\n  .border-gray-600 {\n    border-color: var(--color-gray-600);\n  }\n  .border-gray-700 {\n    border-color: var(--color-gray-700);\n  }\n  .border-green-400 {\n    border-color: var(--color-green-400);\n  }\n  .border-indigo-500 {\n    border-color: var(--color-indigo-500);\n  }\n  .border-indigo-600\\/40 {\n    border-color: color-mix(in srgb, oklch(51.1% 0.262 276.966) 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-indigo-600) 40%, transparent);\n    }\n  }\n  .border-purple-200 {\n    border-color: var(--color-purple-200);\n  }\n  .border-red-200 {\n    border-color: var(--color-red-200);\n  }\n  .border-white {\n    border-color: var(--color-white);\n  }\n  .border-white\\/10 {\n    border-color: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n  }\n  .border-white\\/20 {\n    border-color: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n  }\n  .border-t-transparent {\n    border-top-color: transparent;\n  }\n  .bg-\\[\\#1A1A1A\\] {\n    background-color: #1A1A1A;\n  }\n  .bg-amber-50 {\n    background-color: var(--color-amber-50);\n  }\n  .bg-black {\n    background-color: var(--color-black);\n  }\n  .bg-black\\/60 {\n    background-color: color-mix(in srgb, #000 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 60%, transparent);\n    }\n  }\n  .bg-black\\/80 {\n    background-color: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n  }\n  .bg-gray-50\\/30 {\n    background-color: color-mix(in srgb, oklch(98.5% 0.002 247.839) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-50) 30%, transparent);\n    }\n  }\n  .bg-gray-50\\/40 {\n    background-color: color-mix(in srgb, oklch(98.5% 0.002 247.839) 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-50) 40%, transparent);\n    }\n  }\n  .bg-gray-50\\/50 {\n    background-color: color-mix(in srgb, oklch(98.5% 0.002 247.839) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-50) 50%, transparent);\n    }\n  }\n  .bg-gray-100 {\n    background-color: var(--color-gray-100);\n  }\n  .bg-gray-200 {\n    background-color: var(--color-gray-200);\n  }\n  .bg-gray-300\\/10 {\n    background-color: color-mix(in srgb, oklch(87.2% 0.01 258.338) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-300) 10%, transparent);\n    }\n  }\n  .bg-gray-400 {\n    background-color: var(--color-gray-400);\n  }\n  .bg-gray-700 {\n    background-color: var(--color-gray-700);\n  }\n  .bg-gray-800 {\n    background-color: var(--color-gray-800);\n  }\n  .bg-gray-800\\/50 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);\n    }\n  }\n  .bg-gray-800\\/90 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 90%, transparent);\n    }\n  }\n  .bg-gray-900 {\n    background-color: var(--color-gray-900);\n  }\n  .bg-indigo-900 {\n    background-color: var(--color-indigo-900);\n  }\n  .bg-indigo-900\\/80 {\n    background-color: color-mix(in srgb, oklch(35.9% 0.144 278.697) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-indigo-900) 80%, transparent);\n    }\n  }\n  .bg-purple-50 {\n    background-color: var(--color-purple-50);\n  }\n  .bg-red-50 {\n    background-color: var(--color-red-50);\n  }\n  .bg-red-400 {\n    background-color: var(--color-red-400);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-transparent\\/10 {\n    background-color: color-mix(in oklab, transparent 10%, transparent);\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-white\\/5 {\n    background-color: color-mix(in srgb, #fff 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);\n    }\n  }\n  .bg-white\\/10 {\n    background-color: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n  }\n  .bg-white\\/90 {\n    background-color: color-mix(in srgb, #fff 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 90%, transparent);\n    }\n  }\n  .bg-yellow-200 {\n    background-color: var(--color-yellow-200);\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-t {\n    --tw-gradient-position: to top in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .\\[mask-image\\:radial-gradient\\(900px_at_center\\,transparent_30\\%\\,white\\)\\] {\n    mask-image: radial-gradient(900px at center,transparent 30%,white);\n  }\n  .bg-\\[length\\:200\\%_200\\%\\] {\n    background-size: 200% 200%;\n  }\n  .bg-clip-text {\n    background-clip: text;\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-1\\.5 {\n    padding: calc(var(--spacing) * 1.5);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .p-12 {\n    padding: calc(var(--spacing) * 12);\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-2\\.5 {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-5 {\n    padding-inline: calc(var(--spacing) * 5);\n  }\n  .px-8 {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n  .px-10 {\n    padding-inline: calc(var(--spacing) * 10);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-5 {\n    padding-block: calc(var(--spacing) * 5);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .py-20 {\n    padding-block: calc(var(--spacing) * 20);\n  }\n  .py-24 {\n    padding-block: calc(var(--spacing) * 24);\n  }\n  .py-32 {\n    padding-block: calc(var(--spacing) * 32);\n  }\n  .pt-2 {\n    padding-top: calc(var(--spacing) * 2);\n  }\n  .pt-4 {\n    padding-top: calc(var(--spacing) * 4);\n  }\n  .pt-8 {\n    padding-top: calc(var(--spacing) * 8);\n  }\n  .pt-24 {\n    padding-top: calc(var(--spacing) * 24);\n  }\n  .pr-1 {\n    padding-right: calc(var(--spacing) * 1);\n  }\n  .pb-3 {\n    padding-bottom: calc(var(--spacing) * 3);\n  }\n  .pb-48 {\n    padding-bottom: calc(var(--spacing) * 48);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .text-right {\n    text-align: right;\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-5xl {\n    font-size: var(--text-5xl);\n    line-height: var(--tw-leading, var(--text-5xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .leading-tight {\n    --tw-leading: var(--leading-tight);\n    line-height: var(--leading-tight);\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .break-words {\n    overflow-wrap: break-word;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .text-black {\n    color: var(--color-black);\n  }\n  .text-gray-200 {\n    color: var(--color-gray-200);\n  }\n  .text-gray-300 {\n    color: var(--color-gray-300);\n  }\n  .text-gray-400 {\n    color: var(--color-gray-400);\n  }\n  .text-gray-400\\/80 {\n    color: color-mix(in srgb, oklch(70.7% 0.022 261.325) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-gray-400) 80%, transparent);\n    }\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-gray-600\\/70 {\n    color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-gray-600) 70%, transparent);\n    }\n  }\n  .text-gray-700 {\n    color: var(--color-gray-700);\n  }\n  .text-gray-800 {\n    color: var(--color-gray-800);\n  }\n  .text-gray-900 {\n    color: var(--color-gray-900);\n  }\n  .text-green-500 {\n    color: var(--color-green-500);\n  }\n  .text-green-600 {\n    color: var(--color-green-600);\n  }\n  .text-indigo-200 {\n    color: var(--color-indigo-200);\n  }\n  .text-indigo-300 {\n    color: var(--color-indigo-300);\n  }\n  .text-red-400 {\n    color: var(--color-red-400);\n  }\n  .text-red-500 {\n    color: var(--color-red-500);\n  }\n  .text-transparent {\n    color: transparent;\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-white\\/60 {\n    color: color-mix(in srgb, #fff 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 60%, transparent);\n    }\n  }\n  .text-white\\/70 {\n    color: color-mix(in srgb, #fff 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 70%, transparent);\n    }\n  }\n  .text-white\\/80 {\n    color: color-mix(in srgb, #fff 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 80%, transparent);\n    }\n  }\n  .text-yellow-300 {\n    color: var(--color-yellow-300);\n  }\n  .text-yellow-400\\/70 {\n    color: color-mix(in srgb, oklch(85.2% 0.199 91.936) 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-yellow-400) 70%, transparent);\n    }\n  }\n  .text-yellow-500 {\n    color: var(--color-yellow-500);\n  }\n  .placeholder-gray-500 {\n    &::placeholder {\n      color: var(--color-gray-500);\n    }\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-5 {\n    opacity: 5%;\n  }\n  .opacity-10 {\n    opacity: 10%;\n  }\n  .opacity-15 {\n    opacity: 15%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .opacity-100 {\n    opacity: 100%;\n  }\n  .opacity-\\[0\\.15\\] {\n    opacity: 0.15;\n  }\n  .mix-blend-overlay {\n    mix-blend-mode: overlay;\n  }\n  .mix-blend-screen {\n    mix-blend-mode: screen;\n  }\n  .shadow {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-2xl {\n    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[0_0_15px_rgba\\(255\\,246\\,141\\,0\\.5\\)\\] {\n    --tw-shadow: 0 0 15px var(--tw-shadow-color, rgba(255,246,141,0.5));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-gray-200\\/30 {\n    --tw-shadow-color: color-mix(in srgb, oklch(92.8% 0.006 264.531) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-gray-200) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-gray-700\\/30 {\n    --tw-shadow-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-gray-700) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-green-400\\/20 {\n    --tw-shadow-color: color-mix(in srgb, oklch(79.2% 0.209 151.711) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-green-400) 20%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .blur {\n    --tw-blur: blur(8px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-md {\n    --tw-blur: blur(var(--blur-md));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur-2xl {\n    --tw-backdrop-blur: blur(var(--blur-2xl));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-md {\n    --tw-backdrop-blur: blur(var(--blur-md));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-xl {\n    --tw-backdrop-blur: blur(var(--blur-xl));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-150 {\n    --tw-duration: 150ms;\n    transition-duration: 150ms;\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .duration-500 {\n    --tw-duration: 500ms;\n    transition-duration: 500ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .prose-invert {\n    --tw-prose-body: var(--tw-prose-invert-body);\n    --tw-prose-headings: var(--tw-prose-invert-headings);\n    --tw-prose-lead: var(--tw-prose-invert-lead);\n    --tw-prose-links: var(--tw-prose-invert-links);\n    --tw-prose-bold: var(--tw-prose-invert-bold);\n    --tw-prose-counters: var(--tw-prose-invert-counters);\n    --tw-prose-bullets: var(--tw-prose-invert-bullets);\n    --tw-prose-hr: var(--tw-prose-invert-hr);\n    --tw-prose-quotes: var(--tw-prose-invert-quotes);\n    --tw-prose-quote-borders: var(--tw-prose-invert-quote-borders);\n    --tw-prose-captions: var(--tw-prose-invert-captions);\n    --tw-prose-kbd: var(--tw-prose-invert-kbd);\n    --tw-prose-kbd-shadows: var(--tw-prose-invert-kbd-shadows);\n    --tw-prose-code: var(--tw-prose-invert-code);\n    --tw-prose-pre-code: var(--tw-prose-invert-pre-code);\n    --tw-prose-pre-bg: var(--tw-prose-invert-pre-bg);\n    --tw-prose-th-borders: var(--tw-prose-invert-th-borders);\n    --tw-prose-td-borders: var(--tw-prose-invert-td-borders);\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .group-hover\\:animate-pulse {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        animation: var(--animate-pulse);\n      }\n    }\n  }\n  .group-hover\\:bg-amber-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: var(--color-amber-100);\n      }\n    }\n  }\n  .group-hover\\:bg-purple-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: var(--color-purple-100);\n      }\n    }\n  }\n  .group-hover\\:bg-red-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: var(--color-red-100);\n      }\n    }\n  }\n  .group-hover\\:opacity-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:scale-110 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:bg-black\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #000 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-black) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-100);\n      }\n    }\n  }\n  .hover\\:bg-gray-800 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-800);\n      }\n    }\n  }\n  .hover\\:bg-red-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-200);\n      }\n    }\n  }\n  .hover\\:bg-red-500 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-500);\n      }\n    }\n  }\n  .hover\\:bg-white\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 10%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:text-gray-200 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-200);\n      }\n    }\n  }\n  .hover\\:text-red-300 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-red-300);\n      }\n    }\n  }\n  .hover\\:text-red-400 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-red-400);\n      }\n    }\n  }\n  .hover\\:text-white {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-red-400 {\n    &:focus {\n      --tw-ring-color: var(--color-red-400);\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .active\\:bg-red-600 {\n    &:active {\n      background-color: var(--color-red-600);\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .sm\\:left-\\[calc\\(100\\%-30px\\)\\] {\n    @media (width >= 40rem) {\n      left: calc(100% - 30px);\n    }\n  }\n  .sm\\:h-7 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .sm\\:h-8 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .sm\\:min-h-\\[400px\\] {\n    @media (width >= 40rem) {\n      min-height: 400px;\n    }\n  }\n  .sm\\:w-7 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 7);\n    }\n  }\n  .sm\\:w-8 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 8);\n    }\n  }\n  .sm\\:w-16 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 16);\n    }\n  }\n  .sm\\:max-w-lg {\n    @media (width >= 40rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:p-2 {\n    @media (width >= 40rem) {\n      padding: calc(var(--spacing) * 2);\n    }\n  }\n  .sm\\:p-6 {\n    @media (width >= 40rem) {\n      padding: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:px-3 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 3);\n    }\n  }\n  .sm\\:px-6 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:py-3 {\n    @media (width >= 40rem) {\n      padding-block: calc(var(--spacing) * 3);\n    }\n  }\n  .sm\\:text-2xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-2xl);\n      line-height: var(--tw-leading, var(--text-2xl--line-height));\n    }\n  }\n  .sm\\:text-3xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .sm\\:text-base {\n    @media (width >= 40rem) {\n      font-size: var(--text-base);\n      line-height: var(--tw-leading, var(--text-base--line-height));\n    }\n  }\n  .sm\\:text-lg {\n    @media (width >= 40rem) {\n      font-size: var(--text-lg);\n      line-height: var(--tw-leading, var(--text-lg--line-height));\n    }\n  }\n  .sm\\:text-sm {\n    @media (width >= 40rem) {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .sm\\:text-xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-xl);\n      line-height: var(--tw-leading, var(--text-xl--line-height));\n    }\n  }\n  .md\\:col-span-2 {\n    @media (width >= 48rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .md\\:block {\n    @media (width >= 48rem) {\n      display: block;\n    }\n  }\n  .md\\:flex {\n    @media (width >= 48rem) {\n      display: flex;\n    }\n  }\n  .md\\:hidden {\n    @media (width >= 48rem) {\n      display: none;\n    }\n  }\n  .md\\:max-w-7xl {\n    @media (width >= 48rem) {\n      max-width: var(--container-7xl);\n    }\n  }\n  .md\\:grid-cols-1 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(1, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-4 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .md\\:bg-transparent {\n    @media (width >= 48rem) {\n      background-color: transparent;\n    }\n  }\n  .md\\:px-8 {\n    @media (width >= 48rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .md\\:text-2xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-2xl);\n      line-height: var(--tw-leading, var(--text-2xl--line-height));\n    }\n  }\n  .md\\:text-4xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .md\\:text-5xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .md\\:text-7xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-7xl);\n      line-height: var(--tw-leading, var(--text-7xl--line-height));\n    }\n  }\n  .md\\:text-xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-xl);\n      line-height: var(--tw-leading, var(--text-xl--line-height));\n    }\n  }\n  .md\\:backdrop-blur-none {\n    @media (width >= 48rem) {\n      --tw-backdrop-blur:  ;\n      -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n      backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    }\n  }\n  .lg\\:order-1 {\n    @media (width >= 64rem) {\n      order: 1;\n    }\n  }\n  .lg\\:order-2 {\n    @media (width >= 64rem) {\n      order: 2;\n    }\n  }\n  .lg\\:mx-0 {\n    @media (width >= 64rem) {\n      margin-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .lg\\:block {\n    @media (width >= 64rem) {\n      display: block;\n    }\n  }\n  .lg\\:flex {\n    @media (width >= 64rem) {\n      display: flex;\n    }\n  }\n  .lg\\:min-h-\\[600px\\] {\n    @media (width >= 64rem) {\n      min-height: 600px;\n    }\n  }\n  .lg\\:max-w-2xl {\n    @media (width >= 64rem) {\n      max-width: var(--container-2xl);\n    }\n  }\n  .lg\\:max-w-md {\n    @media (width >= 64rem) {\n      max-width: var(--container-md);\n    }\n  }\n  .lg\\:grid-cols-2 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:flex-row {\n    @media (width >= 64rem) {\n      flex-direction: row;\n    }\n  }\n  .lg\\:justify-end {\n    @media (width >= 64rem) {\n      justify-content: flex-end;\n    }\n  }\n  .lg\\:justify-start {\n    @media (width >= 64rem) {\n      justify-content: flex-start;\n    }\n  }\n  .lg\\:gap-12 {\n    @media (width >= 64rem) {\n      gap: calc(var(--spacing) * 12);\n    }\n  }\n  .lg\\:gap-16 {\n    @media (width >= 64rem) {\n      gap: calc(var(--spacing) * 16);\n    }\n  }\n  .lg\\:p-12 {\n    @media (width >= 64rem) {\n      padding: calc(var(--spacing) * 12);\n    }\n  }\n  .lg\\:px-8 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .lg\\:text-left {\n    @media (width >= 64rem) {\n      text-align: left;\n    }\n  }\n  .lg\\:text-4xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .lg\\:text-5xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .xl\\:text-6xl {\n    @media (width >= 80rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n  .dark\\:border-amber-800 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-amber-800);\n    }\n  }\n  .dark\\:border-black {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-black);\n    }\n  }\n  .dark\\:border-gray-600 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-gray-600);\n    }\n  }\n  .dark\\:border-gray-700 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-gray-700);\n    }\n  }\n  .dark\\:border-green-600 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-green-600);\n    }\n  }\n  .dark\\:border-purple-800 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-purple-800);\n    }\n  }\n  .dark\\:border-red-800 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-red-800);\n    }\n  }\n  .dark\\:border-white {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-white);\n    }\n  }\n  .dark\\:border-white\\/10 {\n    @media (prefers-color-scheme: dark) {\n      border-color: color-mix(in srgb, #fff 10%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n      }\n    }\n  }\n  .dark\\:border-white\\/20 {\n    @media (prefers-color-scheme: dark) {\n      border-color: color-mix(in srgb, #fff 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-amber-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(41.4% 0.112 45.904) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-amber-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-black {\n    @media (prefers-color-scheme: dark) {\n      background-color: var(--color-black);\n    }\n  }\n  .dark\\:bg-gray-600 {\n    @media (prefers-color-scheme: dark) {\n      background-color: var(--color-gray-600);\n    }\n  }\n  .dark\\:bg-gray-800 {\n    @media (prefers-color-scheme: dark) {\n      background-color: var(--color-gray-800);\n    }\n  }\n  .dark\\:bg-gray-800\\/10 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 10%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-gray-800) 10%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-gray-800\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-gray-800) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-gray-800\\/90 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 90%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-gray-800) 90%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-gray-900 {\n    @media (prefers-color-scheme: dark) {\n      background-color: var(--color-gray-900);\n    }\n  }\n  .dark\\:bg-purple-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(38.1% 0.176 304.987) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-purple-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-red-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:text-gray-100 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-gray-100);\n    }\n  }\n  .dark\\:text-gray-300 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-gray-300);\n    }\n  }\n  .dark\\:text-gray-400 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-gray-400);\n    }\n  }\n  .dark\\:text-lime-500 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-lime-500);\n    }\n  }\n  .dark\\:text-white {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-white);\n    }\n  }\n  .dark\\:placeholder-gray-400 {\n    @media (prefers-color-scheme: dark) {\n      &::placeholder {\n        color: var(--color-gray-400);\n      }\n    }\n  }\n  .dark\\:group-hover\\:bg-amber-900\\/30 {\n    @media (prefers-color-scheme: dark) {\n      &:is(:where(.group):hover *) {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, oklch(41.4% 0.112 45.904) 30%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-amber-900) 30%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:group-hover\\:bg-purple-900\\/30 {\n    @media (prefers-color-scheme: dark) {\n      &:is(:where(.group):hover *) {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, oklch(38.1% 0.176 304.987) 30%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-purple-900) 30%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:group-hover\\:bg-red-900\\/30 {\n    @media (prefers-color-scheme: dark) {\n      &:is(:where(.group):hover *) {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 30%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-gray-800 {\n    @media (prefers-color-scheme: dark) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--color-gray-800);\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-red-300 {\n    @media (prefers-color-scheme: dark) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--color-red-300);\n        }\n      }\n    }\n  }\n}\n:root {\n  --background: #ffffff;\n  --foreground: #1a1a1a;\n  --primary: #f2e9e4;\n  --secondary: #c9ada7;\n  --accent: #9a8c98;\n  --muted: #22223b;\n  --border: #c9ada7;\n  --foreground-rgb: 26, 26, 26;\n  --background-rgb: 255, 255, 255;\n}\n.dark {\n  --background: #22223b;\n  --foreground: #f2e9e4;\n  --primary: #f2e9e4;\n  --secondary: #c9ada7;\n  --accent: #9a8c98;\n  --muted: #c9ada7;\n  --border: #4a4e69;\n  --foreground-rgb: 242, 233, 228;\n  --background-rgb: 34, 34, 59;\n}\nbody {\n  overflow-x: hidden;\n  transition: background-color 0.3s ease, color 0.3s ease;\n}\n.bg-background {\n  background-color: var(--background) !important;\n}\nhtml {\n  scroll-behavior: smooth;\n}\n.glow-button {\n  position: relative;\n  overflow: hidden;\n}\n.glow-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.3);\n  transition: left 0.5s;\n}\n.glow-button:hover::before {\n  left: 100%;\n}\n.text-outline-kairos {\n  text-shadow: -2px -2px 0 #ffffff,\r\n    2px -2px 0 #ffffff,\r\n    -2px 2px 0 #ffffff,\r\n    2px 2px 0 #ffffff;\n}\n.dark .text-outline-kairos {\n  text-shadow: -2px -2px 0 #000000,\r\n    2px -2px 0 #000000,\r\n    -2px 2px 0 #000000,\r\n    2px 2px 0 #000000;\n}\n.text-outline-purple {\n  text-shadow: -2px -2px 0 #ffffff,\r\n    2px -2px 0 #ffffff,\r\n    -2px 2px 0 #ffffff,\r\n    2px 2px 0 #ffffff;\n}\n.dark .text-outline-purple {\n  text-shadow: -2px -2px 0 #000000,\r\n    2px -2px 0 #000000,\r\n    -2px 2px 0 #000000,\r\n    2px 2px 0 #000000;\n}\n.text-glow-minimal {\n  text-shadow: 0 0 3px rgba(242, 233, 228, 0.3);\n}\n.dark .text-glow-minimal {\n  text-shadow: 0 0 3px rgba(242, 233, 228, 0.4);\n}\n.placeholder-visible::placeholder {\n  color: #6b7280;\n  opacity: 1;\n}\n.dark .placeholder-visible::placeholder {\n  color: #9ca3af;\n  opacity: 1;\n}\n.border-glow-kairos {\n  border: 2px solid #f2e9e4;\n  box-shadow: 0 0 20px rgba(242, 233, 228, 0.3),\r\n    inset 0 0 20px rgba(242, 233, 228, 0.1);\n}\n.border-glow-purple {\n  border: 2px solid #9a8c98;\n  box-shadow: 0 0 15px rgba(154, 140, 152, 0.4),\r\n    inset 0 0 15px rgba(154, 140, 152, 0.1);\n}\n.pattern-bg {\n  background-image: radial-gradient(circle at 1px 1px, var(--border) 1px, transparent 0);\n  background-size: 24px 24px;\n  opacity: 0.4;\n}\n.dark .pattern-bg {\n  opacity: 0.2;\n}\n.dots-kairos-red {\n  background-image: radial-gradient(circle at 2px 2px, #f2e9e4 2px, transparent 0);\n  background-size: 32px 32px;\n  opacity: 0.35;\n}\n.dots-kairos-purple {\n  background-image: radial-gradient(circle at 1px 1px, #9a8c98 1px, transparent 0);\n  background-size: 20px 20px;\n  opacity: 0.4;\n}\n.dots-kairos-beige {\n  background-image: radial-gradient(circle at 1.5px 1.5px, #c9ada7 1.5px, transparent 0);\n  background-size: 28px 28px;\n  opacity: 0.38;\n}\n.dots-multi {\n  background-image: radial-gradient(circle at 2px 2px, #f2e9e4 1px, transparent 0),\r\n    radial-gradient(circle at 12px 12px, #c9ada7 1px, transparent 0),\r\n    radial-gradient(circle at 22px 6px, #9a8c98 1px, transparent 0);\n  background-size: 24px 24px;\n  opacity: 0.45;\n}\n.dark .dots-kairos-red {\n  opacity: 0.15;\n}\n.dark .dots-kairos-purple {\n  opacity: 0.2;\n}\n.dark .dots-kairos-beige {\n  opacity: 0.18;\n}\n.dark .dots-multi {\n  opacity: 0.15;\n}\n.font-inknut {\n  font-family: var(--font-inknut-antiqua);\n}\n.glass-card {\n  -webkit-backdrop-filter: blur(24px) saturate(180%);\n  backdrop-filter: blur(24px) saturate(180%);\n  border-radius: 1.75rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  position: relative;\n  overflow: hidden;\n}\n.glass-card {\n  background: rgba(242, 233, 228, 0.08);\n  border: 1px solid rgba(201, 173, 167, 0.15);\n  box-shadow: 0 0 0 1px rgba(242, 233, 228, 0.1) inset,\r\n    0 8px 32px -8px rgba(34, 34, 59, 0.08),\r\n    0 2px 16px -4px rgba(154, 140, 152, 0.1);\n}\n.glass-card:hover {\n  background: rgba(242, 233, 228, 0.12);\n  border-color: rgba(201, 173, 167, 0.25);\n  box-shadow: 0 0 0 1px rgba(242, 233, 228, 0.15) inset,\r\n    0 16px 48px -12px rgba(34, 34, 59, 0.12),\r\n    0 4px 24px -6px rgba(154, 140, 152, 0.15);\n  transform: translateY(-2px);\n}\n.dark .glass-card {\n  background: rgba(242, 233, 228, 0.06);\n  border: 1px solid rgba(201, 173, 167, 0.12);\n  box-shadow: 0 0 0 1px rgba(242, 233, 228, 0.08) inset,\r\n    0 8px 32px -8px rgba(0, 0, 0, 0.4),\r\n    0 2px 16px -4px rgba(74, 78, 105, 0.2);\n}\n.dark .glass-card:hover {\n  background: rgba(242, 233, 228, 0.1);\n  border-color: rgba(201, 173, 167, 0.2);\n  box-shadow: 0 0 0 1px rgba(242, 233, 228, 0.12) inset,\r\n    0 16px 48px -12px rgba(0, 0, 0, 0.5),\r\n    0 4px 24px -6px rgba(74, 78, 105, 0.3);\n  transform: translateY(-2px);\n}\n.glass-card:hover {\n  background: rgba(255, 255, 255, 0.07);\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.07) inset,\r\n    0 15px 30px -8px rgba(0, 0, 0, 0.5),\r\n    0 0 25px rgba(63, 107, 253, 0.15);\n}\n.sidebar-glass-menu {\n  background: rgba(0, 0, 0, 0.85);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\n  backdrop-filter: blur(20px) saturate(180%);\n  border-radius: 0.75rem;\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05) inset,\r\n    0 8px 16px rgba(0, 0, 0, 0.6);\n}\n.custom-scrollbar::-webkit-scrollbar {\n  width: 6px;\n}\n.custom-scrollbar::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 10px;\n}\n.custom-scrollbar::-webkit-scrollbar-thumb {\n  background: rgba(255, 246, 141, 0.3);\n  border-radius: 10px;\n}\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\n  background: rgba(172, 172, 169, 0.5);\n}\n.custom-scrollbar {\n  scrollbar-width: thin;\n  scrollbar-color: rgba(150, 148, 150, 0.203) rgba(0, 0, 0, 0.1);\n}\n.grain-bg {\n  background-image: url(\"data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\");\n}\n.vignette-effect {\n  background: radial-gradient(circle at center, transparent 40%, rgba(0,0,0,0.4) 100%);\n  mix-blend-mode: multiply;\n}\n@media (max-width: 768px) {\n  .vignette-effect {\n    background: radial-gradient(circle at center, transparent 50%, rgba(0,0,0,0.3) 100%);\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n    }\n  }\n}\r\n\r\n\r\n"], "names": [], "mappings": "AACA;EA0/FE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1/FJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EA4GE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAxPF;;AAAA;EA6PE;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAGE;;;;;EAIA;;;;;;;;EAOA;;;;;;EAKA;;;;;EAIA;;;;EASA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;;EAMA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;EAOA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;;;;;;;;;;EAYA;;;;;;EAKA;;;;EAMA;;;;EAMA;;;;;EAIA;;;;;EAIA;;;;EASA;;;;;;;;;;;;;;;;EAeA;;;;;;;;;;;;EAWA;;;;EAMA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;;EA4CA;;;;;EAIA;;;;;EAIA;;;;EAMA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAYA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;;EAMA;;;;;;;EAMA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAIE;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;;;;;;;;;;;EAoBA;;;;;EAIA;;;;;EAMI;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAOzB;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAOzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;;EAKrC;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IACE;;;;;EAMF;IAEI;MAAuB;;;;MAErB;QAAgD;;;;;;;EAQtD;IAEI;MAAuB;;;;MAErB;QAAgD;;;;;;;EAQtD;IAEI;MAAuB;;;;MAErB;QAAgD;;;;;;;EAQtD;IAEI;MAAuB;;;;;;EAO3B;IAEI;MAAuB;;;;;;;AAO/B;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;;;;;;AAUA;;;;AAGA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;;AAeA;;;;;;;AAQA;;;;;;AAOA;;;;;;;AAQA;;;;;AAMA;;;;;;;;AASA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;EACE;;;;;AAIF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA"}}]}