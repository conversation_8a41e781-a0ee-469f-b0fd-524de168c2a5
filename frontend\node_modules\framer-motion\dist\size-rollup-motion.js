import{jsx as t,jsxs as e}from"react/jsx-runtime";import{createContext as n,useContext as i,useId as s,useEffect as o,useCallback as r,Component as a,useMemo as l,useLayoutEffect as h,useRef as u,useInsertionEffect as c,forwardRef as d,Fragment as p,createElement as m}from"react";function f(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function y(t){const e=[{},{}];return t?.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function g(t,e,n,i){if("function"==typeof e){const[s,o]=y(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=y(i);e=e(void 0!==n?n:t.custom,s,o)}return e}function v(t,e,n){const i=t.getProps();return g(i,e,void 0!==n?n:i.custom,t)}function x(t,e){-1===t.indexOf(e)&&t.push(e)}function T(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const w=(t,e,n)=>n>e?e:n<t?t:n;const P={},S=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),b=t=>/^0[^.\s]+$/u.test(t);function A(t){let e;return()=>(void 0===e&&(e=t()),e)}const V=t=>t,E=(t,e)=>n=>e(t(n)),M=(...t)=>t.reduce(E),D=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class C{constructor(){this.subscriptions=[]}add(t){return x(this.subscriptions,t),()=>T(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const k=t=>1e3*t,R=t=>t/1e3;function L(t,e){return e?t*(1e3/e):0}const j=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function B(t,e,n,i){if(t===e&&n===i)return V;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=j(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:j(s(t),e,i)}const F=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,O=t=>e=>1-t(1-e),I=B(.33,1.53,.69,.99),U=O(I),N=F(U),W=t=>(t*=2)<1?.5*U(t):.5*(2-Math.pow(2,-10*(t-1))),$=t=>1-Math.sin(Math.acos(t)),Y=O($),X=F($),K=B(.42,0,1,1),z=B(0,0,.58,1),H=B(.42,0,.58,1),q=t=>Array.isArray(t)&&"number"==typeof t[0],G={linear:V,easeIn:K,easeInOut:H,easeOut:z,circIn:$,circInOut:X,circOut:Y,backIn:U,backInOut:N,backOut:I,anticipate:W},_=t=>{if(q(t)){t.length;const[e,n,i,s]=t;return B(e,n,i,s)}return"string"==typeof t?G[t]:t},Z=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],J={value:null,addProjectionMetrics:null};function Q(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=Z.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){r.has(e)&&(u.schedule(e),t()),l++,e(a)}const u={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(h),e&&J.value&&J.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,u.process(t)))}};return u}(o,e?n:void 0),t)),{}),{setup:a,read:l,resolveKeyframes:h,preUpdate:u,update:c,preRender:d,render:p,postRender:m}=r,f=()=>{const o=P.useManualTiming?s.timestamp:performance.now();n=!1,P.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),h.process(s),u.process(s),c.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(f))};return{schedule:Z.reduce(((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(f)),a.schedule(e,o,r)),e}),{}),cancel:t=>{for(let e=0;e<Z.length;e++)r[Z[e]].cancel(t)},state:s,steps:r}}const{schedule:tt,cancel:et,state:nt,steps:it}=Q("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:V,!0);let st;function ot(){st=void 0}const rt={now:()=>(void 0===st&&rt.set(nt.isProcessing||P.useManualTiming?nt.timestamp:performance.now()),st),set:t=>{st=t,queueMicrotask(ot)}},at=t=>e=>"string"==typeof e&&e.startsWith(t),lt=at("--"),ht=at("var(--"),ut=t=>!!ht(t)&&ct.test(t.split("/*")[0].trim()),ct=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,dt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},pt={...dt,transform:t=>w(0,1,t)},mt={...dt,default:1},ft=t=>Math.round(1e5*t)/1e5,yt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const gt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,vt=(t,e)=>n=>Boolean("string"==typeof n&&gt.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),xt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(yt);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},Tt={...dt,transform:t=>Math.round((t=>w(0,255,t))(t))},wt={test:vt("rgb","red"),parse:xt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+Tt.transform(t)+", "+Tt.transform(e)+", "+Tt.transform(n)+", "+ft(pt.transform(i))+")"};const Pt={test:vt("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:wt.transform},St=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),bt=St("deg"),At=St("%"),Vt=St("px"),Et=St("vh"),Mt=St("vw"),Dt=(()=>({...At,parse:t=>At.parse(t)/100,transform:t=>At.transform(100*t)}))(),Ct={test:vt("hsl","hue"),parse:xt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+At.transform(ft(e))+", "+At.transform(ft(n))+", "+ft(pt.transform(i))+")"},kt={test:t=>wt.test(t)||Pt.test(t)||Ct.test(t),parse:t=>wt.test(t)?wt.parse(t):Ct.test(t)?Ct.parse(t):Pt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?wt.transform(t):Ct.transform(t)},Rt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Lt="number",jt="color",Bt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ft(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Bt,(t=>(kt.test(t)?(i.color.push(o),s.push(jt),n.push(kt.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(Lt),n.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function Ot(t){return Ft(t).values}function It(t){const{split:e,types:n}=Ft(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===Lt?ft(t[o]):e===jt?kt.transform(t[o]):t[o]}return s}}const Ut=t=>"number"==typeof t?0:t;const Nt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(yt)?.length||0)+(t.match(Rt)?.length||0)>0},parse:Ot,createTransformer:It,getAnimatableNone:function(t){const e=Ot(t);return It(t)(e.map(Ut))}};function Wt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function $t(t,e){return n=>n>0?e:t}const Yt=(t,e,n)=>t+(e-t)*n,Xt=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},Kt=[Pt,wt,Ct];function zt(t){const e=(n=t,Kt.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===Ct&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=Wt(a,i,t+1/3),o=Wt(a,i,t),r=Wt(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const Ht=(t,e)=>{const n=zt(t),i=zt(e);if(!n||!i)return $t(t,e);const s={...n};return t=>(s.red=Xt(n.red,i.red,t),s.green=Xt(n.green,i.green,t),s.blue=Xt(n.blue,i.blue,t),s.alpha=Yt(n.alpha,i.alpha,t),wt.transform(s))},qt=new Set(["none","hidden"]);function Gt(t,e){return n=>Yt(t,e,n)}function _t(t){return"number"==typeof t?Gt:"string"==typeof t?ut(t)?$t:kt.test(t)?Ht:Qt:Array.isArray(t)?Zt:"object"==typeof t?kt.test(t)?Ht:Jt:$t}function Zt(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>_t(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function Jt(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=_t(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const Qt=(t,e)=>{const n=Nt.createTransformer(e),i=Ft(t),s=Ft(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?qt.has(t)&&!s.values.length||qt.has(e)&&!i.values.length?function(t,e){return qt.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):M(Zt(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):$t(t,e)};function te(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Yt(t,e,n);return _t(t)(t,e)}const ee=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>tt.update(e,t),stop:()=>et(e),now:()=>nt.isProcessing?nt.timestamp:rt.now()}},ne=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=t(e/(s-1))+", ";return`linear(${i.substring(0,i.length-2)})`},ie=2e4;function se(t){let e=0;let n=t.next(e);for(;!n.done&&e<ie;)e+=50,n=t.next(e);return e>=ie?1/0:e}function oe(t,e,n){const i=Math.max(e-5,0);return L(n-t(i),e-i)}const re=100,ae=10,le=1,he=0,ue=800,ce=.3,de=.3,pe={granular:.01,default:2},me={granular:.005,default:.5},fe=.01,ye=10,ge=.05,ve=1,xe=.001;function Te({duration:t=ue,bounce:e=ce,velocity:n=he,mass:i=le}){let s,o,r=1-e;r=w(ge,ve,r),t=w(fe,ye,R(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=Pe(e,r),l=Math.exp(-s);return xe-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),h=Pe(Math.pow(e,2),r);return(-s(e)+xe>0?-1:1)*((o-a)*l)/h}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<we;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=k(t),isNaN(a))return{stiffness:re,damping:ae,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const we=12;function Pe(t,e){return t*Math.sqrt(1-e*e)}const Se=["duration","bounce"],be=["stiffness","damping","mass"];function Ae(t,e){return e.some((e=>void 0!==t[e]))}function Ve(t=de,e=ce){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:h,mass:u,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:he,stiffness:re,damping:ae,mass:le,isResolvedFromDuration:!1,...t};if(!Ae(t,be)&&Ae(t,Se))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*w(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:le,stiffness:s,damping:o}}else{const n=Te(t);e={...e,...n,mass:le},e.isResolvedFromDuration=!0}return e}({...n,velocity:-R(n.velocity||0)}),m=d||0,f=h/(2*Math.sqrt(l*u)),y=r-o,g=R(Math.sqrt(l/u)),v=Math.abs(y)<5;let x;if(i||(i=v?pe.granular:pe.default),s||(s=v?me.granular:me.default),f<1){const t=Pe(g,f);x=e=>{const n=Math.exp(-f*g*e);return r-n*((m+f*g*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-g*t)*(y+(m+g*y)*t);else{const t=g*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*g*e),i=Math.min(t*e,300);return r-n*((m+f*g*y)*Math.sinh(i)+t*y*Math.cosh(i))/t}}const T={calculatedDuration:p&&c||null,next:t=>{const e=x(t);if(p)a.done=t>=c;else{let n=0===t?m:0;f<1&&(n=0===t?k(m):oe(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(se(T),ie),e=ne((e=>T.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return T}function Ee({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:h=.5,restSpeed:u}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=c+m,y=void 0===r?f:r(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/i),v=t=>y+g(t),x=t=>{const e=g(t),n=v(t);d.done=Math.abs(e)<=h,d.value=d.done?y:n};let T,w;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(T=t,w=Ve({keyframes:[d.value,p(d.value)],velocity:oe(v,t,d.value),damping:s,stiffness:o,restDelta:h,restSpeed:u}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==T||(e=!0,x(t),P(t)),void 0!==T&&t>=T?w.next(t-T):(!e&&x(t),d)}}}function Me(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||P.mix||te,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||V:e;o=M(t,o)}i.push(o)}return i}(e,i,s),l=a.length,h=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=D(t[i],t[i+1],n);return a[i](s)};return n?e=>h(w(t[0],t[o-1],e)):h}function De(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=D(0,e,i);t.push(Yt(n,1,s))}}(e,t.length-1),e}function Ce({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(_):_(i),o={done:!1,value:e[0]},r=function(t,e){return t.map((t=>t*e))}(n&&n.length===e.length?n:De(e),t),a=Me(r,e,{ease:Array.isArray(s)?s:(l=e,h=s,l.map((()=>h||H)).splice(0,l.length-1))});var l,h;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}Ve.applyToOptions=t=>{const e=function(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(se(i),ie);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:R(s)}}(t,100,Ve);return t.ease=e.ease,t.duration=k(e.duration),t.type="keyframes",t};const ke=t=>null!==t;function Re(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(ke),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const Le={decay:Ee,inertia:Ee,tween:Ce,keyframes:Ce,spring:Ve};function je(t){"string"==typeof t.type&&(t.type=Le[t.type])}class Be{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Fe=t=>t/100;class Oe extends Be{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){const{motionValue:t}=this.options;t&&t.updatedAt!==rt.now()&&this.tick(rt.now())}if(this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:e}=this.options;e&&e()},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;je(t);const{type:e=Ce,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Ce;a!==Ce&&"number"!=typeof r[0]&&(this.mixKeyframes=M(Fe,te(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=se(l));const{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:h,repeat:u,repeatType:c,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>i;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let v=this.currentTime,x=n;if(u){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,u+1);Boolean(e%2)&&("reverse"===c?(n=1-n,d&&(n-=d/r)):"mirror"===c&&(x=o)),v=w(0,1,n)*r}const T=g?{done:!1,value:h[0]}:x.next(v);s&&(T.value=s(T.value));let{done:P}=T;g||null===a||(P=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return S&&p!==Ee&&(T.value=Re(h,this.options,f,this.speed)),m&&m(T.value),S&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return R(this.calculatedDuration)}get time(){return R(this.currentTime)}set time(t){t=k(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(rt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=R(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=ee,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=n??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(rt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const Ie=t=>180*t/Math.PI,Ue=t=>{const e=Ie(Math.atan2(t[1],t[0]));return We(e)},Ne={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Ue,rotateZ:Ue,skewX:t=>Ie(Math.atan(t[1])),skewY:t=>Ie(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},We=t=>((t%=360)<0&&(t+=360),t),$e=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Ye=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Xe={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:$e,scaleY:Ye,scale:t=>($e(t)+Ye(t))/2,rotateX:t=>We(Ie(Math.atan2(t[6],t[5]))),rotateY:t=>We(Ie(Math.atan2(-t[2],t[0]))),rotateZ:Ue,rotate:Ue,skewX:t=>Ie(Math.atan(t[4])),skewY:t=>Ie(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Ke(t){return t.includes("scale")?1:0}function ze(t,e){if(!t||"none"===t)return Ke(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=Xe,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=Ne,s=e}if(!s)return Ke(e);const o=i[e],r=s[1].split(",").map(He);return"function"==typeof o?o(r):r[o]}function He(t){return parseFloat(t.trim())}const qe=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ge=(()=>new Set(qe))(),_e=t=>t===dt||t===Vt,Ze=new Set(["x","y","z"]),Je=qe.filter((t=>!Ze.has(t)));const Qe={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ze(e,"x"),y:(t,{transform:e})=>ze(e,"y")};Qe.translateX=Qe.x,Qe.translateY=Qe.y;const tn=new Set;let en=!1,nn=!1,sn=!1;function on(){if(nn){const t=Array.from(tn).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return Je.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}nn=!1,en=!1,tn.forEach((t=>t.complete(sn))),tn.clear()}function rn(){tn.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(nn=!0)}))}class an{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tn.add(this),en||(en=!0,tt.read(rn),tt.resolveKeyframes(on))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tn.delete(this)}cancel(){"scheduled"===this.state&&(tn.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const ln=A((()=>void 0!==window.ScrollTimeline)),hn={};function un(t,e){const n=A(t);return()=>hn[e]??n()}const cn=un((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),dn=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,pn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:dn([0,.65,.55,1]),circOut:dn([.55,0,1,.45]),backIn:dn([.31,.01,.66,-.59]),backOut:dn([.33,1.53,.69,.99])};function mn(t,e){return t?"function"==typeof t?cn()?ne(t,e):"ease-out":q(t)?dn(t):Array.isArray(t)?t.map((t=>mn(t,e)||pn.easeOut)):pn[t]:void 0}function fn(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},h=void 0){const u={[e]:n};l&&(u.offset=l);const c=mn(a,s);Array.isArray(c)&&(u.easing=c);const d={delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};h&&(d.pseudoElement=h);return t.animate(u,d)}function yn(t){return"function"==typeof t&&"applyToOptions"in t}class gn extends Be{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=function({type:t,...e}){return yn(t)&&cn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=fn(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=Re(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return R(Number(t))}get time(){return R(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=k(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&ln()?(this.animation.timeline=t,V):e(this)}}const vn={anticipate:W,backInOut:N,circInOut:X};function xn(t){"string"==typeof t.ease&&t.ease in vn&&(t.ease=vn[t.ease])}class Tn extends gn{constructor(t){xn(t),je(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new Oe({...o,autoplay:!1}),a=k(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const wn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Nt.test(t)&&"0"!==t||t.startsWith("url(")));const Pn=new Set(["opacity","clipPath","filter","transform"]),Sn=A((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));class bn extends Be{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=rt.now();const c={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:h,...u},d=h?.KeyframeResolver||an;this.keyframeResolver=new d(r,((t,e,n)=>this.onKeyframesResolved(t,e,c,!n)),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:h}=n;this.resolvedAt=rt.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=wn(s,e),a=wn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||yn(n))&&i)}(t,s,o,r)||(!P.instantAnimations&&a||h?.(Re(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const u={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},c=!l&&function(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Sn()&&n&&Pn.has(n)&&("transform"!==n||!l)&&!a&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}(u)?new Tn({...u,element:u.motionValue.owner.current}):new Oe(u);c.finished.then((()=>this.notifyFinished())).catch(V),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(this.keyframeResolver?.resume(),sn=!0,rn(),on(),sn=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const An=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Vn(t,e,n=1){const[i,s]=function(t){const e=An.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return S(t)?parseFloat(t):t}return ut(s)?Vn(s,e,n+1):s}function En(t,e){return t?.[e]??t?.default??t}const Mn=new Set(["width","height","top","left","right","bottom",...qe]),Dn=t=>e=>e.test(t),Cn=[dt,Vt,At,bt,Mt,Et,{test:t=>"auto"===t,parse:t=>t}],kn=t=>Cn.find(Dn(t));const Rn=new Set(["brightness","contrast","saturate","opacity"]);function Ln(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(yt)||[];if(!i)return t;const s=n.replace(i,"");let o=Rn.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const jn=/\b([a-z-]*)\(.*?\)/gu,Bn={...Nt,getAnimatableNone:t=>{const e=t.match(jn);return e?e.map(Ln).join(" "):t}},Fn={...dt,transform:Math.round},On={borderWidth:Vt,borderTopWidth:Vt,borderRightWidth:Vt,borderBottomWidth:Vt,borderLeftWidth:Vt,borderRadius:Vt,radius:Vt,borderTopLeftRadius:Vt,borderTopRightRadius:Vt,borderBottomRightRadius:Vt,borderBottomLeftRadius:Vt,width:Vt,maxWidth:Vt,height:Vt,maxHeight:Vt,top:Vt,right:Vt,bottom:Vt,left:Vt,padding:Vt,paddingTop:Vt,paddingRight:Vt,paddingBottom:Vt,paddingLeft:Vt,margin:Vt,marginTop:Vt,marginRight:Vt,marginBottom:Vt,marginLeft:Vt,backgroundPositionX:Vt,backgroundPositionY:Vt,...{rotate:bt,rotateX:bt,rotateY:bt,rotateZ:bt,scale:mt,scaleX:mt,scaleY:mt,scaleZ:mt,skew:bt,skewX:bt,skewY:bt,distance:Vt,translateX:Vt,translateY:Vt,translateZ:Vt,x:Vt,y:Vt,z:Vt,perspective:Vt,transformPerspective:Vt,opacity:pt,originX:Dt,originY:Dt,originZ:Vt},zIndex:Fn,fillOpacity:pt,strokeOpacity:pt,numOctaves:Fn},In={...On,color:kt,backgroundColor:kt,outlineColor:kt,fill:kt,stroke:kt,borderColor:kt,borderTopColor:kt,borderRightColor:kt,borderBottomColor:kt,borderLeftColor:kt,filter:Bn,WebkitFilter:Bn},Un=t=>In[t];function Nn(t,e){let n=Un(t);return n!==Bn&&(n=Nt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Wn=new Set(["auto","none","0"]);class $n extends an{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),ut(i))){const s=Vn(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!Mn.has(n)||2!==t.length)return;const[i,s]=t,o=kn(i),r=kn(s);if(o!==r)if(_e(o)&&_e(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else Qe[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||b(i)))&&n.push(e);var i;n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Wn.has(e)&&Ft(e).values.length&&(i=t[s]),s++}if(i&&n)for(const s of e)t[s]=Nn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Qe[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,o=n[s];n[s]=Qe[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}}class Yn{constructor(t,e={}){this.version="__VERSION__",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=rt.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=rt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new C);const n=this.events[t].add(e);return"change"===t?()=>{n(),tt.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=rt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return L(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Xn(t,e){return new Yn(t,e)}const Kn=(t,e)=>e&&"number"==typeof t?e.transform(t):t,{schedule:zn,cancel:Hn}=Q(queueMicrotask,!1),qn={x:!1,y:!1};function Gn(){return qn.x||qn.y}function _n(t,e){const n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Zn(t){return!("touch"===t.pointerType||Gn())}const Jn=(t,e)=>!!e&&(t===e||Jn(t,e.parentElement)),Qn=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,ti=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const ei=new WeakSet;function ni(t){return e=>{"Enter"===e.key&&t(e)}}function ii(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function si(t){return Qn(t)&&!Gn()}function oi(t,e,n={}){const[i,s,o]=_n(t,n),r=t=>{const i=t.currentTarget;if(!si(t)||ei.has(i))return;ei.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ei.has(i)&&ei.delete(i),si(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||Jn(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),t instanceof HTMLElement&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=ni((()=>{if(ei.has(n))return;ii(n,"down");const t=ni((()=>{ii(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>ii(n,"cancel")),e)}));n.addEventListener("keydown",i,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",i)),e)})(t,s))),e=t,ti.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),o}const ri=[...Cn,kt,Nt],ai=t=>Array.isArray(t);function li(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Xn(n))}const hi=t=>Boolean(t&&t.getVelocity);function ui(t,e){const n=t.getValue("willChange");if(i=n,Boolean(hi(i)&&i.add))return n.add(e);if(!n&&P.WillChange){const n=new P.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}const ci=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),di="data-"+ci("framerAppearId");function pi(t){return t.props[di]}const mi=t=>null!==t;const fi={type:"spring",stiffness:500,damping:25,restSpeed:10},yi={type:"keyframes",duration:.8},gi={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},vi=(t,{keyframes:e})=>e.length>2?yi:Ge.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:fi:gi;const xi=(t,e,n,i={},s,o)=>r=>{const a=En(i,t)||{},l=a.delay||i.delay||0;let{elapsed:h=0}=i;h-=k(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-h,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length})(a)||Object.assign(u,vi(t,u)),u.duration&&(u.duration=k(u.duration)),u.repeatDelay&&(u.repeatDelay=k(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let c=!1;if((!1===u.type||0===u.duration&&!u.repeatDelay)&&(u.duration=0,0===u.delay&&(c=!0)),(P.instantAnimations||P.skipAnimations)&&(c=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,c&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(mi),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}(u.keyframes,a);if(void 0!==t)return void tt.update((()=>{u.onUpdate(t),u.onComplete()}))}return a.isSync?new Oe(u):new bn(u)};function Ti({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function wi(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],h=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const i=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||h&&Ti(h,e))continue;const r={delay:n,...En(o||{},e)},u=i.get();if(void 0!==u&&!i.isAnimating&&!Array.isArray(s)&&s===u&&!r.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const n=pi(t);if(n){const t=window.MotionHandoffAnimation(n,e,tt);null!==t&&(r.startTime=t,c=!0)}}ui(t,e),i.start(xi(e,i,s,t.shouldReduceMotion&&Mn.has(e)?{type:!1}:r,t,c));const d=i.animation;d&&l.push(d)}return r&&Promise.all(l).then((()=>{tt.update((()=>{r&&function(t,e){const n=v(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o)li(t,e,(r=o[e],ai(r)?r[r.length-1]||0:r));var r}(t,r)}))})),l}function Pi(t,e,n={}){const i=v(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(wi(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(Si).forEach(((t,i)=>{t.notify("AnimationStart",e),r.push(Pi(t,e,{...o,delay:n+l(i)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,o+i,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then((()=>e()))}return Promise.all([o(),r(n.delay)])}function Si(t,e){return t.sortNodePosition(e)}function bi(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function Ai(t){return"string"==typeof t||Array.isArray(t)}const Vi=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ei=["initial",...Vi],Mi=Ei.length;function Di(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Di(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Mi;n++){const i=Ei[n],s=t.props[i];(Ai(s)||!1===s)&&(e[i]=s)}return e}const Ci=[...Vi].reverse(),ki=Vi.length;function Ri(t){return e=>Promise.all(e.map((({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map((e=>Pi(t,e,n)));i=Promise.all(s)}else if("string"==typeof e)i=Pi(t,e,n);else{const s="function"==typeof e?v(t,e,n.custom):e;i=Promise.all(wi(t,s,n))}return i.then((()=>{t.notify("AnimationComplete",e)}))}(t,e,n))))}function Li(t){let e=Ri(t),n=Fi(),i=!0;const s=e=>(n,i)=>{const s=v(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...i}=s;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=Di(t.parent)||{},l=[],h=new Set;let u={},c=1/0;for(let e=0;e<ki;e++){const d=Ci[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],y=Ai(m),g=d===o?p.isActive:null;!1===g&&(c=e);let v=m===a[d]&&m!==r[d]&&y;if(v&&i&&t.manuallyAnimateOnMount&&(v=!1),p.protectedKeys={...u},!p.isActive&&null===g||!m&&!p.prevProp||f(m)||"boolean"==typeof m)continue;const x=ji(p.prevProp,m);let T=x||d===o&&p.isActive&&!v&&y||e>c&&y,w=!1;const P=Array.isArray(m)?m:[m];let S=P.reduce(s(d),{});!1===g&&(S={});const{prevResolvedValues:b={}}=p,A={...b,...S},V=e=>{T=!0,h.has(e)&&(w=!0,h.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in A){const e=S[t],n=b[t];if(u.hasOwnProperty(t))continue;let i=!1;i=ai(e)&&ai(n)?!bi(e,n):e!==n,i?null!=e?V(t):h.add(t):void 0!==e&&h.has(t)?V(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=S,p.isActive&&(u={...u,...S}),i&&t.blockInitialAnimation&&(T=!1);T&&(!(v&&x)||w)&&l.push(...P.map((t=>({animation:t,options:{type:d}}))))}if(h.size){const e={};if("boolean"!=typeof r.initial){const n=v(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}h.forEach((n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null})),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){if(n[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach((t=>t.animationState?.setActive(e,i))),n[e].isActive=i;const s=o(e);for(const t in n)n[t].protectedKeys={};return s},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Fi(),i=!0}}}function ji(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!bi(e,t)}function Bi(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Fi(){return{animate:Bi(!0),whileInView:Bi(),whileHover:Bi(),whileTap:Bi(),whileDrag:Bi(),whileFocus:Bi(),exit:Bi()}}class Oi{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Ii=0;const Ui={animation:{Feature:class extends Oi{constructor(t){super(t),t.animationState||(t.animationState=Li(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();f(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Oi{constructor(){super(...arguments),this.id=Ii++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then((()=>{e(this.id)}))}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Ni(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function Wi(t){return{point:{x:t.pageX,y:t.pageY}}}function $i(t,e,n,i){return Ni(t,e,(t=>e=>Qn(e)&&t(e,Wi(e)))(n),i)}function Yi({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Xi(t){return t.max-t.min}function Ki(t,e,n,i=.5){t.origin=i,t.originPoint=Yt(e.min,e.max,t.origin),t.scale=Xi(n)/Xi(e),t.translate=Yt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function zi(t,e,n,i){Ki(t.x,e.x,n.x,i?i.originX:void 0),Ki(t.y,e.y,n.y,i?i.originY:void 0)}function Hi(t,e,n){t.min=n.min+e.min,t.max=t.min+Xi(e)}function qi(t,e,n){t.min=e.min-n.min,t.max=t.min+Xi(e)}function Gi(t,e,n){qi(t.x,e.x,n.x),qi(t.y,e.y,n.y)}const _i=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Zi(t){return[t("x"),t("y")]}function Ji(t){return void 0===t||1===t}function Qi({scale:t,scaleX:e,scaleY:n}){return!Ji(t)||!Ji(e)||!Ji(n)}function ts(t){return Qi(t)||es(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function es(t){return ns(t.x)||ns(t.y)}function ns(t){return t&&"0%"!==t}function is(t,e,n){return n+e*(t-n)}function ss(t,e,n,i,s){return void 0!==s&&(t=is(t,s,i)),is(t,n,i)+e}function os(t,e=0,n=1,i,s){t.min=ss(t.min,e,n,i,s),t.max=ss(t.max,e,n,i,s)}function rs(t,{x:e,y:n}){os(t.x,e.translate,e.scale,e.originPoint),os(t.y,n.translate,n.scale,n.originPoint)}const as=.999999999999,ls=1.0000000000001;function hs(t,e){t.min=t.min+e,t.max=t.max+e}function us(t,e,n,i,s=.5){os(t,e,n,Yt(t.min,t.max,s),i)}function cs(t,e){us(t.x,e.x,e.scaleX,e.scale,e.originX),us(t.y,e.y,e.scaleY,e.scale,e.originY)}function ds(t,e){return Yi(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const ps=({current:t})=>t?t.ownerDocument.defaultView:null;function ms(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}const fs=(t,e)=>Math.abs(t-e);class ys{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=xs(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=fs(t.x,e.x),i=fs(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=nt;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=gs(e,this.transformPagePoint),tt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=xs("pointercancel"===t.type?this.lastMoveEventInfo:gs(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!Qn(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=gs(Wi(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=nt;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,xs(o,this.history)),this.removeListeners=M($i(this.contextWindow,"pointermove",this.handlePointerMove),$i(this.contextWindow,"pointerup",this.handlePointerUp),$i(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),et(this.updatePoint)}}function gs(t,e){return e?{point:e(t.point)}:t}function vs(t,e){return{x:t.x-e.x,y:t.y-e.y}}function xs({point:t},e){return{point:t,delta:vs(t,ws(e)),offset:vs(t,Ts(e)),velocity:Ps(e,.1)}}function Ts(t){return t[0]}function ws(t){return t[t.length-1]}function Ps(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=ws(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>k(e)));)n--;if(!i)return{x:0,y:0};const o=R(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Ss(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function bs(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const As=.35;function Vs(t,e,n){return{min:Es(t,e),max:Es(t,n)}}function Es(t,e){return"number"==typeof t?t:t[e]||0}const Ms=new WeakMap;class Ds{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new ys(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Wi(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?qn[o]?null:(qn[o]=!0,()=>{qn[o]=!1}):qn.x||qn.y?null:(qn.x=qn.y=!0,()=>{qn.x=qn.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Zi((t=>{let e=this.getAxisMotionValue(t).get()||0;if(At.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Xi(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),s&&tt.postRender((()=>s(t,e))),ui(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Zi((t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:ps(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&tt.postRender((()=>s(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!Cs(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Yt(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Yt(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&ms(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!n)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Ss(t.x,n,s),y:Ss(t.y,e,i)}}(n.layoutBox,t),this.elastic=function(t=As){return!1===t?t=0:!0===t&&(t=As),{x:Vs(t,"left","right"),y:Vs(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&Zi((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!ms(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=ds(t,n),{scroll:s}=e;return s&&(hs(i.x,s.offset.x),hs(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:bs(t.x,e.x),y:bs(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Yi(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Zi((r=>{if(!Cs(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const h=i?200:1e6,u=i?40:1e7,c={type:"inertia",velocity:n?t[r]:0,bounceStiffness:h,bounceDamping:u,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,c)}));return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return ui(this.visualElement,t),n.start(xi(t,n,0,e,this.visualElement,!1))}stopAnimation(){Zi((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){Zi((t=>this.getAxisMotionValue(t).animation?.pause()))}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Zi((e=>{const{drag:n}=this.getProps();if(!Cs(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Yt(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!ms(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Zi((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Xi(t),s=Xi(e);return s>i?n=D(e.min,e.max-i,t.min):i>s&&(n=D(t.min,t.max-s,e.min)),w(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Zi((e=>{if(!Cs(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Yt(s,o,i[e]))}))}addListeners(){if(!this.visualElement.current)return;Ms.set(this.visualElement,this);const t=$i(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();ms(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),tt.read(e);const s=Ni(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Zi((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=As,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function Cs(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const ks=t=>(e,n)=>{t&&tt.postRender((()=>t(e,n)))};const Rs=(t,e)=>t.depth-e.depth;class Ls{constructor(){this.children=[],this.isDirty=!1}add(t){x(this.children,t),this.isDirty=!0}remove(t){T(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Rs),this.isDirty=!1,this.children.forEach(t)}}function js(t){return hi(t)?t.get():t}const Bs=["TopLeft","TopRight","BottomLeft","BottomRight"],Fs=Bs.length,Os=t=>"string"==typeof t?parseFloat(t):t,Is=t=>"number"==typeof t||Vt.test(t);function Us(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Ns=$s(0,.5,Y),Ws=$s(.5,.95,V);function $s(t,e,n){return i=>i<t?0:i>e?1:n(D(t,e,i))}function Ys(t,e){t.min=e.min,t.max=e.max}function Xs(t,e){Ys(t.x,e.x),Ys(t.y,e.y)}function Ks(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function zs(t,e,n,i,s){return t=is(t-=e,1/n,i),void 0!==s&&(t=is(t,1/s,i)),t}function Hs(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){At.test(e)&&(e=parseFloat(e),e=Yt(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Yt(o.min,o.max,i);t===o&&(a-=e),t.min=zs(t.min,e,n,a,s),t.max=zs(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const qs=["x","scaleX","originX"],Gs=["y","scaleY","originY"];function _s(t,e,n,i){Hs(t.x,e,qs,n?n.x:void 0,i?i.x:void 0),Hs(t.y,e,Gs,n?n.y:void 0,i?i.y:void 0)}function Zs(t){return 0===t.translate&&1===t.scale}function Js(t){return Zs(t.x)&&Zs(t.y)}function Qs(t,e){return t.min===e.min&&t.max===e.max}function to(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function eo(t,e){return to(t.x,e.x)&&to(t.y,e.y)}function no(t){return Xi(t.x)/Xi(t.y)}function io(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class so{constructor(){this.members=[]}add(t){x(this.members,t),t.scheduleRender()}remove(t){if(T(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const oo={};const ro={hasAnimatedSinceResize:!0,hasEverUpdated:!1},ao=["","X","Y","Z"],lo={visibility:"hidden"};let ho=0;function uo(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function co(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=pi(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",tt,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&co(i)}function po({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=e?.()){this.id=ho++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(yo),this.nodes.forEach(So),this.nodes.forEach(bo),this.nodes.forEach(go)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ls)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new C),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var n;this.isSVG=(n=e)instanceof SVGElement&&"svg"!==n.tagName,this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=rt.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(et(i),t(o-e))};return tt.setup(i,!0),()=>et(i)}(i,250),ro.hasAnimatedSinceResize&&(ro.hasAnimatedSinceResize=!1,this.nodes.forEach(Po))}))}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||Co,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!eo(this.targetLayout,i),h=!e&&n;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);const e={...En(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||Po(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),et(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Ao),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&co(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(xo);this.isUpdating||this.nodes.forEach(To),this.isUpdating=!1,this.nodes.forEach(wo),this.nodes.forEach(mo),this.nodes.forEach(fo),this.clearAllSnapshots();const t=rt.now();nt.delta=w(0,1e3/60,t-nt.timestamp),nt.timestamp=t,nt.isProcessing=!0,it.update.process(nt),it.preRender.process(nt),it.render.process(nt),nt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,zn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(vo),this.sharedNodes.forEach(Vo)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tt.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Xi(this.snapshot.measuredBox.x)||Xi(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!Js(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||ts(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Lo((i=n).x),Lo(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Bo))){const{scroll:t}=this.root;t&&(hs(e.x,t.offset.x),hs(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(Xs(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Xs(e,t),hs(e.x,s.offset.x),hs(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Xs(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&cs(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),ts(i.latestValues)&&cs(n,i.latestValues)}return ts(this.latestValues)&&cs(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Xs(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!ts(n.latestValues))continue;Qi(n.latestValues)&&n.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};Xs(i,n.measurePageBox()),_s(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return ts(this.latestValues)&&_s(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==nt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const n=Boolean(this.resumingFrom)||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:s}=this.options;if(this.layout&&(i||s)){if(this.resolvedRelativeTargetAt=nt.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Gi(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Xs(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var o,r,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,Hi(o.x,r.x,a.x),Hi(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Xs(this.target,this.layout.layoutBox),rs(this.target,this.targetDelta)):Xs(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Gi(this.relativeTargetOrigin,this.target,t.target),Xs(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!Qi(this.parent.latestValues)&&!es(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===nt.timestamp&&(n=!1),n)return;const{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!s)return;Xs(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&cs(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,rs(t,r)),i&&ts(o.latestValues)&&cs(t,o.latestValues))}e.x<ls&&e.x>as&&(e.x=1),e.y<ls&&e.y>as&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(Ks(this.prevProjectionDelta.x,this.projectionDelta.x),Ks(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),zi(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&io(this.projectionDelta.x,this.prevProjectionDelta.x)&&io(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),h=!l||l.members.length<=1,u=Boolean(a&&!h&&!0===this.options.crossfade&&!this.path.some(Do));let c;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,y;Eo(o.x,t.x,n),Eo(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Gi(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,y=n,Mo(p.x,m.x,f.x,y),Mo(p.y,m.y,f.y,y),c&&(l=this.relativeTarget,d=c,Qs(l.x,d.x)&&Qs(l.y,d.y))&&(this.isProjectionDirty=!1),c||(c={x:{min:0,max:0},y:{min:0,max:0}}),Xs(c,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Yt(0,n.opacity??1,Ns(i)),t.opacityExit=Yt(e.opacity??1,0,Ws(i))):o&&(t.opacity=Yt(e.opacity??1,n.opacity??1,i));for(let s=0;s<Fs;s++){const o=`border${Bs[s]}Radius`;let r=Us(e,o),a=Us(n,o);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||Is(r)===Is(a)?(t[o]=Math.max(Yt(Os(r),Os(a),i),0),(At.test(a)||At.test(r))&&(t[o]+="%")):t[o]=a)}(e.rotate||n.rotate)&&(t.rotate=Yt(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,u,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(et(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tt.update((()=>{ro.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Xn(0)),this.currentAnimation=function(t,e,n){const i=hi(t)?t:Xn(t);return i.start(xi("",i,e,n)),i.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&jo(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Xi(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Xi(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Xs(e,n),cs(e,s),zi(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new so);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&uo("z",t,i,this.animationValues);for(let e=0;e<ao.length;e++)uo(`rotate${ao[e]}`,t,i,this.animationValues),uo(`skew${ao[e]}`,t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return lo;const e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=js(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=js(t?.pointerEvents)||""),this.hasProjected&&!ts(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}const s=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),n&&(e.transform=n(s,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,i.animationValues?e.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const t in oo){if(void 0===s[t])continue;const{correct:n,applyTo:o,isCSSVariable:r}=oo[t],a="none"===e.transform?s[t]:n(s[t],i);if(o){const t=o.length;for(let n=0;n<t;n++)e[o[n]]=a}else r?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=i===this?js(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>t.currentAnimation?.stop(!1))),this.root.nodes.forEach(xo),this.root.sharedNodes.clear()}}}function mo(t){t.updateLayout()}function fo(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:i}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?Zi((t=>{const i=o?e.measuredBox[t]:e.layoutBox[t],s=Xi(i);i.min=n[t].min,i.max=i.min+s})):jo(s,e.layoutBox,n)&&Zi((i=>{const s=o?e.measuredBox[i]:e.layoutBox[i],r=Xi(n[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};zi(r,n,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?zi(a,t.applyTransform(i,!0),e.measuredBox):zi(a,n,e.layoutBox);const l=!Js(r);let h=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Gi(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Gi(a,n,o.layoutBox),eo(r,a)||(h=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function yo(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function go(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function vo(t){t.clearSnapshot()}function xo(t){t.clearMeasurements()}function To(t){t.isLayoutDirty=!1}function wo(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Po(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function So(t){t.resolveTargetDelta()}function bo(t){t.calcProjection()}function Ao(t){t.resetSkewAndRotation()}function Vo(t){t.removeLeadSnapshot()}function Eo(t,e,n){t.translate=Yt(e.translate,0,n),t.scale=Yt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Mo(t,e,n,i){t.min=Yt(e.min,n.min,i),t.max=Yt(e.max,n.max,i)}function Do(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Co={duration:.45,ease:[.4,0,.1,1]},ko=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Ro=ko("applewebkit/")&&!ko("chrome/")?Math.round:V;function Lo(t){t.min=Ro(t.min),t.max=Ro(t.max)}function jo(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=no(e),s=no(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Bo(t){return t!==t.root&&t.scroll?.wasRoot}const Fo=po({attachResizeListener:(t,e)=>Ni(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Oo={current:void 0},Io=po({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Oo.current){const t=new Fo({});t.mount(window),t.setOptions({layoutScroll:!0}),Oo.current=t}return Oo.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function Uo(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const No={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Vt.test(t))return t;t=parseFloat(t)}return`${Uo(t,e.target.x)}% ${Uo(t,e.target.y)}%`}},Wo={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=Nt.parse(t);if(s.length>5)return i;const o=Nt.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const h=Yt(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=h),"number"==typeof s[3+r]&&(s[3+r]/=h),o(s)}},$o={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Yo={};for(const t in $o)Yo[t]={isEnabled:e=>$o[t].some((t=>!!e[t]))};const Xo="undefined"!=typeof window,Ko={current:null},zo={current:!1};const Ho=new WeakMap;function qo(t){return f(t.animate)||Ei.some((e=>Ai(t[e])))}function Go(t){return Boolean(qo(t)||t.variants)}const _o=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Zo{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=an,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=rt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,tt.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=qo(e),this.isVariantNode=Go(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in u){const e=u[t];void 0!==a[t]&&hi(e)&&e.set(a[t],!1)}}mount(t){this.current=t,Ho.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),zo.current||function(){if(zo.current=!0,Xo)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ko.current=t.matches;t.addListener(e),e()}else Ko.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Ko.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),et(this.notifyUpdate),et(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Ge.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&tt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{i(),s(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in Yo){const e=Yo[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<_o.length;e++){const n=_o[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(hi(s))t.addValue(i,s);else if(hi(o))t.addValue(i,Xn(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,Xn(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Xn(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var i;return null!=n&&("string"==typeof n&&(S(n)||b(n))?n=parseFloat(n):(i=n,!ri.find(Dn(i))&&Nt.test(e)&&(n=Nn(t,e))),this.setBaseTarget(t,hi(n)?n.get():n)),hi(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const i=g(this.props,e,this.presenceContext?.custom);i&&(n=i[t])}if(e&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,t);return void 0===i||hi(i)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new C),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Jo extends Zo{constructor(){super(...arguments),this.KeyframeResolver=$n}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;hi(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}const Qo={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tr=qe.length;function er(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const n=e[t];if(Ge.has(t))r=!0;else if(lt(t))s[t]=n;else{const e=Kn(n,On[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<tr;o++){const r=qe[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=Kn(a,On[r]);l||(s=!1,i+=`${Qo[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}function nr(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const e in n)t.style.setProperty(e,n[e])}function ir(t,{layout:e,layoutId:n}){return Ge.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!oo[t]||"opacity"===t)}function sr(t,e,n){const{style:i}=t,s={};for(const o in i)(hi(i[o])||e.style&&hi(e.style[o])||ir(o,t)||void 0!==n?.getValue(o)?.liveStyle)&&(s[o]=i[o]);return s}class or extends Jo{constructor(){super(...arguments),this.type="html",this.renderInstance=nr}readValueFromInstance(t,e){if(Ge.has(e))return((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return ze(n,e)})(t,e);{const i=(n=t,window.getComputedStyle(n)),s=(lt(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return ds(t,e)}build(t,e,n){er(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return sr(t,e,n)}}const rr=n(null);const ar=n({}),lr=n({});class hr extends a{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;!function(t){for(const e in t)oo[e]=t[e],lt(e)&&(oo[e].isCSSVariable=!0)}(cr),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",(()=>{this.safeToRemove()})),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),ro.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||tt.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),zn.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ur(e){const[n,a]=function(t=!0){const e=i(rr);if(null===e)return[!0,null];const{isPresent:n,onExitComplete:a,register:l}=e,h=s();o((()=>{if(t)return l(h)}),[t]);const u=r((()=>t&&a&&a(h)),[h,a,t]);return!n&&a?[!1,u]:[!0]}(),l=i(ar);return t(hr,{...e,layoutGroup:l,switchLayoutGroup:i(lr),isPresent:n,safeToRemove:a})}const cr={borderRadius:{...No,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:No,borderTopRightRadius:No,borderBottomLeftRadius:No,borderBottomRightRadius:No,boxShadow:Wo},dr={pan:{Feature:class extends Oi{constructor(){super(...arguments),this.removePointerDownListener=V}onPointerDown(t){this.session=new ys(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ps(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:ks(t),onStart:ks(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&tt.postRender((()=>i(t,e)))}}}mount(){this.removePointerDownListener=$i(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Oi{constructor(t){super(t),this.removeGroupControls=V,this.removeListeners=V,this.controls=new Ds(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||V}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Io,MeasureLayout:ur}};function pr(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&tt.postRender((()=>s(e,Wi(e))))}function mr(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&tt.postRender((()=>s(e,Wi(e))))}const fr=new WeakMap,yr=new WeakMap,gr=t=>{const e=fr.get(t.target);e&&e(t)},vr=t=>{t.forEach(gr)};function xr(t,e,n){const i=function({root:t,...e}){const n=t||document;yr.has(n)||yr.set(n,{});const i=yr.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(vr,{root:t,...e})),i[s]}(e);return fr.set(t,n),i.observe(t),()=>{fr.delete(t),i.unobserve(t)}}const Tr={some:0,all:1};const wr={inView:{Feature:class extends Oi{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:Tr[i]};return xr(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Oi{mount(){const{current:t}=this.node;t&&(this.unmount=oi(t,((t,e)=>(mr(this.node,e,"Start"),(t,{success:e})=>mr(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Oi{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=M(Ni(this.node.current,"focus",(()=>this.onFocus())),Ni(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Oi{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=_n(t,n),r=t=>{if(!Zn(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{Zn(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach((t=>{t.addEventListener("pointerenter",r,s)})),o}(t,((t,e)=>(pr(this.node,e,"Start"),t=>pr(this.node,t,"End")))))}unmount(){}}}},Pr={layout:{ProjectionNode:Io,MeasureLayout:ur}},Sr=n({strict:!1}),br=n({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Ar=n({});function Vr(t){const{initial:e,animate:n}=function(t,e){if(qo(t)){const{initial:e,animate:n}=t;return{initial:!1===e||Ai(e)?e:void 0,animate:Ai(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,i(Ar));return l((()=>({initial:e,animate:n})),[Er(e),Er(n)])}function Er(t){return Array.isArray(t)?t.join(" "):t}const Mr=Symbol.for("motionComponentSymbol");function Dr(t,e,n){return r((i=>{i&&t.onMount&&t.onMount(i),e&&(i?e.mount(i):e.unmount()),n&&("function"==typeof n?n(i):ms(n)&&(n.current=i))}),[e])}const Cr=Xo?h:o;function kr(t,e,n,s,r){const{visualElement:a}=i(Ar),l=i(Sr),h=i(rr),d=i(br).reducedMotion,p=u(null);s=s||l.renderer,!p.current&&s&&(p.current=s(t,{visualState:e,parent:a,props:n,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:d}));const m=p.current,f=i(lr);!m||m.projection||!r||"html"!==m.type&&"svg"!==m.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Rr(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&ms(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:u,layoutScroll:l,layoutRoot:h})}(p.current,n,r,f);const y=u(!1);c((()=>{m&&y.current&&m.update(n,h)}));const g=n[di],v=u(Boolean(g)&&!window.MotionHandoffIsComplete?.(g)&&window.MotionHasOptimisedAnimation?.(g));return Cr((()=>{m&&(y.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),zn.render(m.render),v.current&&m.animationState&&m.animationState.animateChanges())})),o((()=>{m&&(!v.current&&m.animationState&&m.animationState.animateChanges(),v.current&&(queueMicrotask((()=>{window.MotionHandoffMarkAsComplete?.(g)})),v.current=!1))})),m}function Rr(t){if(t)return!1!==t.options.allowProjection?t.projection:Rr(t.parent)}function Lr({preloadedFeatures:n,createVisualElement:s,useRender:o,useVisualState:r,Component:a}){function l(n,l){let h;const u={...i(br),...n,layoutId:jr(n)},{isStatic:c}=u,d=Vr(n),p=r(n,c);if(!c&&Xo){i(Sr).strict;const t=function(t){const{drag:e,layout:n}=Yo;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(u);h=t.MeasureLayout,d.visualElement=kr(a,p,u,s,t.ProjectionNode)}return e(Ar.Provider,{value:d,children:[h&&d.visualElement?t(h,{visualElement:d.visualElement,...u}):null,o(a,n,Dr(p,d.visualElement,l),p,c,d.visualElement)]})}n&&function(t){for(const e in t)Yo[e]={...Yo[e],...t[e]}}(n),l.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;const h=d(l);return h[Mr]=a,h}function jr({layoutId:t}){const e=i(ar).id;return e&&void 0!==t?e+"-"+t:t}const Br=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Fr(t,e,n){for(const i in e)hi(e[i])||ir(i,n)||(t[i]=e[i])}function Or(t,e){const n={};return Fr(n,t.style||{},t),Object.assign(n,function({transformTemplate:t},e){return l((()=>{const n={style:{},transform:{},transformOrigin:{},vars:{}};return er(n,e,t),Object.assign({},n.vars,n.style)}),[e])}(t,e)),n}function Ir(t,e){const n={},i=Or(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Ur=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Nr(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Ur.has(t)}let Wr=t=>!Nr(t);try{($r=require("@emotion/is-prop-valid").default)&&(Wr=t=>t.startsWith("on")?!Nr(t):$r(t))}catch{}var $r;const Yr=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Xr(t){return"string"==typeof t&&!t.includes("-")&&!!(Yr.indexOf(t)>-1||/[A-Z]/u.test(t))}const Kr={offset:"stroke-dashoffset",array:"stroke-dasharray"},zr={offset:"strokeDashoffset",array:"strokeDasharray"};function Hr(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,h,u){if(er(t,a,h),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=u?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==i&&(c.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Kr:zr;t[o.offset]=Vt.transform(-i);const r=Vt.transform(e),a=Vt.transform(n);t[o.array]=`${r} ${a}`}(c,s,o,r,!1)}const qr=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Gr=t=>"string"==typeof t&&"svg"===t.toLowerCase();function _r(t,e,n,i){const s=l((()=>{const n={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Hr(n,e,Gr(i),t.transformTemplate,t.style),{...n.attrs,style:{...n.style}}}),[e]);if(t.style){const e={};Fr(e,t.style,t),s.style={...e,...s.style}}return s}function Zr(t=!1){return(e,n,i,{latestValues:s},o)=>{const r=(Xr(e)?_r:Ir)(n,s,o,e),a=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(Wr(s)||!0===n&&Nr(s)||!e&&!Nr(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(n,"string"==typeof e,t),h=e!==p?{...a,...r,ref:i}:{},{children:u}=n,c=l((()=>hi(u)?u.get():u),[u]);return m(e,{...h,children:c})}}const Jr=t=>(e,n)=>{const s=i(Ar),o=i(rr),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:Qr(n,i,s,t),renderState:e()}}(t,e,s,o);return n?r():function(t){const e=u(null);return null===e.current&&(e.current=t()),e.current}(r)};function Qr(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=js(o[t]);let{initial:r,animate:a}=t;const l=qo(t),h=Go(t);e&&h&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let u=!!n&&!1===n.initial;u=u||!1===r;const c=u?a:r;if(c&&"boolean"!=typeof c&&!f(c)){const e=Array.isArray(c)?c:[c];for(let n=0;n<e.length;n++){const i=g(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[u?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}const ta={useVisualState:Jr({scrapeMotionValuesFromProps:sr,createRenderState:Br})};function ea(t,e,n){const i=sr(t,e,n);for(const n in t)if(hi(t[n])||hi(e[n])){i[-1!==qe.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}const na={useVisualState:Jr({scrapeMotionValuesFromProps:ea,createRenderState:qr})};function ia(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return Lr({...Xr(n)?na:ta,preloadedFeatures:t,useRender:Zr(i),createVisualElement:e,Component:n})}}const sa=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oa extends Jo{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=_i}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Ge.has(e)){const t=Un(e);return t&&t.default||0}return e=sa.has(e)?e:ci(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return ea(t,e,n)}build(t,e,n){Hr(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){nr(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(sa.has(n)?n:ci(n),e.attrs[n])}(t,e,0,i)}mount(t){this.isSVGTag=Gr(t.tagName),super.mount(t)}}const ra=(t,e)=>Xr(t)?new oa(e):new or(e,{allowProjection:t!==p}),aa=ia({...Ui,...wr,...dr,...Pr},ra)("div");export{aa as MotionDiv};
