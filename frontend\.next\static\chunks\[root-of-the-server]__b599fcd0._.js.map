{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_cb69f1e2.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_cb69f1e2-module__bRYYAW__className\",\n  \"variable\": \"inter_cb69f1e2-module__bRYYAW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_cb69f1e2.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22fonts.ts%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inknut_antiqua_8bb50917.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inknut_antiqua_8bb50917-module__p2dL8W__className\",\n  \"variable\": \"inknut_antiqua_8bb50917-module__p2dL8W__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inknut_antiqua_8bb50917.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22fonts.ts%22,%22import%22:%22Inknut_Antiqua%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22700%22],%22display%22:%22swap%22,%22variable%22:%22--font-inknut-antiqua%22}],%22variableName%22:%22inknutAntiqua%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inknut Antiqua', 'Inknut Antiqua Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,iKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,iKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,iKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\ninterface ThemeProviderProps {\n  children: ReactNode;\n}\n\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  const [theme, setThemeState] = useState<Theme>('light');\n\n  // Initialize theme from localStorage or default to light\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('kairos-theme') as Theme;\n    if (savedTheme) {\n      setThemeState(savedTheme);\n    } else {\n      // Default to light mode\n      setThemeState('light');\n    }\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    const root = document.documentElement;\n\n    if (theme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  }, [theme]);\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    localStorage.setItem('kairos-theme', newTheme);\n  };\n\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n  };\n\n  const value: ThemeContextType = {\n    theme,\n    toggleTheme,\n    setTheme,\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAYA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,cAAc,EAAE,QAAQ,EAAsB;;IAC5D,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE/C,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,cAAc;YAChB,OAAO;gBACL,wBAAwB;gBACxB,cAAc;YAChB;QACF;kCAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,OAAO,SAAS,eAAe;YAErC,IAAI,UAAU,QAAQ;gBACpB,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC;YACxB;QACF;kCAAG;QAAC;KAAM;IAEV,MAAM,WAAW,CAAC;QAChB,cAAc;QACd,aAAa,OAAO,CAAC,gBAAgB;IACvC;IAEA,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,UAAU,SAAS;QAC9C,SAAS;IACX;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;IA9CgB;KAAA", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/BackgroundBlobs.tsx"], "sourcesContent": ["// components/BackgroundBlobs.tsx\r\n'use client';\r\n\r\nimport React from 'react';\r\nimport { useTheme } from '../contexts/ThemeContext';\r\n\r\ninterface XYBlobProps {\r\n  x: string;           // X position from left (e.g., \"10%\", \"200px\")\r\n  y: string;           // Y position from top (e.g., \"20%\", \"300px\")\r\n  size: string;        // Width/height (e.g., \"40vw\", \"300px\")\r\n  color: string;       // Main color (e.g., \"#3f6bfd\")\r\n  opacity?: number;    // 0-1 opacity value\r\n  blur?: string;       // Blur amount (e.g., \"80px\")\r\n  mirror?: boolean;    // Whether to add mirror effect\r\n}\r\n\r\n// XYBlob component for positioning blobs anywhere\r\nfunction XYBlob({ \r\n  x, \r\n  y, \r\n  size, \r\n  color, \r\n  opacity = 0.7, \r\n  blur = \"80px\",\r\n  mirror = false\r\n}: XYBlobProps) {\r\n  // Create solid color with opacity instead of gradient\r\n  const getSolidColor = () => {\r\n    const rgba = hexToRgba(color, 0.3);\r\n    return rgba;\r\n  };\r\n\r\n  // Helper to convert hex to rgba\r\n  const hexToRgba = (hex: string, alpha: number) => {\r\n    const r = parseInt(hex.slice(1, 3), 16);\r\n    const g = parseInt(hex.slice(3, 5), 16);\r\n    const b = parseInt(hex.slice(5, 7), 16);\r\n    return `rgba(${r},${g},${b},${alpha})`;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"pointer-events-none fixed -z-40 rounded-full mix-blend-screen\"\r\n      style={{\r\n        left: x,\r\n        top: y,\r\n        width: size,\r\n        height: size,\r\n        opacity: opacity,\r\n        background: getSolidColor(),\r\n        filter: `blur(${blur})`\r\n      }}\r\n    >\r\n      {mirror && (\r\n        <div \r\n          className=\"absolute inset-0 scale-x-[-1]\" \r\n          style={{ background: \"inherit\" }} \r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function BackgroundBlobs() {\r\n  const { theme } = useTheme();\r\n\r\n  // Define colors based on theme\r\n  const blobColors = theme === 'dark'\r\n    ? {\r\n        primary: '#4C85F6',\r\n        secondary: '#BCC5FF',\r\n        accent: '#A020F0'\r\n      }\r\n    : {\r\n        primary: '#c9ada7',\r\n        secondary: '#9a8c98',\r\n        accent: '#4a4e69'\r\n      };\r\n\r\n  return (\r\n    <>\r\n      {/* Theme-aware background */}\r\n      <div className=\"fixed inset-0 -z-50 bg-background overflow-hidden\" />\r\n\r\n      <XYBlob\r\n        x=\"-20%\"\r\n        y=\"0%\"\r\n        size=\"40vw\"\r\n        color={blobColors.primary}\r\n        opacity={theme === 'dark' ? 0.7 : 0.3}\r\n        blur=\"150px\"\r\n      />\r\n\r\n      <XYBlob\r\n        x=\"80%\"\r\n        y=\"40%\"\r\n        size=\"30vw\"\r\n        color={blobColors.secondary}\r\n        opacity={theme === 'dark' ? 0.6 : 0.25}\r\n        blur=\"150px\"\r\n        mirror={true}\r\n      />\r\n\r\n      <XYBlob\r\n        x=\"22%\"\r\n        y=\"65%\"\r\n        size=\"60vw\"\r\n        color={blobColors.accent}\r\n        opacity={theme === 'dark' ? 0.5 : 0.2}\r\n        blur=\"60px\"\r\n      />\r\n\r\n      {/* You can add more blobs here as needed */}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;AAIjC;;;AAHA;;AAeA,kDAAkD;AAClD,SAAS,OAAO,EACd,CAAC,EACD,CAAC,EACD,IAAI,EACJ,KAAK,EACL,UAAU,GAAG,EACb,OAAO,MAAM,EACb,SAAS,KAAK,EACF;IACZ,sDAAsD;IACtD,MAAM,gBAAgB;QACpB,MAAM,OAAO,UAAU,OAAO;QAC9B,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,YAAY,CAAC,KAAa;QAC9B,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;QACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;QACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;QACpC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACxC;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACzB;kBAEC,wBACC,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE,YAAY;YAAU;;;;;;;;;;;AAKzC;KA5CS;AA8CM,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAEzB,+BAA+B;IAC/B,MAAM,aAAa,UAAU,SACzB;QACE,SAAS;QACT,WAAW;QACX,QAAQ;IACV,IACA;QACE,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IAEJ,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAO,WAAW,OAAO;gBACzB,SAAS,UAAU,SAAS,MAAM;gBAClC,MAAK;;;;;;0BAGP,6LAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAO,WAAW,SAAS;gBAC3B,SAAS,UAAU,SAAS,MAAM;gBAClC,MAAK;gBACL,QAAQ;;;;;;0BAGV,6LAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAO,WAAW,MAAM;gBACxB,SAAS,UAAU,SAAS,MAAM;gBAClC,MAAK;;;;;;;;AAMb;GApDwB;;QACJ,mIAAA,CAAA,WAAQ;;;MADJ", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/utils/websocket.ts"], "sourcesContent": ["// WebSocket client utility for chat functionality\r\n\r\n// Backend URL configuration - can be moved to environment variables later\r\nexport const BACKEND_WS_URL = process.env.NEXT_PUBLIC_BACKEND_WS_URL || 'ws://localhost:8000/ws';\r\nexport const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';\r\n\r\n// Message types\r\nexport interface ChatMessage {\r\n  type: 'user' | 'ai';\r\n  content: string;\r\n  chart?: any;\r\n  causality?: any;\r\n  insights?: string;\r\n  external_contexts?: any[];\r\n  timestamp?: string;\r\n  status?: string;\r\n  thought?: string;\r\n}\r\n\r\nexport interface WebSocketMessage {\r\n  message: string;\r\n  slug?: string;\r\n  files_path?: string[];\r\n  external_contexts?: any[];\r\n  model_context?: {\r\n    chart?: {\r\n      selection?: {\r\n        point?: { name: string, value: number };\r\n        range?: { start: string, end: string, values: any[] };\r\n      };\r\n      data?: any[];\r\n    };\r\n    // Can add other context types here in the future\r\n    // document?: { ... };\r\n    // code?: { ... };\r\n    // etc.\r\n  };\r\n}\r\n\r\nexport interface WebSocketResponse {\r\n  message?: string;\r\n  thought?: string;\r\n  chart?: any;\r\n  status?: string;\r\n  next_agent?: string;\r\n  slug?: string;\r\n  causality?: any;\r\n  external_contexts?: any[];\r\n  insights?: string;\r\n  history?: ChatMessage[];\r\n}\r\n\r\n// WebSocket connection states\r\nexport type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';\r\n\r\n// WebSocket client class\r\nexport class WebSocketClient {\r\n  private socket: WebSocket | null = null;\r\n  private reconnectAttempts = 0;\r\n  private maxReconnectAttempts = 5;\r\n  private reconnectTimeout: NodeJS.Timeout | null = null;\r\n  private messageQueue: WebSocketMessage[] = [];\r\n  private onMessageCallback: ((data: WebSocketResponse) => void) | null = null;\r\n  private onStatusChangeCallback: ((status: ConnectionStatus) => void) | null = null;\r\n\r\n  constructor() {\r\n    // Initialize in a disconnected state\r\n    this.notifyStatusChange('disconnected');\r\n  }\r\n\r\n  // Connect to the WebSocket server\r\n  public connect(slug?: string): void {\r\n    if (this.socket?.readyState === WebSocket.OPEN) {\r\n      console.log('WebSocket already connected');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      this.notifyStatusChange('connecting');\r\n      // Use slug to target a specific chat channel if provided\r\n      const url = slug ? `${BACKEND_WS_URL}/${slug}` : BACKEND_WS_URL;\r\n      console.log('Connecting WebSocket to', url);\r\n      this.socket = new WebSocket(url);\r\n\r\n      this.socket.onopen = this.handleOpen.bind(this);\r\n      this.socket.onmessage = this.handleMessage.bind(this);\r\n      this.socket.onclose = this.handleClose.bind(this);\r\n      this.socket.onerror = this.handleError.bind(this);\r\n    } catch (error) {\r\n      console.error('Failed to connect to WebSocket:', error);\r\n      this.notifyStatusChange('error');\r\n      this.attemptReconnect();\r\n    }\r\n  }\r\n\r\n  // Disconnect from the WebSocket server\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.close();\r\n      this.socket = null;\r\n    }\r\n\r\n    if (this.reconnectTimeout) {\r\n      clearTimeout(this.reconnectTimeout);\r\n      this.reconnectTimeout = null;\r\n    }\r\n\r\n    this.notifyStatusChange('disconnected');\r\n  }\r\n\r\n  // Send a message to the WebSocket server\r\n  public sendMessage(message: WebSocketMessage): void {\r\n    console.log(\"[DEBUG] Sending WebSocket message:\", message);\r\n    if (this.socket?.readyState === WebSocket.OPEN) {\r\n      this.socket.send(JSON.stringify(message));\r\n    } else {\r\n      console.log('WebSocket not connected, queueing message');\r\n      this.messageQueue.push(message);\r\n      this.connect();\r\n    }\r\n  }\r\n\r\n  // Set callback for incoming messages\r\n  public onMessage(callback: (data: WebSocketResponse) => void): void {\r\n    this.onMessageCallback = callback;\r\n  }\r\n\r\n  // Set callback for connection status changes\r\n  public onStatusChange(callback: (status: ConnectionStatus) => void): void {\r\n    this.onStatusChangeCallback = callback;\r\n  }\r\n\r\n  // Handle WebSocket open event\r\n  private handleOpen(): void {\r\n    console.log('WebSocket connected');\r\n    this.reconnectAttempts = 0;\r\n    this.notifyStatusChange('connected');\r\n\r\n    // Send any queued messages\r\n    while (this.messageQueue.length > 0) {\r\n      const message = this.messageQueue.shift();\r\n      if (message) {\r\n        this.sendMessage(message);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Handle WebSocket message event\r\n  private handleMessage(event: MessageEvent): void {\r\n    try {\r\n      console.log(\"[DEBUG] Raw WebSocket message received:\", event.data);\r\n      const data = JSON.parse(event.data) as WebSocketResponse;\r\n      console.log(\"[DEBUG] Parsed WebSocket message:\", data);\r\n      if (this.onMessageCallback) {\r\n        this.onMessageCallback(data);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing WebSocket message:', error);\r\n    }\r\n  }\r\n\r\n  // Handle WebSocket close event\r\n  private handleClose(event: CloseEvent): void {\r\n    console.log(`WebSocket closed: ${event.code} ${event.reason}`);\r\n    this.socket = null;\r\n    this.notifyStatusChange('disconnected');\r\n    this.attemptReconnect();\r\n  }\r\n\r\n  // Handle WebSocket error event\r\n  private handleError(event: Event): void {\r\n    console.error('WebSocket error:', event);\r\n    this.notifyStatusChange('error');\r\n    this.socket?.close();\r\n  }\r\n\r\n  // Attempt to reconnect to the WebSocket server\r\n  private attemptReconnect(): void {\r\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\r\n      console.log('Max reconnect attempts reached');\r\n      return;\r\n    }\r\n\r\n    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);\r\n    console.log(`Attempting to reconnect in ${delay}ms`);\r\n\r\n    this.reconnectTimeout = setTimeout(() => {\r\n      this.reconnectAttempts++;\r\n      this.connect();\r\n    }, delay);\r\n  }\r\n\r\n  // Notify status change\r\n  private notifyStatusChange(status: ConnectionStatus): void {\r\n    if (this.onStatusChangeCallback) {\r\n      this.onStatusChangeCallback(status);\r\n    }\r\n  }\r\n}\r\n\r\n// Create a singleton instance\r\nexport const websocketClient = new WebSocketClient();\r\n\r\n// File upload utility function\r\nexport async function uploadFile(file: File): Promise<{ filename: string }> {\r\n  const formData = new FormData();\r\n  formData.append('file', file);\r\n\r\n  const response = await fetch(`${BACKEND_API_URL}/upload`, {\r\n    method: 'POST',\r\n    body: formData,\r\n  });\r\n\r\n  if (!response.ok) {\r\n    throw new Error(`Upload failed: ${response.statusText}`);\r\n  }\r\n\r\n  return response.json();\r\n}\r\n\r\n"], "names": [], "mappings": "AAAA,kDAAkD;AAElD,0EAA0E;;;;;;;;AAC5C;AAAvB,MAAM,iBAAiB,+DAA0C;AACjE,MAAM,kBAAkB,6DAA2C;AAoDnE,MAAM;IACH,SAA2B,KAAK;IAChC,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;IACzB,mBAA0C,KAAK;IAC/C,eAAmC,EAAE,CAAC;IACtC,oBAAgE,KAAK;IACrE,yBAAsE,KAAK;IAEnF,aAAc;QACZ,qCAAqC;QACrC,IAAI,CAAC,kBAAkB,CAAC;IAC1B;IAEA,kCAAkC;IAC3B,QAAQ,IAAa,EAAQ;QAClC,IAAI,IAAI,CAAC,MAAM,EAAE,eAAe,UAAU,IAAI,EAAE;YAC9C,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI;YACF,IAAI,CAAC,kBAAkB,CAAC;YACxB,yDAAyD;YACzD,MAAM,MAAM,OAAO,GAAG,eAAe,CAAC,EAAE,MAAM,GAAG;YACjD,QAAQ,GAAG,CAAC,2BAA2B;YACvC,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU;YAE5B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;YAC9C,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;YACpD,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;YAChD,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAClD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,gBAAgB;QACvB;IACF;IAEA,uCAAuC;IAChC,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,aAAa,IAAI,CAAC,gBAAgB;YAClC,IAAI,CAAC,gBAAgB,GAAG;QAC1B;QAEA,IAAI,CAAC,kBAAkB,CAAC;IAC1B;IAEA,yCAAyC;IAClC,YAAY,OAAyB,EAAQ;QAClD,QAAQ,GAAG,CAAC,sCAAsC;QAClD,IAAI,IAAI,CAAC,MAAM,EAAE,eAAe,UAAU,IAAI,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;QAClC,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,OAAO;QACd;IACF;IAEA,qCAAqC;IAC9B,UAAU,QAA2C,EAAQ;QAClE,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA,6CAA6C;IACtC,eAAe,QAA4C,EAAQ;QACxE,IAAI,CAAC,sBAAsB,GAAG;IAChC;IAEA,8BAA8B;IACtB,aAAmB;QACzB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,kBAAkB,CAAC;QAExB,2BAA2B;QAC3B,MAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAG;YACnC,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,KAAK;YACvC,IAAI,SAAS;gBACX,IAAI,CAAC,WAAW,CAAC;YACnB;QACF;IACF;IAEA,iCAAiC;IACzB,cAAc,KAAmB,EAAQ;QAC/C,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C,MAAM,IAAI;YACjE,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;YAClC,QAAQ,GAAG,CAAC,qCAAqC;YACjD,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,iBAAiB,CAAC;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,+BAA+B;IACvB,YAAY,KAAiB,EAAQ;QAC3C,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAC7D,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,gBAAgB;IACvB;IAEA,+BAA+B;IACvB,YAAY,KAAY,EAAQ;QACtC,QAAQ,KAAK,CAAC,oBAAoB;QAClC,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,MAAM,EAAE;IACf;IAEA,+CAA+C;IACvC,mBAAyB;QAC/B,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACvD,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG;QACnE,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,MAAM,EAAE,CAAC;QAEnD,IAAI,CAAC,gBAAgB,GAAG,WAAW;YACjC,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,OAAO;QACd,GAAG;IACL;IAEA,uBAAuB;IACf,mBAAmB,MAAwB,EAAQ;QACzD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,sBAAsB,CAAC;QAC9B;IACF;AACF;AAGO,MAAM,kBAAkB,IAAI;AAG5B,eAAe,WAAW,IAAU;IACzC,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IAExB,MAAM,WAAW,MAAM,MAAM,GAAG,gBAAgB,OAAO,CAAC,EAAE;QACxD,QAAQ;QACR,MAAM;IACR;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,UAAU,EAAE;IACzD;IAEA,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/contexts/ChatContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport {\r\n  WebSocketClient,\r\n  websocketClient,\r\n  ChatMessage,\r\n  ConnectionStatus,\r\n  WebSocketResponse,\r\n  WebSocketMessage,\r\n  uploadFile\r\n} from '../utils/websocket';\r\n\r\n// Define the context type\r\ninterface ChatContextType {\r\n  messages: ChatMessage[];\r\n  thinking: string;\r\n  isThinking: boolean;\r\n  connectionStatus: ConnectionStatus;\r\n  chartData: any | null;\r\n  causalityData: any | null;\r\n  chartSelection: {\r\n    point?: { name: string, value: number } | null;\r\n    range?: { start: string, end: string, values: any[] } | null;\r\n  };\r\n  uploadedFiles: { name: string, path: string }[];\r\n  externalContexts: any[];\r\n  selectedExternalContexts: any[];\r\n  insights: string;\r\n  currentSlug: string | null;\r\n  sendMessage: (message: string) => void;\r\n  uploadFile: (file: File) => Promise<string>;\r\n  removeUploadedFile: (fileName: string) => void;\r\n  clearMessages: () => void;\r\n  clearUploadedFiles: () => void;\r\n  setChartSelection: (selection: { point?: any, range?: any }) => void;\r\n  setSelectedExternalContexts: (contexts: any[]) => void;\r\n  connectWithSlug: (slug: string) => void;\r\n}\r\n\r\n// Create the context with a default value\r\nconst ChatContext = createContext<ChatContextType | undefined>(undefined);\r\n\r\n// Provider component\r\nexport function ChatProvider({ children }: { children: ReactNode }) {\r\n  const router = useRouter();\r\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\r\n  const [thinking, setThinking] = useState<string>('');\r\n  const [isThinking, setIsThinking] = useState<boolean>(false);\r\n  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');\r\n  const [chartData, setChartData] = useState<any | null>(null);\r\n  const [causalityData, setCausalityData] = useState<any | null>(null);\r\n  const [chartSelection, setChartSelection] = useState<{\r\n    point?: { name: string, value: number } | null;\r\n    range?: { start: string, end: string, values: any[] } | null;\r\n  }>({});\r\n  const [uploadedFiles, setUploadedFiles] = useState<{ name: string, path: string }[]>([]);\r\n  const [externalContexts, setExternalContexts] = useState<any[]>([]);\r\n  const [selectedExternalContexts, setSelectedExternalContexts] = useState<any[]>([]);\r\n  const [insights, setInsights] = useState<string>(\"\");\r\n  const [currentSlug, setCurrentSlug] = useState<string | null>(null);\r\n\r\n  // Connect to WebSocket on component mount\r\n  useEffect(() => {\r\n    console.log(\"[DEBUG] ChatContext mounted\");\r\n\r\n    // Set up WebSocket event handlers\r\n    websocketClient.onStatusChange((status) => {\r\n      console.log(\"[DEBUG] WebSocket status changed:\", status);\r\n      setConnectionStatus(status);\r\n    });\r\n\r\n    websocketClient.onMessage((data) => {\r\n      console.log(\"[DEBUG] WebSocket message received in context:\", data);\r\n      handleWebSocketResponse(data);\r\n    });\r\n\r\n    // Connect to the WebSocket server - only if not already connected\r\n    if (typeof window !== 'undefined') {\r\n      // Extract slug from URL if present\r\n      const pathParts = window.location.pathname.split('/');\r\n      if (pathParts.length > 2 && pathParts[1] === 'chats') {\r\n        const slugFromUrl = pathParts[2];\r\n        console.log(\"[DEBUG] Found slug in URL:\", slugFromUrl);\r\n        if (slugFromUrl && slugFromUrl !== 'new-chat') {\r\n          setCurrentSlug(slugFromUrl);\r\n          websocketClient.connect(slugFromUrl);\r\n        } else {\r\n          websocketClient.connect();\r\n        }\r\n      } else {\r\n        websocketClient.connect();\r\n      }\r\n    } else {\r\n      websocketClient.connect();\r\n    }\r\n\r\n    // Clean up on unmount\r\n    return () => {\r\n      console.log(\"[DEBUG] ChatContext unmounting, disconnecting WebSocket\");\r\n      websocketClient.disconnect();\r\n    };\r\n  }, []);\r\n\r\n  // Handle WebSocket responses\r\n  const handleWebSocketResponse = (data: WebSocketResponse) => {\r\n    console.log(\"[DEBUG] WebSocket response received:\", data);\r\n\r\n    // Log all fields for debugging\r\n    console.log(\"[DEBUG] Response fields:\", {\r\n      hasMessage: !!data.message,\r\n      hasThought: !!data.thought,\r\n      hasChart: !!data.chart,\r\n      hasCausality: !!data.causality,\r\n      hasInsights: !!data.insights,\r\n      hasExternalContexts: !!data.external_contexts,\r\n      hasHistory: !!data.history\r\n    });\r\n\r\n    // Handle slug if present\r\n    if (data.slug) {\r\n      console.log(\"[DEBUG] Slug received:\", data.slug);\r\n      setCurrentSlug(data.slug);\r\n\r\n      // Update the URL without full page reload\r\n      if (typeof window !== 'undefined') {\r\n        console.log(\"[DEBUG] Updating URL with slug:\", data.slug);\r\n\r\n        // Force navigation to the chat page if we're on the main chat page\r\n        const currentPath = window.location.pathname;\r\n        if (currentPath === '/' || currentPath === '/chat') {\r\n          console.log(\"[DEBUG] Redirecting to chat page with slug:\", data.slug);\r\n          // Use Next.js router for navigation instead of direct window.location\r\n          router.push(`/chats/${data.slug}`);\r\n        } else {\r\n          // Just update the URL without navigation for other pages\r\n          window.history.pushState({}, '', `/chats/${data.slug}`);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle chat history if present\r\n    if (data.history && Array.isArray(data.history)) {\r\n      console.log(\"[DEBUG] Chat history received:\", data.history);\r\n\r\n      // Log the structure of each message in history for debugging\r\n      data.history.forEach((msg, idx) => {\r\n        console.log(`[DEBUG] History message ${idx}:`, {\r\n          type: msg.type,\r\n          hasChart: !!msg.chart,\r\n          hasCausality: !!msg.causality,\r\n          hasInsights: !!msg.insights,\r\n          hasExternalContexts: !!msg.external_contexts\r\n        });\r\n      });\r\n\r\n      setMessages(data.history as ChatMessage[]);\r\n\r\n      // Extract latest data from history for charts, causality, insights, etc.\r\n      // This ensures we have the latest data even if it's not sent separately\r\n      const aiMessages = data.history.filter(msg => msg.type === 'ai');\r\n      if (aiMessages.length > 0) {\r\n        const latestAiMsg = aiMessages[aiMessages.length - 1];\r\n        console.log(\"[DEBUG] Latest AI message from history:\", latestAiMsg);\r\n\r\n        // Always update chart data from the latest message in history\r\n        // regardless of whether data.chart exists\r\n        if (latestAiMsg.chart) {\r\n          console.log(\"[DEBUG] Setting chart data from history:\", latestAiMsg.chart);\r\n          setChartData(latestAiMsg.chart);\r\n        }\r\n\r\n        // Always update causality data from the latest message in history\r\n        if (latestAiMsg.causality) {\r\n          console.log(\"[DEBUG] Setting causality data from history:\", latestAiMsg.causality);\r\n          setCausalityData(latestAiMsg.causality);\r\n        } else {\r\n          console.log(\"[DEBUG] No causality data in latest message\");\r\n        }\r\n\r\n        // Always update insights from the latest message in history\r\n        if (latestAiMsg.insights) {\r\n          console.log(\"[DEBUG] Setting insights from history:\", latestAiMsg.insights);\r\n          setInsights(latestAiMsg.insights);\r\n        } else {\r\n          console.log(\"[DEBUG] No insights data in latest message\");\r\n        }\r\n\r\n        // Always update external contexts from the latest message in history\r\n        if (latestAiMsg.external_contexts) {\r\n          console.log(\"[DEBUG] Setting external contexts from history:\", latestAiMsg.external_contexts);\r\n          const enriched = latestAiMsg.external_contexts.map((ctx: any, idx: number) => ({\r\n            ...ctx,\r\n            id: ctx.url + '-' + idx,\r\n            favicon: `https://www.google.com/s2/favicons?domain=${new URL(ctx.url).hostname}`,\r\n            isSelected: true,\r\n          }));\r\n          setExternalContexts(enriched);\r\n        } else {\r\n          console.log(\"[DEBUG] No external contexts in latest message\");\r\n        }\r\n      } else {\r\n        console.log(\"[DEBUG] No AI messages found in history\");\r\n      }\r\n    }\r\n\r\n    if (data.thought) {\r\n      console.log(\"[DEBUG] Processing thought:\", data.thought);\r\n      // Update thinking state\r\n      setThinking(data.thought);\r\n      setIsThinking(true);\r\n    } else if (data.message) {\r\n      console.log(\"[DEBUG] Processing message:\", data.message);\r\n      // Add AI message\r\n      if (data.message) {\r\n        // Create a complete message object with all available data\r\n        const newMessage: ChatMessage = {\r\n          type: 'ai',\r\n          content: data.message,\r\n          timestamp: new Date().toISOString(),\r\n          status: data.status || 'success'\r\n        };\r\n\r\n        // Add other fields if they exist in the response\r\n        if (data.chart) newMessage.chart = data.chart;\r\n        if (data.causality) newMessage.causality = data.causality;\r\n        if (data.insights) newMessage.insights = data.insights;\r\n        if (data.external_contexts) newMessage.external_contexts = data.external_contexts;\r\n        if (data.thought) newMessage.thought = data.thought;\r\n\r\n        // Update the state with the new message\r\n        setMessages(prev => [...prev, newMessage]);\r\n\r\n        // Also update the individual state variables for each component\r\n        // This ensures the UI components get the latest data\r\n        if (data.chart) setChartData(data.chart);\r\n        if (data.causality) setCausalityData(data.causality);\r\n        if (data.insights) setInsights(data.insights || \"\");\r\n        if (data.external_contexts) {\r\n          const enriched = data.external_contexts.map((ctx: any, idx: number) => ({\r\n            ...ctx,\r\n            id: ctx.url + '-' + idx,\r\n            favicon: `https://www.google.com/s2/favicons?domain=${new URL(ctx.url).hostname}`,\r\n            isSelected: true,\r\n          }));\r\n          setExternalContexts(enriched);\r\n        }\r\n      }\r\n      setIsThinking(false);\r\n      setThinking('');\r\n    } else {\r\n      console.log(\"[DEBUG] No message or thought in response:\", data);\r\n    }\r\n\r\n    // Handle chart data if present (even if null)\r\n    if ('chart' in data) {\r\n      console.log(\"[DEBUG] Chart data received:\", data.chart);\r\n      setChartData(data.chart);\r\n    }\r\n\r\n    // Handle causality data if present (even if null)\r\n    if ('causality' in data) {\r\n      console.log(\"[DEBUG] Causality data received:\", data.causality);\r\n      setCausalityData(data.causality);\r\n    }\r\n\r\n    // Handle external contexts if present (even if null)\r\n    if ('external_contexts' in data && data.external_contexts) {\r\n      console.log(\"[DEBUG] External contexts received:\", data.external_contexts);\r\n      const enriched = data.external_contexts.map((ctx: any, idx: number) => ({\r\n        ...ctx,\r\n        id: ctx.url + '-' + idx,\r\n        favicon: `https://www.google.com/s2/favicons?domain=${new URL(ctx.url).hostname}`,\r\n        isSelected: true,\r\n      }));\r\n      setExternalContexts(enriched);\r\n    } else if ('external_contexts' in data && data.external_contexts === null) {\r\n      // Clear external contexts if null\r\n      setExternalContexts([]);\r\n    }\r\n\r\n    // Handle insights if present (even if null)\r\n    if ('insights' in data) {\r\n      console.log(\"[DEBUG] Insights received:\", data.insights);\r\n      setInsights(data.insights || \"\");\r\n    }\r\n  };\r\n\r\n  // Connect to WebSocket with a specific slug\r\n  const connectWithSlug = (slug: string) => {\r\n    console.log(\"[DEBUG] Connecting with slug:\", slug);\r\n\r\n    // Only update if the slug has changed\r\n    if (currentSlug !== slug) {\r\n      setCurrentSlug(slug);\r\n\r\n      // Connect with the slug - our improved WebSocket client will handle\r\n      // updating the slug without creating a new connection if possible\r\n      websocketClient.connect(slug);\r\n    } else {\r\n      console.log(\"[DEBUG] Already connected with slug:\", slug);\r\n    }\r\n  };\r\n\r\n  // Send the message to the WebSocket server\r\n  const sendMessage = (message: string) => {\r\n    // Add user message to the chat\r\n    setMessages(prev => [...prev, { type: 'user', content: message }]);\r\n\r\n    // Set thinking state\r\n    setIsThinking(true);\r\n    setThinking('Analyzing query...');\r\n\r\n    // Create message payload\r\n    const payload: WebSocketMessage = { message };\r\n\r\n    // Add file paths if available\r\n    if (uploadedFiles.length > 0) {\r\n      payload.files_path = uploadedFiles.map(file => file.path);\r\n    }\r\n\r\n    // Add model context with any available context information\r\n    const modelContext: any = {};\r\n\r\n    // Add chart context if available\r\n    if (chartData || chartSelection?.point || chartSelection?.range) {\r\n      modelContext.chart = {\r\n        data: chartData\r\n      };\r\n\r\n      // Only add selection if it exists\r\n      if (chartSelection) {\r\n        modelContext.chart.selection = chartSelection;\r\n      }\r\n\r\n      console.log(\"[DEBUG] Adding chart context to message:\", modelContext.chart);\r\n    }\r\n\r\n    // Only add model_context if we have context to share\r\n    if (Object.keys(modelContext).length > 0) {\r\n      payload.model_context = modelContext;\r\n    }\r\n\r\n    // Add selected external contexts if any\r\n    if (selectedExternalContexts.length > 0) {\r\n      payload.external_contexts = selectedExternalContexts;\r\n    }\r\n\r\n    // Send the message to the WebSocket server\r\n    websocketClient.sendMessage(payload);\r\n  };\r\n\r\n  // Upload a file\r\n  const handleFileUpload = async (file: File): Promise<string> => {\r\n    try {\r\n      const result = await uploadFile(file);\r\n\r\n      // Add to uploaded files\r\n      if (result.filename) {\r\n        setUploadedFiles(prev => [...prev, { name: file.name, path: result.filename }]);\r\n      }\r\n\r\n      return result.filename;\r\n    } catch (error) {\r\n      console.error('Error uploading file:', error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Remove an uploaded file\r\n  const removeUploadedFile = (fileName: string) => {\r\n    setUploadedFiles(prev => prev.filter(file => file.name !== fileName));\r\n  };\r\n\r\n  // Clear all uploaded files\r\n  const clearUploadedFiles = () => {\r\n    setUploadedFiles([]);\r\n  };\r\n\r\n  // Clear all messages\r\n  const clearMessages = () => {\r\n    setMessages([]);\r\n    setThinking('');\r\n    setIsThinking(false);\r\n    setChartData(null);\r\n  };\r\n\r\n  // Create the context value\r\n  const contextValue: ChatContextType = {\r\n    messages,\r\n    thinking,\r\n    isThinking,\r\n    connectionStatus,\r\n    chartData,\r\n    causalityData,\r\n    chartSelection,\r\n    uploadedFiles,\r\n    externalContexts,\r\n    selectedExternalContexts,\r\n    insights,\r\n    currentSlug,\r\n    sendMessage,\r\n    uploadFile: handleFileUpload,\r\n    removeUploadedFile,\r\n    clearMessages,\r\n    clearUploadedFiles,\r\n    setChartSelection,\r\n    setSelectedExternalContexts,\r\n    connectWithSlug,\r\n  };\r\n\r\n  return (\r\n    <ChatContext.Provider value={contextValue}>\r\n      {children}\r\n    </ChatContext.Provider>\r\n  );\r\n}\r\n\r\n// Custom hook to use the chat context\r\nexport function useChat() {\r\n  const context = useContext(ChatContext);\r\n  if (context === undefined) {\r\n    throw new Error('useChat must be used within a ChatProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAyCA,0CAA0C;AAC1C,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAGxD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGhD,CAAC;IACJ,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC,EAAE;IACvF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,QAAQ,GAAG,CAAC;YAEZ,kCAAkC;YAClC,4HAAA,CAAA,kBAAe,CAAC,cAAc;0CAAC,CAAC;oBAC9B,QAAQ,GAAG,CAAC,qCAAqC;oBACjD,oBAAoB;gBACtB;;YAEA,4HAAA,CAAA,kBAAe,CAAC,SAAS;0CAAC,CAAC;oBACzB,QAAQ,GAAG,CAAC,kDAAkD;oBAC9D,wBAAwB;gBAC1B;;YAEA,kEAAkE;YAClE,wCAAmC;gBACjC,mCAAmC;gBACnC,MAAM,YAAY,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACjD,IAAI,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,SAAS;oBACpD,MAAM,cAAc,SAAS,CAAC,EAAE;oBAChC,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,IAAI,eAAe,gBAAgB,YAAY;wBAC7C,eAAe;wBACf,4HAAA,CAAA,kBAAe,CAAC,OAAO,CAAC;oBAC1B,OAAO;wBACL,4HAAA,CAAA,kBAAe,CAAC,OAAO;oBACzB;gBACF,OAAO;oBACL,4HAAA,CAAA,kBAAe,CAAC,OAAO;gBACzB;YACF,OAAO;;YAEP;YAEA,sBAAsB;YACtB;0CAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,4HAAA,CAAA,kBAAe,CAAC,UAAU;gBAC5B;;QACF;iCAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,+BAA+B;QAC/B,QAAQ,GAAG,CAAC,4BAA4B;YACtC,YAAY,CAAC,CAAC,KAAK,OAAO;YAC1B,YAAY,CAAC,CAAC,KAAK,OAAO;YAC1B,UAAU,CAAC,CAAC,KAAK,KAAK;YACtB,cAAc,CAAC,CAAC,KAAK,SAAS;YAC9B,aAAa,CAAC,CAAC,KAAK,QAAQ;YAC5B,qBAAqB,CAAC,CAAC,KAAK,iBAAiB;YAC7C,YAAY,CAAC,CAAC,KAAK,OAAO;QAC5B;QAEA,yBAAyB;QACzB,IAAI,KAAK,IAAI,EAAE;YACb,QAAQ,GAAG,CAAC,0BAA0B,KAAK,IAAI;YAC/C,eAAe,KAAK,IAAI;YAExB,0CAA0C;YAC1C,wCAAmC;gBACjC,QAAQ,GAAG,CAAC,mCAAmC,KAAK,IAAI;gBAExD,mEAAmE;gBACnE,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;gBAC5C,IAAI,gBAAgB,OAAO,gBAAgB,SAAS;oBAClD,QAAQ,GAAG,CAAC,+CAA+C,KAAK,IAAI;oBACpE,sEAAsE;oBACtE,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;gBACnC,OAAO;oBACL,yDAAyD;oBACzD,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;gBACxD;YACF;QACF;QAEA,iCAAiC;QACjC,IAAI,KAAK,OAAO,IAAI,MAAM,OAAO,CAAC,KAAK,OAAO,GAAG;YAC/C,QAAQ,GAAG,CAAC,kCAAkC,KAAK,OAAO;YAE1D,6DAA6D;YAC7D,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK;gBACzB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC,EAAE;oBAC7C,MAAM,IAAI,IAAI;oBACd,UAAU,CAAC,CAAC,IAAI,KAAK;oBACrB,cAAc,CAAC,CAAC,IAAI,SAAS;oBAC7B,aAAa,CAAC,CAAC,IAAI,QAAQ;oBAC3B,qBAAqB,CAAC,CAAC,IAAI,iBAAiB;gBAC9C;YACF;YAEA,YAAY,KAAK,OAAO;YAExB,yEAAyE;YACzE,wEAAwE;YACxE,MAAM,aAAa,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;YAC3D,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,MAAM,cAAc,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;gBACrD,QAAQ,GAAG,CAAC,2CAA2C;gBAEvD,8DAA8D;gBAC9D,0CAA0C;gBAC1C,IAAI,YAAY,KAAK,EAAE;oBACrB,QAAQ,GAAG,CAAC,4CAA4C,YAAY,KAAK;oBACzE,aAAa,YAAY,KAAK;gBAChC;gBAEA,kEAAkE;gBAClE,IAAI,YAAY,SAAS,EAAE;oBACzB,QAAQ,GAAG,CAAC,gDAAgD,YAAY,SAAS;oBACjF,iBAAiB,YAAY,SAAS;gBACxC,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;gBAEA,4DAA4D;gBAC5D,IAAI,YAAY,QAAQ,EAAE;oBACxB,QAAQ,GAAG,CAAC,0CAA0C,YAAY,QAAQ;oBAC1E,YAAY,YAAY,QAAQ;gBAClC,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;gBAEA,qEAAqE;gBACrE,IAAI,YAAY,iBAAiB,EAAE;oBACjC,QAAQ,GAAG,CAAC,mDAAmD,YAAY,iBAAiB;oBAC5F,MAAM,WAAW,YAAY,iBAAiB,CAAC,GAAG,CAAC,CAAC,KAAU,MAAgB,CAAC;4BAC7E,GAAG,GAAG;4BACN,IAAI,IAAI,GAAG,GAAG,MAAM;4BACpB,SAAS,CAAC,0CAA0C,EAAE,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,EAAE;4BACjF,YAAY;wBACd,CAAC;oBACD,oBAAoB;gBACtB,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,GAAG,CAAC,+BAA+B,KAAK,OAAO;YACvD,wBAAwB;YACxB,YAAY,KAAK,OAAO;YACxB,cAAc;QAChB,OAAO,IAAI,KAAK,OAAO,EAAE;YACvB,QAAQ,GAAG,CAAC,+BAA+B,KAAK,OAAO;YACvD,iBAAiB;YACjB,IAAI,KAAK,OAAO,EAAE;gBAChB,2DAA2D;gBAC3D,MAAM,aAA0B;oBAC9B,MAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,WAAW,IAAI,OAAO,WAAW;oBACjC,QAAQ,KAAK,MAAM,IAAI;gBACzB;gBAEA,iDAAiD;gBACjD,IAAI,KAAK,KAAK,EAAE,WAAW,KAAK,GAAG,KAAK,KAAK;gBAC7C,IAAI,KAAK,SAAS,EAAE,WAAW,SAAS,GAAG,KAAK,SAAS;gBACzD,IAAI,KAAK,QAAQ,EAAE,WAAW,QAAQ,GAAG,KAAK,QAAQ;gBACtD,IAAI,KAAK,iBAAiB,EAAE,WAAW,iBAAiB,GAAG,KAAK,iBAAiB;gBACjF,IAAI,KAAK,OAAO,EAAE,WAAW,OAAO,GAAG,KAAK,OAAO;gBAEnD,wCAAwC;gBACxC,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAW;gBAEzC,gEAAgE;gBAChE,qDAAqD;gBACrD,IAAI,KAAK,KAAK,EAAE,aAAa,KAAK,KAAK;gBACvC,IAAI,KAAK,SAAS,EAAE,iBAAiB,KAAK,SAAS;gBACnD,IAAI,KAAK,QAAQ,EAAE,YAAY,KAAK,QAAQ,IAAI;gBAChD,IAAI,KAAK,iBAAiB,EAAE;oBAC1B,MAAM,WAAW,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAC,KAAU,MAAgB,CAAC;4BACtE,GAAG,GAAG;4BACN,IAAI,IAAI,GAAG,GAAG,MAAM;4BACpB,SAAS,CAAC,0CAA0C,EAAE,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,EAAE;4BACjF,YAAY;wBACd,CAAC;oBACD,oBAAoB;gBACtB;YACF;YACA,cAAc;YACd,YAAY;QACd,OAAO;YACL,QAAQ,GAAG,CAAC,8CAA8C;QAC5D;QAEA,8CAA8C;QAC9C,IAAI,WAAW,MAAM;YACnB,QAAQ,GAAG,CAAC,gCAAgC,KAAK,KAAK;YACtD,aAAa,KAAK,KAAK;QACzB;QAEA,kDAAkD;QAClD,IAAI,eAAe,MAAM;YACvB,QAAQ,GAAG,CAAC,oCAAoC,KAAK,SAAS;YAC9D,iBAAiB,KAAK,SAAS;QACjC;QAEA,qDAAqD;QACrD,IAAI,uBAAuB,QAAQ,KAAK,iBAAiB,EAAE;YACzD,QAAQ,GAAG,CAAC,uCAAuC,KAAK,iBAAiB;YACzE,MAAM,WAAW,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAC,KAAU,MAAgB,CAAC;oBACtE,GAAG,GAAG;oBACN,IAAI,IAAI,GAAG,GAAG,MAAM;oBACpB,SAAS,CAAC,0CAA0C,EAAE,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,EAAE;oBACjF,YAAY;gBACd,CAAC;YACD,oBAAoB;QACtB,OAAO,IAAI,uBAAuB,QAAQ,KAAK,iBAAiB,KAAK,MAAM;YACzE,kCAAkC;YAClC,oBAAoB,EAAE;QACxB;QAEA,4CAA4C;QAC5C,IAAI,cAAc,MAAM;YACtB,QAAQ,GAAG,CAAC,8BAA8B,KAAK,QAAQ;YACvD,YAAY,KAAK,QAAQ,IAAI;QAC/B;IACF;IAEA,4CAA4C;IAC5C,MAAM,kBAAkB,CAAC;QACvB,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,sCAAsC;QACtC,IAAI,gBAAgB,MAAM;YACxB,eAAe;YAEf,oEAAoE;YACpE,kEAAkE;YAClE,4HAAA,CAAA,kBAAe,CAAC,OAAO,CAAC;QAC1B,OAAO;YACL,QAAQ,GAAG,CAAC,wCAAwC;QACtD;IACF;IAEA,2CAA2C;IAC3C,MAAM,cAAc,CAAC;QACnB,+BAA+B;QAC/B,YAAY,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;aAAE;QAEjE,qBAAqB;QACrB,cAAc;QACd,YAAY;QAEZ,yBAAyB;QACzB,MAAM,UAA4B;YAAE;QAAQ;QAE5C,8BAA8B;QAC9B,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,QAAQ,UAAU,GAAG,cAAc,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAC1D;QAEA,2DAA2D;QAC3D,MAAM,eAAoB,CAAC;QAE3B,iCAAiC;QACjC,IAAI,aAAa,gBAAgB,SAAS,gBAAgB,OAAO;YAC/D,aAAa,KAAK,GAAG;gBACnB,MAAM;YACR;YAEA,kCAAkC;YAClC,IAAI,gBAAgB;gBAClB,aAAa,KAAK,CAAC,SAAS,GAAG;YACjC;YAEA,QAAQ,GAAG,CAAC,4CAA4C,aAAa,KAAK;QAC5E;QAEA,qDAAqD;QACrD,IAAI,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,GAAG;YACxC,QAAQ,aAAa,GAAG;QAC1B;QAEA,wCAAwC;QACxC,IAAI,yBAAyB,MAAM,GAAG,GAAG;YACvC,QAAQ,iBAAiB,GAAG;QAC9B;QAEA,2CAA2C;QAC3C,4HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;IAC9B;IAEA,gBAAgB;IAChB,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE;YAEhC,wBAAwB;YACxB,IAAI,OAAO,QAAQ,EAAE;gBACnB,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM;4BAAE,MAAM,KAAK,IAAI;4BAAE,MAAM,OAAO,QAAQ;wBAAC;qBAAE;YAChF;YAEA,OAAO,OAAO,QAAQ;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;IAC7D;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB;QACzB,iBAAiB,EAAE;IACrB;IAEA,qBAAqB;IACrB,MAAM,gBAAgB;QACpB,YAAY,EAAE;QACd,YAAY;QACZ,cAAc;QACd,aAAa;IACf;IAEA,2BAA2B;IAC3B,MAAM,eAAgC;QACpC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GApXgB;;QACC,qIAAA,CAAA,YAAS;;;KADV;AAuXT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/layout.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { inter, inknutAntiqua } from './fonts';\r\nimport \"./globals.css\"\r\nimport { usePathname } from 'next/navigation';\r\nimport BackgroundBlobs from './components/BackgroundBlobs';\r\nimport { ChatProvider } from './contexts/ChatContext';\r\nimport { ThemeProvider } from './contexts/ThemeContext';\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const pathname = usePathname();\r\n  const isOnChatPage = pathname?.startsWith('/chats');\r\n\r\n  return (\r\n    <html lang=\"en\" className={`${inter.variable} ${inknutAntiqua.variable}`} suppressHydrationWarning>\r\n      <head>\r\n        <title>Kairos AI</title>\r\n        <meta name=\"description\" content=\"Kairos AI - Time-aware intelligence for forecasting and decision-making\" />\r\n        <link rel=\"icon\" href=\"/kairoslogo.png\" />\r\n        <link rel=\"apple-touch-icon\" href=\"/kairoslogo.png\" />\r\n      </head>\r\n      <body>\r\n        <ThemeProvider>\r\n          {!isOnChatPage && <BackgroundBlobs />}\r\n          <ChatProvider>\r\n            {children}\r\n          </ChatProvider>\r\n        </ThemeProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS,WAAW,EACjC,QAAQ,EAGT;;IACC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,UAAU,WAAW;IAE1C,qBACE,6LAAC;QAAK,MAAK;QAAK,WAAW,GAAG,gLAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,iMAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE;QAAE,wBAAwB;;0BAChG,6LAAC;;kCACC,6LAAC;kCAAM;;;;;;kCACP,6LAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,6LAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,6LAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;;;;;;;0BAEpC,6LAAC;0BACC,cAAA,6LAAC,mIAAA,CAAA,gBAAa;;wBACX,CAAC,8BAAgB,6LAAC,wIAAA,CAAA,UAAe;;;;;sCAClC,6LAAC,kIAAA,CAAA,eAAY;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA1BwB;;QAKL,qIAAA,CAAA,cAAW;;;KALN", "debugId": null}}]}