@import "tailwindcss";
@plugin '@tailwindcss/typography';

/* Removed CSS variables - using pure Tailwind dark mode instead */

body {
  overflow-x: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Removed .bg-background class - using Tailwind instead */

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* PREMIUM button effects with YOUR COLORS */
.glow-button {
  position: relative;
  overflow: hidden;
}

.glow-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  transition: left 0.5s;
}

.glow-button:hover::before {
  left: 100%;
}

/* Premium text outlines using your colors - FIXED for better visibility */
.text-outline-kairos {
  text-shadow:
    -2px -2px 0 #ffffff,
    2px -2px 0 #ffffff,
    -2px 2px 0 #ffffff,
    2px 2px 0 #ffffff;
}

.dark .text-outline-kairos {
  text-shadow:
    -2px -2px 0 #000000,
    2px -2px 0 #000000,
    -2px 2px 0 #000000,
    2px 2px 0 #000000;
}

.text-outline-purple {
  text-shadow:
    -2px -2px 0 #ffffff,
    2px -2px 0 #ffffff,
    -2px 2px 0 #ffffff,
    2px 2px 0 #ffffff;
}

.dark .text-outline-purple {
  text-shadow:
    -2px -2px 0 #000000,
    2px -2px 0 #000000,
    -2px 2px 0 #000000,
    2px 2px 0 #000000;
}

/* Minimal glow effects for specific words */
.text-glow-minimal {
  text-shadow: 0 0 3px rgba(242, 233, 228, 0.3);
}

.dark .text-glow-minimal {
  text-shadow: 0 0 3px rgba(242, 233, 228, 0.4);
}

/* Better placeholder styling for both light and dark modes */
.placeholder-visible::placeholder {
  color: #6b7280; /* Gray-500 for light mode */
  opacity: 1;
}

.dark .placeholder-visible::placeholder {
  color: #9ca3af; /* Gray-400 for dark mode */
  opacity: 1;
}

/* Premium border effects */
.border-glow-kairos {
  border: 2px solid #f2e9e4;
  box-shadow:
    0 0 20px rgba(242, 233, 228, 0.3),
    inset 0 0 20px rgba(242, 233, 228, 0.1);
}

.border-glow-purple {
  border: 2px solid #9a8c98;
  box-shadow:
    0 0 15px rgba(154, 140, 152, 0.4),
    inset 0 0 15px rgba(154, 140, 152, 0.1);
}

/* FANTASTIC dotted patterns using YOUR COLORS */
.pattern-bg {
  background-image: radial-gradient(circle at 1px 1px, var(--border) 1px, transparent 0);
  background-size: 24px 24px;
  opacity: 0.4;
}

.dark .pattern-bg {
  opacity: 0.2;
}

/* Premium dotted patterns with your colors - ENHANCED VISIBILITY IN LIGHT MODE */
.dots-kairos-red {
  background-image: radial-gradient(circle at 2px 2px, #f2e9e4 2px, transparent 0);
  background-size: 32px 32px;
  opacity: 0.35; /* Increased from 0.15 for better visibility */
}

.dots-kairos-purple {
  background-image: radial-gradient(circle at 1px 1px, #9a8c98 1px, transparent 0);
  background-size: 20px 20px;
  opacity: 0.4; /* Increased from 0.2 for better visibility */
}

.dots-kairos-beige {
  background-image: radial-gradient(circle at 1.5px 1.5px, #c9ada7 1.5px, transparent 0);
  background-size: 28px 28px;
  opacity: 0.38; /* Increased from 0.18 for better visibility */
}

/* Multi-color dot pattern - ENHANCED VISIBILITY */
.dots-multi {
  background-image:
    radial-gradient(circle at 2px 2px, #f2e9e4 1px, transparent 0),
    radial-gradient(circle at 12px 12px, #c9ada7 1px, transparent 0),
    radial-gradient(circle at 22px 6px, #9a8c98 1px, transparent 0);
  background-size: 24px 24px;
  opacity: 0.45; /* Increased from 0.25 for better visibility */
}

/* Keep dark mode dots at current visibility level */
.dark .dots-kairos-red {
  opacity: 0.15;
}

.dark .dots-kairos-purple {
  opacity: 0.2;
}

.dark .dots-kairos-beige {
  opacity: 0.18;
}

.dark .dots-multi {
  opacity: 0.15;
}

/* Removed gradient backgrounds - using solid colors instead */

.font-inknut {
  font-family: var(--font-inknut-antiqua);
}
/* Apple-like Glass card base styles */
.glass-card {
  -webkit-backdrop-filter: blur(24px) saturate(180%);
  backdrop-filter: blur(24px) saturate(180%);
  border-radius: 1.75rem;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

/* Light mode glass card - Apple style */
.glass-card {
  background: rgba(242, 233, 228, 0.08);
  border: 1px solid rgba(201, 173, 167, 0.15);
  box-shadow:
    0 0 0 1px rgba(242, 233, 228, 0.1) inset,
    0 8px 32px -8px rgba(34, 34, 59, 0.08),
    0 2px 16px -4px rgba(154, 140, 152, 0.1);
}

.glass-card:hover {
  background: rgba(242, 233, 228, 0.12);
  border-color: rgba(201, 173, 167, 0.25);
  box-shadow:
    0 0 0 1px rgba(242, 233, 228, 0.15) inset,
    0 16px 48px -12px rgba(34, 34, 59, 0.12),
    0 4px 24px -6px rgba(154, 140, 152, 0.15);
  transform: translateY(-2px);
}

/* Dark mode glass card - Apple style */
.dark .glass-card {
  background: rgba(242, 233, 228, 0.06);
  border: 1px solid rgba(201, 173, 167, 0.12);
  box-shadow:
    0 0 0 1px rgba(242, 233, 228, 0.08) inset,
    0 8px 32px -8px rgba(0, 0, 0, 0.4),
    0 2px 16px -4px rgba(74, 78, 105, 0.2);
}

.dark .glass-card:hover {
  background: rgba(242, 233, 228, 0.1);
  border-color: rgba(201, 173, 167, 0.2);
  box-shadow:
    0 0 0 1px rgba(242, 233, 228, 0.12) inset,
    0 16px 48px -12px rgba(0, 0, 0, 0.5),
    0 4px 24px -6px rgba(74, 78, 105, 0.3);
  transform: translateY(-2px);
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.07);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.07) inset,
    0 15px 30px -8px rgba(0, 0, 0, 0.5),
    0 0 25px rgba(63, 107, 253, 0.15);
}

/* New sidebar-specific glass effect */
.sidebar-glass-menu {
  background: rgba(0, 0, 0, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.15);

  -webkit-backdrop-filter: blur(20px) saturate(180%);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: 0.75rem;
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 8px 16px rgba(0, 0, 0, 0.6);
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 246, 141, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(172, 172, 169, 0.5);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(150, 148, 150, 0.203) rgba(0, 0, 0, 0.1);
}

/* Grain overlay effect */
.grain-bg {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* Vignette effect */
.vignette-effect {
  background: radial-gradient(circle at center, transparent 40%, rgba(0,0,0,0.4) 100%);
  mix-blend-mode: multiply;
}

/* Performance optimizations */
@media (max-width: 768px) {
  .vignette-effect {
    background: radial-gradient(circle at center, transparent 50%, rgba(0,0,0,0.3) 100%);
  }
}


