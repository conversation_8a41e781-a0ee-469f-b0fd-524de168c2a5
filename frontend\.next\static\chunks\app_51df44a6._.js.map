{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'motion/react';\nimport { SunIcon, MoonIcon } from '@heroicons/react/24/outline';\nimport { useTheme } from '../contexts/ThemeContext';\n\ninterface ThemeToggleProps {\n  className?: string;\n}\n\nexport default function ThemeToggle({ className = '' }: ThemeToggleProps) {\n  const { theme, toggleTheme } = useTheme();\n\n  return (\n    <motion.button\n      onClick={toggleTheme}\n      className={`relative flex items-center justify-center w-14 h-7 sm:w-16 sm:h-8 rounded-full transition-all duration-500 shadow-lg border touch-manipulation ${\n        theme === 'dark'\n          ? 'bg-gray-700 dark:bg-gray-600 shadow-gray-700/30 border-white/20'\n          : 'bg-gray-200 shadow-gray-200/30 border-black/20'\n      } ${className}`}\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {/* Toggle circle - Apple-like */}\n      <motion.div\n  className={`absolute top-0 bottom-0 my-auto w-6 h-6 sm:w-7 sm:h-7 rounded-full flex items-center justify-center shadow-xl transition-colors duration-500 ${\n    theme === 'dark'\n      ? 'bg-white border border-gray-300 left-[calc(100%-26px)] sm:left-[calc(100%-30px)]'\n      : 'bg-white border border-gray-300 shadow-lg left-[2px]'\n  }`}\n  layout\n  transition={{\n    type: 'spring',\n    stiffness: 700,\n    damping: 30,\n  }}\n>\n\n        {/* Icon with smooth transition */}\n        <motion.div\n          initial={false}\n          animate={{\n            scale: 1,\n            rotate: theme === 'dark' ? 0 : 180,\n          }}\n          transition={{\n            duration: 0.5,\n            ease: 'easeInOut'\n          }}\n        >\n          {theme === 'dark' ? (\n            <MoonIcon className=\"w-4 h-4 text-gray-700\" />\n          ) : (\n            <SunIcon className=\"w-4 h-4 text-yellow-500\" />\n          )}\n        </motion.div>\n      </motion.div>\n\n      {/* Background icons with better positioning */}\n      <div className=\"absolute inset-0 flex items-center justify-between px-2.5\">\n        <motion.div\n          animate={{\n            opacity: theme === 'light' ? 0 : 0.6,\n            scale: theme === 'light' ? 0.8 : 1\n          }}\n          transition={{ duration: 0.4 }}\n        >\n          <SunIcon className=\"w-3.5 h-3.5 text-yellow-400/70\" />\n        </motion.div>\n        <motion.div\n          animate={{\n            opacity: theme === 'dark' ? 0 : 0.6,\n            scale: theme === 'dark' ? 0.8 : 1\n          }}\n          transition={{ duration: 0.4 }}\n        >\n          <MoonIcon className=\"w-3.5 h-3.5 text-gray-600/70\" />\n        </motion.div>\n      </div>\n    </motion.button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;;;AALA;;;;AAWe,SAAS,YAAY,EAAE,YAAY,EAAE,EAAoB;;IACtE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAEtC,qBACE,6LAAC,qNAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAW,CAAC,+IAA+I,EACzJ,UAAU,SACN,oEACA,iDACL,CAAC,EAAE,WAAW;QACf,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAY,CAAC,UAAU,EAAE,UAAU,UAAU,SAAS,QAAQ,KAAK,CAAC;;0BAGpE,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACf,WAAW,CAAC,6IAA6I,EACvJ,UAAU,SACN,qFACA,wDACJ;gBACF,MAAM;gBACN,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;0BAIM,cAAA,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,SAAS;wBACP,OAAO;wBACP,QAAQ,UAAU,SAAS,IAAI;oBACjC;oBACA,YAAY;wBACV,UAAU;wBACV,MAAM;oBACR;8BAEC,UAAU,uBACT,6LAAC,kNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;6CAEpB,6LAAC,gNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAMzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,SAAS,UAAU,UAAU,IAAI;4BACjC,OAAO,UAAU,UAAU,MAAM;wBACnC;wBACA,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC,gNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,SAAS,UAAU,SAAS,IAAI;4BAChC,OAAO,UAAU,SAAS,MAAM;wBAClC;wBACA,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC,kNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;GAzEwB;;QACS,mIAAA,CAAA,WAAQ;;;KADjB", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/landing/ModernNavbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname } from 'next/navigation';\r\nimport { motion } from 'motion/react';\r\nimport ThemeToggle from '../ThemeToggle';\r\nimport { useTheme } from '../../contexts/ThemeContext';\r\n\r\nexport default function ModernNavbar() {\r\n  const pathname = usePathname();\r\n  const { theme } = useTheme();\r\n  const [scrolled, setScrolled] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n\r\n  // Handle scroll effect for navbar\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const offset = window.scrollY;\r\n      if (offset > 50) {\r\n        setScrolled(true);\r\n      } else {\r\n        setScrolled(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n\r\n    // Enable smooth scrolling for anchor links\r\n    const originalScrollBehavior = document.documentElement.style.scrollBehavior;\r\n    document.documentElement.style.scrollBehavior = 'smooth';\r\n\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n      document.documentElement.style.scrollBehavior = originalScrollBehavior;\r\n    };\r\n  }, []);\r\n\r\n  const isActive = (path: string) => {\r\n    return pathname === path;\r\n  };\r\n\r\n  // Helper for smooth scroll with offset\r\n  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {\r\n    e.preventDefault();\r\n    if (id === 'top') {\r\n      window.scrollTo({ top: 0, behavior: 'smooth' });\r\n      return;\r\n    }\r\n    const el = document.getElementById(id);\r\n    if (el) {\r\n      const yOffset = -100; // Adjust this value to match your navbar height\r\n      const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;\r\n      window.scrollTo({ top: y, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.nav\r\n      initial={{ opacity: 0, y: -20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n      className={`fixed w-[95vw] md:max-w-7xl left-1/2 -translate-x-1/2 z-60 transition-all duration-300 ${\r\n        scrolled\r\n          ? 'py-3 mx-auto my-3 bg-background/95 backdrop-blur-xl rounded-full border border-border/20'\r\n          : 'py-5 mx-auto bg-background/95 backdrop-blur-xl md:bg-transparent md:backdrop-blur-none'\r\n      }`}\r\n    >\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\">\r\n        <div className=\"flex justify-between items-center\">\r\n          {/* Logo */}\r\n          <Link href=\"/\" className=\"flex items-center\">\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.5, delay: 0.2 }}\r\n              className=\"flex items-center space-x-3\"\r\n            >\r\n              <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-1.5 sm:p-2 shadow-lg border border-black/20 dark:border-white/20\">\r\n                <Image\r\n                  src=\"/kairoslogo.png\"\r\n                  alt=\"Kairos AI Logo\"\r\n                  width={32}\r\n                  height={32}\r\n                  className=\"h-6 w-6 sm:h-8 sm:w-8\"\r\n                  priority\r\n                />\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <span className=\"text-lg sm:text-xl md:text-2xl font-bold text-foreground font-inknut\">\r\n                  Kairos\r\n                </span>\r\n                <span className=\"ml-1 text-kairosGreen text-lg sm:text-xl md:text-2xl\">AI</span>\r\n              </div>\r\n            </motion.div>\r\n          </Link>\r\n\r\n          {/* Desktop Navigation */}\r\n          <div className=\"hidden md:flex items-center\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.5, delay: 0.3 }}\r\n              className=\"flex space-x-6 ml-12\"\r\n            >\r\n              {[\r\n                { name: 'About', path: '#about' },\r\n                { name: 'Features', path: '#features' },\r\n                { name: 'Founders', path: '#founders' },\r\n              ].map((item, index) => (\r\n                <a\r\n                  key={item.path}\r\n                  href={item.path}\r\n                  onClick={item.path.startsWith('#') ? (e) => handleSmoothScroll(e, item.path.substring(1)) : undefined}\r\n                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${\r\n                    isActive(item.path)\r\n                      ? 'text-foreground bg-primary'\r\n                      : 'text-muted hover:text-foreground hover:bg-secondary/20'\r\n                  }`}\r\n                >\r\n                  {item.name}\r\n                </a>\r\n              ))}\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.5, delay: 0.4 }}\r\n              className=\"ml-4 flex items-center space-x-3\"\r\n            >\r\n              <ThemeToggle />\r\n              <a\r\n                href=\"#top\"\r\n                onClick={(e) => handleSmoothScroll(e, 'top')}\r\n                className=\"px-4 py-2 bg-red-400 text-white border border-white dark:border-black rounded-full hover:bg-red-500 dark:hover:bg-red-300 transition-all duration-300 shadow-lg font-medium cursor-pointer\"\r\n              >\r\n                Get Started\r\n              </a>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Mobile menu button */}\r\n          <div className=\"md:hidden flex items-center\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\r\n              className=\"inline-flex items-center justify-center p-2 rounded-full text-white hover:text-white focus:outline-none bg-red-400 hover:bg-red-500 border border-black dark:border-white transition-colors duration-200\"\r\n              aria-expanded=\"false\"\r\n            >\r\n              <span className=\"sr-only\">Open main menu</span>\r\n              {/* Icon when menu is closed */}\r\n              <svg\r\n                className={`${mobileMenuOpen ? 'hidden' : 'block'} h-6 w-6`}\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n                aria-hidden=\"true\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth=\"2\"\r\n                  d=\"M4 6h16M4 12h16M4 18h16\"\r\n                />\r\n              </svg>\r\n              {/* Icon when menu is open */}\r\n              <svg\r\n                className={`${mobileMenuOpen ? 'block' : 'hidden'} h-6 w-6`}\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n                aria-hidden=\"true\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth=\"2\"\r\n                  d=\"M6 18L18 6M6 6l12 12\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile menu, show/hide based on menu state */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20, scale: 0.95 }}\r\n        animate={{\r\n          opacity: mobileMenuOpen ? 1 : 0,\r\n          y: mobileMenuOpen ? 0 : -20,\r\n          scale: mobileMenuOpen ? 1 : 0.95,\r\n        }}\r\n        transition={{\r\n          duration: 0.2,\r\n          ease: \"easeOut\",\r\n          type: \"spring\",\r\n          stiffness: 300,\r\n          damping: 30\r\n        }}\r\n        className={`md:hidden fixed inset-x-4 top-20 z-50 ${mobileMenuOpen ? 'pointer-events-auto' : 'pointer-events-none'}`}\r\n      >\r\n        <div className=\"bg-background backdrop-blur-xl rounded-2xl border border-black/10 dark:border-white/10 shadow-2xl p-6 mx-auto max-w-sm\">\r\n          <div className=\"flex flex-col space-y-2 w-full\">\r\n            {[\r\n              { name: 'About', path: '#about' },\r\n              { name: 'Features', path: '#features' },\r\n              { name: 'Founders', path: '#founders' },\r\n            ].map((item, index) => (\r\n              <motion.a\r\n                key={item.path}\r\n                href={item.path}\r\n                onClick={item.path.startsWith('#') ? (e) => { handleSmoothScroll(e, item.path.substring(1)); setMobileMenuOpen(false); } : undefined}\r\n                className={`block px-4 py-3 rounded-xl text-base font-medium transition-all duration-200 touch-manipulation ${\r\n                  isActive(item.path)\r\n                    ? 'text-foreground bg-primary border border-black/10 dark:border-white/10'\r\n                    : 'text-muted hover:text-foreground hover:bg-secondary/50 active:bg-secondary/70'\r\n                }`}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                transition={{ delay: index * 0.1 }}\r\n                whileTap={{ scale: 0.98 }}\r\n              >\r\n                {item.name}\r\n              </motion.a>\r\n            ))}\r\n\r\n            <div className=\"pt-4 border-t border-border/50 space-y-3\">\r\n              <div className=\"flex justify-center\">\r\n                <ThemeToggle />\r\n              </div>\r\n              <motion.a\r\n                href=\"#top\"\r\n                onClick={(e) => { handleSmoothScroll(e, 'top'); setMobileMenuOpen(false); }}\r\n                className=\"block w-full px-4 py-3 bg-red-400 text-white rounded-xl hover:bg-red-500 active:bg-red-600 transition-colors text-center font-medium shadow-lg border border-black/10 dark:border-white/10 touch-manipulation\"\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.3 }}\r\n                whileTap={{ scale: 0.98 }}\r\n              >\r\n                Get Started\r\n              </motion.a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n    </motion.nav>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;uDAAe;oBACnB,MAAM,SAAS,OAAO,OAAO;oBAC7B,IAAI,SAAS,IAAI;wBACf,YAAY;oBACd,OAAO;wBACL,YAAY;oBACd;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC,2CAA2C;YAC3C,MAAM,yBAAyB,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc;YAC5E,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;YAEhD;0CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;gBAClD;;QACF;iCAAG,EAAE;IAEL,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,uCAAuC;IACvC,MAAM,qBAAqB,CAAC,GAAwC;QAClE,EAAE,cAAc;QAChB,IAAI,OAAO,OAAO;YAChB,OAAO,QAAQ,CAAC;gBAAE,KAAK;gBAAG,UAAU;YAAS;YAC7C;QACF;QACA,MAAM,KAAK,SAAS,cAAc,CAAC;QACnC,IAAI,IAAI;YACN,MAAM,UAAU,CAAC,KAAK,gDAAgD;YACtE,MAAM,IAAI,GAAG,qBAAqB,GAAG,GAAG,GAAG,OAAO,WAAW,GAAG;YAChE,OAAO,QAAQ,CAAC;gBAAE,KAAK;gBAAG,UAAU;YAAS;QAC/C;IACF;IAEA,qBACE,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,uFAAuF,EACjG,WACI,6FACA,0FACJ;;0BAEF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,QAAQ;;;;;;;;;;;kDAGZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAuE;;;;;;0DAGvF,6LAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;;;;;;sCAM7E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAET;wCACC;4CAAE,MAAM;4CAAS,MAAM;wCAAS;wCAChC;4CAAE,MAAM;4CAAY,MAAM;wCAAY;wCACtC;4CAAE,MAAM;4CAAY,MAAM;wCAAY;qCACvC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAM,mBAAmB,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM;4CAC5F,WAAW,CAAC,uEAAuE,EACjF,SAAS,KAAK,IAAI,IACd,+BACA,0DACJ;sDAED,KAAK,IAAI;2CATL,KAAK,IAAI;;;;;;;;;;8CAcpB,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC,oIAAA,CAAA,UAAW;;;;;sDACZ,6LAAC;4CACC,MAAK;4CACL,SAAS,CAAC,IAAM,mBAAmB,GAAG;4CACtC,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,kBAAkB,CAAC;gCAClC,WAAU;gCACV,iBAAc;;kDAEd,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAE1B,6LAAC;wCACC,WAAW,GAAG,iBAAiB,WAAW,QAAQ,QAAQ,CAAC;wCAC3D,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;kDAIN,6LAAC;wCACC,WAAW,GAAG,iBAAiB,UAAU,SAAS,QAAQ,CAAC;wCAC3D,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;oBAAI,OAAO;gBAAK;gBAC3C,SAAS;oBACP,SAAS,iBAAiB,IAAI;oBAC9B,GAAG,iBAAiB,IAAI,CAAC;oBACzB,OAAO,iBAAiB,IAAI;gBAC9B;gBACA,YAAY;oBACV,UAAU;oBACV,MAAM;oBACN,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;gBACA,WAAW,CAAC,sCAAsC,EAAE,iBAAiB,wBAAwB,uBAAuB;0BAEpH,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ;gCACC;oCAAE,MAAM;oCAAS,MAAM;gCAAS;gCAChC;oCAAE,MAAM;oCAAY,MAAM;gCAAY;gCACtC;oCAAE,MAAM;oCAAY,MAAM;gCAAY;6BACvC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,qNAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;wCAAQ,mBAAmB,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;wCAAK,kBAAkB;oCAAQ,IAAI;oCAC3H,WAAW,CAAC,gGAAgG,EAC1G,SAAS,KAAK,IAAI,IACd,2EACA,iFACJ;oCACF,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,KAAK,IAAI;mCAbL,KAAK,IAAI;;;;;0CAiBlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAW;;;;;;;;;;kDAEd,6LAAC,qNAAA,CAAA,SAAM,CAAC,CAAC;wCACP,MAAK;wCACL,SAAS,CAAC;4CAAQ,mBAAmB,GAAG;4CAAQ,kBAAkB;wCAAQ;wCAC1E,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAK;kDACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAlPwB;;QACL,qIAAA,CAAA,cAAW;QACV,mIAAA,CAAA,WAAQ;;;KAFJ", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/LandingBackgroundBlobs.tsx"], "sourcesContent": ["// components/LandingBackgroundBlobs.tsx\r\n'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { motion } from 'motion/react';\r\n\r\ninterface XYBlobProps {\r\n  x: string;           // X position from left (e.g., \"10%\", \"200px\")\r\n  y: string;           // Y position from top (e.g., \"20%\", \"300px\")\r\n  size: string;        // Width/height (e.g., \"40vw\", \"300px\")\r\n  color: string;       // Main color (e.g., \"#3f6bfd\")\r\n  opacity?: number;    // 0-1 opacity value\r\n  blur?: string;       // Blur amount (e.g., \"80px\")\r\n  mirror?: boolean;    // Whether to add mirror effect\r\n  animate?: boolean;   // Whether to animate the blob\r\n  delay?: number;      // Animation delay\r\n  duration?: number;   // Animation duration\r\n  floatAmount?: number; // How much the blob floats (px)\r\n}\r\n\r\n// XYBlob component for positioning blobs anywhere\r\nfunction XYBlob({\r\n  x,\r\n  y,\r\n  size,\r\n  color,\r\n  opacity = 0.7,\r\n  blur = \"80px\",\r\n  mirror = false,\r\n  animate = false,\r\n  delay = 0,\r\n  duration = 20,\r\n  floatAmount = 30\r\n}: XYBlobProps) {\r\n  // Create gradient based on color\r\n  const getGradient = (c: string) => {\r\n    const rgba = hexToRgba(c, 0.4);\r\n    return `radial-gradient(circle at 50% 50%, ${c} 0%, ${rgba} 30%, transparent 70%)`;\r\n  };\r\n\r\n  // Helper to convert hex to rgba\r\n  const hexToRgba = (hex: string, alpha: number) => {\r\n    const r = parseInt(hex.slice(1, 3), 16);\r\n    const g = parseInt(hex.slice(3, 5), 16);\r\n    const b = parseInt(hex.slice(5, 7), 16);\r\n    return `rgba(${r},${g},${b},${alpha})`;\r\n  };\r\n\r\n  // Generate the background gradient\r\n  const backgroundGradient = getGradient(color);\r\n\r\n  // Simplified animation variants\r\n  const blobVariants = {\r\n    initial: {\r\n      scale: 0.9,\r\n      opacity: 0\r\n    },\r\n    animate: {\r\n      scale: 1,\r\n      opacity: opacity,\r\n      transition: {\r\n        duration: 0.8,\r\n        delay: delay,\r\n        ease: \"easeOut\"\r\n      }\r\n    }\r\n  };\r\n\r\n  // Separate pulse variant for animated blobs\r\n  const pulseVariants = animate ? {\r\n    scale: [1, 1.03, 0.97, 1],\r\n    x: [0, floatAmount/2, -floatAmount/2, 0],\r\n    y: [0, -floatAmount/2, floatAmount/2, 0],\r\n    transition: {\r\n      duration: duration * 1.5,\r\n      repeat: Infinity,\r\n      repeatType: \"reverse\" as const,\r\n      ease: \"easeInOut\"\r\n    }\r\n  } : undefined;\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"pointer-events-none fixed -z-40 rounded-full mix-blend-screen\"\r\n      initial=\"initial\"\r\n      animate={animate ? \"pulse\" : \"animate\"}\r\n      variants={blobVariants}\r\n      {...(animate && { animate: pulseVariants })}\r\n      style={{\r\n        left: x,\r\n        top: y,\r\n        width: size,\r\n        height: size,\r\n        filter: `blur(${blur})`,\r\n        background: backgroundGradient\r\n      }}\r\n    >\r\n      {mirror && (\r\n        <div\r\n          className=\"absolute inset-0 scale-x-[-1]\"\r\n          style={{ background: backgroundGradient }}\r\n        />\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n\r\nexport default function LandingBackgroundBlobs() {\r\n  // State to track if we should animate (disable on mobile for performance)\r\n  const [shouldAnimate, setShouldAnimate] = useState(false);\r\n\r\n  // Check if we're on a device that can handle animations\r\n  useEffect(() => {\r\n    // Only enable animations on desktop devices\r\n    const checkDevice = () => {\r\n      const isMobile = window.innerWidth < 768;\r\n      setShouldAnimate(!isMobile);\r\n    };\r\n\r\n    // Check on mount\r\n    checkDevice();\r\n\r\n    // Check on resize\r\n    window.addEventListener('resize', checkDevice);\r\n    return () => window.removeEventListener('resize', checkDevice);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {/* dark base */}\r\n      <div className=\"fixed inset-0 -z-50 bg-black overflow-hidden\" />\r\n\r\n      {/* Main blue blob in top left */}\r\n      <XYBlob\r\n        x=\"-10%\"\r\n        y=\"-5%\"\r\n        size=\"45vw\"\r\n        color=\"#3f6bfd\"\r\n        opacity={0.6}\r\n        blur=\"120px\"\r\n        animate={shouldAnimate}\r\n        delay={0}\r\n        duration={8}\r\n        floatAmount={40}\r\n      />\r\n\r\n      {/* Yellow blob in top right */}\r\n      <XYBlob\r\n        x=\"70%\"\r\n        y=\"-10%\"\r\n        size=\"40vw\"\r\n        color=\"#fff68d\"\r\n        opacity={0.4}\r\n        blur=\"100px\"\r\n        mirror={true}\r\n        animate={shouldAnimate}\r\n        delay={0.2}\r\n        duration={9}\r\n        floatAmount={35}\r\n      />\r\n\r\n      {/* Green blob in middle */}\r\n      <XYBlob\r\n        x=\"30%\"\r\n        y=\"40%\"\r\n        size=\"50vw\"\r\n        color=\"#46fcb0\"\r\n        opacity={0.3}\r\n        blur=\"90px\"\r\n        animate={shouldAnimate}\r\n        delay={0.4}\r\n        duration={10}\r\n        floatAmount={50}\r\n      />\r\n\r\n      {/* Blue blob in bottom right */}\r\n      <XYBlob\r\n        x=\"60%\"\r\n        y=\"70%\"\r\n        size=\"45vw\"\r\n        color=\"#3f6bfd\"\r\n        opacity={0.5}\r\n        blur=\"110px\"\r\n        animate={shouldAnimate}\r\n        delay={0.6}\r\n        duration={9}\r\n        floatAmount={45}\r\n      />\r\n    </>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AAGxC;AACA;;;AAHA;;;AAmBA,kDAAkD;AAClD,SAAS,OAAO,EACd,CAAC,EACD,CAAC,EACD,IAAI,EACJ,KAAK,EACL,UAAU,GAAG,EACb,OAAO,MAAM,EACb,SAAS,KAAK,EACd,UAAU,KAAK,EACf,QAAQ,CAAC,EACT,WAAW,EAAE,EACb,cAAc,EAAE,EACJ;IACZ,iCAAiC;IACjC,MAAM,cAAc,CAAC;QACnB,MAAM,OAAO,UAAU,GAAG;QAC1B,OAAO,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,KAAK,sBAAsB,CAAC;IACpF;IAEA,gCAAgC;IAChC,MAAM,YAAY,CAAC,KAAa;QAC9B,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;QACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;QACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;QACpC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACxC;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,YAAY;IAEvC,gCAAgC;IAChC,MAAM,eAAe;QACnB,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,SAAS;YACP,OAAO;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,OAAO;gBACP,MAAM;YACR;QACF;IACF;IAEA,4CAA4C;IAC5C,MAAM,gBAAgB,UAAU;QAC9B,OAAO;YAAC;YAAG;YAAM;YAAM;SAAE;QACzB,GAAG;YAAC;YAAG,cAAY;YAAG,CAAC,cAAY;YAAG;SAAE;QACxC,GAAG;YAAC;YAAG,CAAC,cAAY;YAAG,cAAY;YAAG;SAAE;QACxC,YAAY;YACV,UAAU,WAAW;YACrB,QAAQ;YACR,YAAY;YACZ,MAAM;QACR;IACF,IAAI;IAEJ,qBACE,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAQ;QACR,SAAS,UAAU,UAAU;QAC7B,UAAU;QACT,GAAI,WAAW;YAAE,SAAS;QAAc,CAAC;QAC1C,OAAO;YACL,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;YACR,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACvB,YAAY;QACd;kBAEC,wBACC,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE,YAAY;YAAmB;;;;;;;;;;;AAKlD;KApFS;AAsFM,SAAS;;IACtB,0EAA0E;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,4CAA4C;YAC5C,MAAM;gEAAc;oBAClB,MAAM,WAAW,OAAO,UAAU,GAAG;oBACrC,iBAAiB,CAAC;gBACpB;;YAEA,iBAAiB;YACjB;YAEA,kBAAkB;YAClB,OAAO,gBAAgB,CAAC,UAAU;YAClC;oDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2CAAG,EAAE;IAEL,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAM;gBACN,SAAS;gBACT,MAAK;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,aAAa;;;;;;0BAIf,6LAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAM;gBACN,SAAS;gBACT,MAAK;gBACL,QAAQ;gBACR,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,aAAa;;;;;;0BAIf,6LAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAM;gBACN,SAAS;gBACT,MAAK;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,aAAa;;;;;;0BAIf,6LAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAM;gBACN,SAAS;gBACT,MAAK;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,aAAa;;;;;;;;AAIrB;GAnFwB;MAAA", "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/landing/DemoChart.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';\r\nimport { motion } from 'motion/react';\r\nimport { useTheme } from '../../contexts/ThemeContext';\r\n\r\n// Sample data for the chart with YOUR COLOR PALETTE\r\nconst initialData = [\r\n  { name: 'Jan', actual: 4000, predicted: 4200, confidence: 3800 },\r\n  { name: 'Feb', actual: 3000, predicted: 3100, confidence: 2900 },\r\n  { name: 'Mar', actual: 2000, predicted: 2300, confidence: 1950 },\r\n  { name: 'Apr', actual: 2780, predicted: 2600, confidence: 2650 },\r\n  { name: 'May', actual: 1890, predicted: 1800, confidence: 1750 },\r\n  { name: 'Jun', actual: 2390, predicted: 2500, confidence: 2350 },\r\n  { name: 'Jul', actual: 3490, predicted: 3400, confidence: 3300 },\r\n  { name: 'Aug', actual: 4000, predicted: 4100, confidence: 3950 },\r\n  { name: 'Sep', actual: 5000, predicted: 4800, confidence: 4750 },\r\n  { name: 'Oct', actual: 6000, predicted: 5900, confidence: 5850 },\r\n  { name: 'Nov', actual: 7000, predicted: 7200, confidence: 6950 },\r\n  { name: 'Dec', actual: 8000, predicted: 8100, confidence: 7900 },\r\n];\r\n\r\n// Custom tooltip component with YOUR COLORS\r\nconst CustomTooltip = ({ active, payload, label }: any) => {\r\n  if (!active || !payload?.length) return null;\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"glass-card p-4 border border-border/50 text-foreground text-sm shadow-2xl\"\r\n      initial={{ opacity: 0, scale: 0.9 }}\r\n      animate={{ opacity: 1, scale: 1 }}\r\n      transition={{ duration: 0.2 }}\r\n    >\r\n      <p className=\"font-bold text-foreground mb-2\">{label}</p>\r\n      <div className=\"space-y-1\">\r\n        <p className=\"flex items-center\">\r\n          <div className=\"w-3 h-3 rounded-full bg-kairos-red mr-2\"></div>\r\n          <span className=\"text-kairos-red\">Actual: {payload[0]?.value}</span>\r\n        </p>\r\n        <p className=\"flex items-center\">\r\n          <div className=\"w-3 h-3 rounded-full bg-kairos-purple mr-2\"></div>\r\n          <span className=\"text-kairos-purple\">Predicted: {payload[1]?.value}</span>\r\n        </p>\r\n        <p className=\"flex items-center\">\r\n          <div className=\"w-3 h-3 rounded-full bg-kairos-beige mr-2\"></div>\r\n          <span className=\"text-kairos-beige\">Confidence: {payload[2]?.value}</span>\r\n        </p>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default function DemoChart() {\r\n  const { theme } = useTheme();\r\n  const [data, setData] = useState(initialData);\r\n  const [key, setKey] = useState(0);\r\n\r\n  // Effect to periodically refresh the animation\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      // Add small random variations to make the chart look dynamic\r\n      const newData = initialData.map(item => ({\r\n        ...item,\r\n        actual: item.actual + (Math.random() * 400 - 200),\r\n        predicted: item.predicted + (Math.random() * 400 - 200),\r\n        confidence: item.confidence + (Math.random() * 300 - 150)\r\n      }));\r\n      setData(newData);\r\n      setKey(prev => prev + 1);\r\n    }, 8000); // Refresh every 8 seconds\r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  // Dynamic colors based on theme - DARKER GRID LINES\r\n  const gridColor = theme === 'dark' ? 'rgba(201, 173, 167, 0.3)' : 'rgba(74, 78, 105, 0.4)';\r\n  const axisColor = theme === 'dark' ? '#c9ada7' : '#4a4e69';\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"w-full h-full min-h-[400px] p-6 bg-transparent relative overflow-hidden\"\r\n      initial={{ opacity: 0 }}\r\n      whileInView={{ opacity: 1 }}\r\n      viewport={{ once: true, amount: 0.3 }}\r\n      transition={{ duration: 0.8 }}\r\n    >\r\n      {/* Animated background gradient */}\r\n      <div className=\"absolute inset-0 bg-kairos-gradient opacity-5 animate-gradient-shift bg-[length:200%_200%]\"></div>\r\n\r\n      <motion.div\r\n        className=\"mb-6 relative z-10\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        whileInView={{ opacity: 1, y: 0 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.6, delay: 0.2 }}\r\n      >\r\n        <h3 className=\"text-2xl font-inknut bg-gradient-to-r from-kairos-red via-kairos-beige to-kairos-purple bg-clip-text text-transparent animate-gradient-shift bg-[length:200%_200%]\">\r\n          Time Series Forecast Demo\r\n        </h3>\r\n        <p className=\"text-muted mt-2\">Real-time AI predictions with confidence intervals</p>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"relative z-10\"\r\n        initial={{ scale: 0.9, opacity: 0 }}\r\n        whileInView={{ scale: 1, opacity: 1 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.8, delay: 0.4 }}\r\n      >\r\n        <ResponsiveContainer width=\"100%\" height={350}>\r\n          <LineChart\r\n            key={key}\r\n            data={data}\r\n            margin={{\r\n              top: 20,\r\n              right: 40,\r\n              left: 20,\r\n              bottom: 20,\r\n            }}\r\n          >\r\n            <CartesianGrid\r\n              strokeDasharray=\"2 4\"\r\n              stroke={gridColor}\r\n              strokeOpacity={0.8}\r\n              strokeWidth={1.5}\r\n            />\r\n            <XAxis\r\n              dataKey=\"name\"\r\n              stroke={axisColor}\r\n              fontSize={12}\r\n              fontWeight={500}\r\n            />\r\n            <YAxis\r\n              stroke={axisColor}\r\n              fontSize={12}\r\n              fontWeight={500}\r\n            />\r\n            <Tooltip content={<CustomTooltip />} />\r\n\r\n            {/* Confidence interval line */}\r\n            <Line\r\n              type=\"monotone\"\r\n              dataKey=\"confidence\"\r\n              stroke=\"#c9ada7\"\r\n              strokeWidth={1}\r\n              strokeDasharray=\"5 5\"\r\n              dot={false}\r\n              activeDot={false}\r\n              isAnimationActive={true}\r\n              animationDuration={3000}\r\n              animationEasing=\"ease-in-out\"\r\n            />\r\n\r\n            {/* Actual values line - YOUR KAIROS RED */}\r\n            <Line\r\n              type=\"monotone\"\r\n              dataKey=\"actual\"\r\n              stroke=\"#f2e9e4\"\r\n              strokeWidth={3}\r\n              dot={{\r\n                r: 5,\r\n                strokeWidth: 2,\r\n                fill: \"#f2e9e4\",\r\n                stroke: \"#22223b\",\r\n                filter: \"drop-shadow(0 0 6px #f2e9e4)\"\r\n              }}\r\n              activeDot={{\r\n                r: 8,\r\n                fill: \"#f2e9e4\",\r\n                stroke: \"#22223b\",\r\n                strokeWidth: 3,\r\n                filter: \"drop-shadow(0 0 12px #f2e9e4)\"\r\n              }}\r\n              isAnimationActive={true}\r\n              animationDuration={4000}\r\n              animationEasing=\"ease-in-out\"\r\n            />\r\n\r\n            {/* Predicted values line - YOUR KAIROS PURPLE */}\r\n            <Line\r\n              type=\"monotone\"\r\n              dataKey=\"predicted\"\r\n              stroke=\"#9a8c98\"\r\n              strokeWidth={3}\r\n              dot={{\r\n                r: 5,\r\n                strokeWidth: 2,\r\n                fill: \"#9a8c98\",\r\n                stroke: \"#22223b\",\r\n                filter: \"drop-shadow(0 0 6px #9a8c98)\"\r\n              }}\r\n              activeDot={{\r\n                r: 8,\r\n                fill: \"#9a8c98\",\r\n                stroke: \"#22223b\",\r\n                strokeWidth: 3,\r\n                filter: \"drop-shadow(0 0 12px #9a8c98)\"\r\n              }}\r\n              isAnimationActive={true}\r\n              animationDuration={2500}\r\n              animationEasing=\"ease-in-out\"\r\n            />\r\n          </LineChart>\r\n        </ResponsiveContainer>\r\n      </motion.div>\r\n\r\n      {/* Beautiful legend with YOUR COLORS */}\r\n      <motion.div\r\n        className=\"flex justify-center mt-8 space-x-8\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        whileInView={{ opacity: 1, y: 0 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.6, delay: 1.0 }}\r\n      >\r\n        <motion.div\r\n          className=\"flex items-center group cursor-pointer\"\r\n          whileHover={{ scale: 1.05 }}\r\n        >\r\n          <div className=\"w-4 h-4 rounded-full bg-kairos-red mr-3 shadow-lg group-hover:shadow-kairos-red/50 transition-all duration-300\"></div>\r\n          <span className=\"text-sm font-medium text-foreground group-hover:text-kairos-red transition-colors\">Actual Values</span>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          className=\"flex items-center group cursor-pointer\"\r\n          whileHover={{ scale: 1.05 }}\r\n        >\r\n          <div className=\"w-4 h-4 rounded-full bg-kairos-purple mr-3 shadow-lg group-hover:shadow-kairos-purple/50 transition-all duration-300\"></div>\r\n          <span className=\"text-sm font-medium text-foreground group-hover:text-kairos-purple transition-colors\">AI Predictions</span>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          className=\"flex items-center group cursor-pointer\"\r\n          whileHover={{ scale: 1.05 }}\r\n        >\r\n          <div className=\"w-4 h-1 bg-kairos-beige mr-3 opacity-70 group-hover:opacity-100 transition-all duration-300\" style={{borderRadius: '2px', borderStyle: 'dashed', borderWidth: '1px', borderColor: '#c9ada7'}}></div>\r\n          <span className=\"text-sm font-medium text-foreground group-hover:text-kairos-beige transition-colors\">Confidence</span>\r\n        </motion.div>\r\n      </motion.div>\r\n\r\n      {/* Animated stats */}\r\n      <motion.div\r\n        className=\"mt-8 grid grid-cols-3 gap-4 text-center\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        whileInView={{ opacity: 1, y: 0 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.6, delay: 1.2 }}\r\n      >\r\n        <div className=\"glass-card p-4 border-l-4 border-kairos-red\">\r\n          <div className=\"text-2xl font-bold text-kairos-red animate-color-cycle\">94.2%</div>\r\n          <div className=\"text-xs text-muted\">Accuracy</div>\r\n        </div>\r\n        <div className=\"glass-card p-4 border-l-4 border-kairos-purple\">\r\n          <div className=\"text-2xl font-bold text-kairos-purple animate-color-cycle\">2.1s</div>\r\n          <div className=\"text-xs text-muted\">Response Time</div>\r\n        </div>\r\n        <div className=\"glass-card p-4 border-l-4 border-kairos-beige\">\r\n          <div className=\"text-2xl font-bold text-kairos-beige animate-color-cycle\">12M+</div>\r\n          <div className=\"text-xs text-muted\">Data Points</div>\r\n        </div>\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAOA,oDAAoD;AACpD,MAAM,cAAc;IAClB;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;IAC/D;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;QAAM,YAAY;IAAK;CAChE;AAED,4CAA4C;AAC5C,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;IACpD,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ,OAAO;IAExC,qBACE,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAE,WAAU;0BAAkC;;;;;;0BAC/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;;oCAAkB;oCAAS,OAAO,CAAC,EAAE,EAAE;;;;;;;;;;;;;kCAEzD,6LAAC;wBAAE,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;;oCAAqB;oCAAY,OAAO,CAAC,EAAE,EAAE;;;;;;;;;;;;;kCAE/D,6LAAC;wBAAE,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;;oCAAoB;oCAAa,OAAO,CAAC,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAKvE;KA3BM;AA6BS,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/B,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,WAAW;gDAAY;oBAC3B,6DAA6D;oBAC7D,MAAM,UAAU,YAAY,GAAG;gEAAC,CAAA,OAAQ,CAAC;gCACvC,GAAG,IAAI;gCACP,QAAQ,KAAK,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG;gCAChD,WAAW,KAAK,SAAS,GAAG,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG;gCACtD,YAAY,KAAK,UAAU,GAAG,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG;4BAC1D,CAAC;;oBACD,QAAQ;oBACR;wDAAO,CAAA,OAAQ,OAAO;;gBACxB;+CAAG,OAAO,0BAA0B;YAEpC;uCAAO,IAAM,cAAc;;QAC7B;8BAAG,EAAE;IAEL,oDAAoD;IACpD,MAAM,YAAY,UAAU,SAAS,6BAA6B;IAClE,MAAM,YAAY,UAAU,SAAS,YAAY;IAEjD,qBACE,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,aAAa;YAAE,SAAS;QAAE;QAC1B,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;QACpC,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,6LAAC;wBAAG,WAAU;kCAAqK;;;;;;kCAGnL,6LAAC;wBAAE,WAAU;kCAAkB;;;;;;;;;;;;0BAGjC,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,aAAa;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBACpC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBAER,MAAM;wBACN,QAAQ;4BACN,KAAK;4BACL,OAAO;4BACP,MAAM;4BACN,QAAQ;wBACV;;0CAEA,6LAAC,gKAAA,CAAA,gBAAa;gCACZ,iBAAgB;gCAChB,QAAQ;gCACR,eAAe;gCACf,aAAa;;;;;;0CAEf,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,YAAY;;;;;;0CAEd,6LAAC,wJAAA,CAAA,QAAK;gCACJ,QAAQ;gCACR,UAAU;gCACV,YAAY;;;;;;0CAEd,6LAAC,0JAAA,CAAA,UAAO;gCAAC,uBAAS,6LAAC;;;;;;;;;;0CAGnB,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,iBAAgB;gCAChB,KAAK;gCACL,WAAW;gCACX,mBAAmB;gCACnB,mBAAmB;gCACnB,iBAAgB;;;;;;0CAIlB,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCACH,GAAG;oCACH,aAAa;oCACb,MAAM;oCACN,QAAQ;oCACR,QAAQ;gCACV;gCACA,WAAW;oCACT,GAAG;oCACH,MAAM;oCACN,QAAQ;oCACR,aAAa;oCACb,QAAQ;gCACV;gCACA,mBAAmB;gCACnB,mBAAmB;gCACnB,iBAAgB;;;;;;0CAIlB,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCACH,GAAG;oCACH,aAAa;oCACb,MAAM;oCACN,QAAQ;oCACR,QAAQ;gCACV;gCACA,WAAW;oCACT,GAAG;oCACH,MAAM;oCACN,QAAQ;oCACR,aAAa;oCACb,QAAQ;gCACV;gCACA,mBAAmB;gCACnB,mBAAmB;gCACnB,iBAAgB;;;;;;;uBAzFb;;;;;;;;;;;;;;;0BAgGX,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;;0CAE1B,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAoF;;;;;;;;;;;;kCAGtG,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;;0CAE1B,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAuF;;;;;;;;;;;;kCAGzG,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;;0CAE1B,6LAAC;gCAAI,WAAU;gCAA8F,OAAO;oCAAC,cAAc;oCAAO,aAAa;oCAAU,aAAa;oCAAO,aAAa;gCAAS;;;;;;0CAC3M,6LAAC;gCAAK,WAAU;0CAAsF;;;;;;;;;;;;;;;;;;0BAK1G,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAyD;;;;;;0CACxE,6LAAC;gCAAI,WAAU;0CAAqB;;;;;;;;;;;;kCAEtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA4D;;;;;;0CAC3E,6LAAC;gCAAI,WAAU;0CAAqB;;;;;;;;;;;;kCAEtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA2D;;;;;;0CAC1E,6LAAC;gCAAI,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;AAK9C;GAlNwB;;QACJ,mIAAA,CAAA,WAAQ;;;MADJ", "debugId": null}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/GrainOverlay.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nexport default function GrainOverlay() {\r\n  return (\r\n    <div \r\n      className=\"fixed inset-0 pointer-events-none z-10 opacity-[0.15] mix-blend-overlay\"\r\n      style={{\r\n        backgroundImage: `url(\"data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")`,\r\n      }}\r\n    />\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB,CAAC,uTAAuT,CAAC;QAC5U;;;;;;AAGN;KATwB", "debugId": null}}, {"offset": {"line": 1590, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/VignetteEffect.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nexport default function VignetteEffect() {\r\n  return (\r\n    <div \r\n      className=\"fixed inset-0 pointer-events-none z-5\"\r\n      style={{\r\n        background: 'radial-gradient(circle at center, transparent 10%, rgba(0,0,0,0.1) 100%)',\r\n        mixBlendMode: 'multiply',\r\n      }}\r\n    />\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,YAAY;YACZ,cAAc;QAChB;;;;;;AAGN;KAVwB", "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/landing/LottieHeroAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'motion/react';\nimport <PERSON>tie from 'lottie-react';\nimport animationData from '../../../public/animations/hero-animation.json';\n\nexport default function LottieHeroAnimation() {\n  return (\n    <motion.div\n      className=\"relative w-full h-full min-h-[300px] sm:min-h-[400px] lg:min-h-[600px] flex items-center justify-center\"\n      initial={{ opacity: 0, scale: 0.8 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 1, delay: 0.3 }}\n    >\n      <div className=\"w-full max-w-sm sm:max-w-lg lg:max-w-2xl\">\n        <Lottie\n          animationData={animationData}\n          loop={true}\n          autoplay={true}\n          style={{\n            width: '100%',\n            height: '100%',\n            minHeight: '300px'\n          }}\n          className=\"sm:min-h-[400px] lg:min-h-[600px]\"\n        />\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAOe,SAAS;IACtB,qBACE,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;YAAG,OAAO;QAAI;kBAEtC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,2JAAA,CAAA,UAAM;gBACL,eAAe,iHAAA,CAAA,UAAa;gBAC5B,MAAM;gBACN,UAAU;gBACV,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,WAAW;gBACb;gBACA,WAAU;;;;;;;;;;;;;;;;AAKpB;KAvBwB", "debugId": null}}, {"offset": {"line": 1687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/landing/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { ArrowRightIcon, ChartBarIcon, ClockIcon, CodeBracketIcon, LightBulbIcon } from '@heroicons/react/24/outline';\r\nimport { CalendarDaysIcon } from '@heroicons/react/24/solid';\r\nimport { motion } from 'motion/react';\r\nimport FontSwitcher from '../../components/FontSwitcher';\r\n\r\n// Custom components\r\nimport ModernNavbar from '../components/landing/ModernNavbar';\r\nimport LandingBackgroundBlobs from '../components/LandingBackgroundBlobs';\r\nimport DemoChart from '../components/landing/DemoChart';\r\nimport GrainOverlay from '../components/GrainOverlay';\r\nimport VignetteEffect from '../components/VignetteEffect';\r\nimport ParallaxSection from '../components/ParallaxSection';\r\nimport LottieHeroAnimation from '../components/landing/LottieHeroAnimation';\r\nimport { DotLottieReact } from '@lottiefiles/dotlottie-react';\r\n\r\n\r\nexport default function LandingPage() {\r\n  const [email, setEmail] = useState('');\r\n  const [isSubmitted, setIsSubmitted] = useState(false);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Basic email validation\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      if (!emailRegex.test(email)) {\r\n        alert('Please enter a valid email address.');\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Get existing emails from localStorage\r\n      const existingEmails = JSON.parse(localStorage.getItem('kairosWaitlist') || '[]');\r\n\r\n      // Check if email already exists\r\n      if (existingEmails.includes(email.toLowerCase())) {\r\n        alert('This email is already on our waitlist!');\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Send email notification using Web3Forms\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('access_key', '5aca658d-f4f1-4b81-af9c-7ab7dbf435b3');\r\n        formData.append('name', 'Kairos AI Waitlist');\r\n        formData.append('email', email);\r\n        formData.append('message', `New Kairos AI waitlist signup!\r\n\r\nEmail: ${email}\r\nTimestamp: ${new Date().toLocaleString()}\r\nSource: Kairos AI Landing Page\r\nUser Agent: ${navigator.userAgent}`);\r\n\r\n        const response = await fetch('https://api.web3forms.com/submit', {\r\n          method: 'POST',\r\n          body: formData\r\n        });\r\n\r\n        const result = await response.json();\r\n\r\n        if (result.success) {\r\n          console.log('✅ Email sent <NAME_EMAIL> via Web3Forms');\r\n          console.log('📧 Response:', result);\r\n        } else {\r\n          console.log('❌ Web3Forms failed:', result.message);\r\n        }\r\n      } catch (emailError) {\r\n        console.log('❌ Email service error:', emailError);\r\n      }\r\n\r\n      // Always log for manual collection as backup\r\n      console.log('📝 NEW WAITLIST SIGNUP:');\r\n      console.log('📧 Email:', email);\r\n      console.log('🕒 Time:', new Date().toLocaleString());\r\n      console.log('💾 Stored in localStorage for backup');\r\n\r\n      // Add new email to the list (regardless of email sending success)\r\n      const updatedEmails = [...existingEmails, email.toLowerCase()];\r\n      localStorage.setItem('kairosWaitlist', JSON.stringify(updatedEmails));\r\n\r\n      // Store submission timestamp\r\n      localStorage.setItem('kairosWaitlistTimestamp', new Date().toISOString());\r\n\r\n      // Show success state (keep until page reload)\r\n      setIsSubmitted(true);\r\n      setEmail('');\r\n\r\n    } catch (err) {\r\n      alert('There was an error. Please try again.');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Animation variants for staggered animations\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n        delayChildren: 0.3\r\n      }\r\n    }\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { y: 20, opacity: 0 },\r\n    visible: {\r\n      y: 0,\r\n      opacity: 1,\r\n      transition: { duration: 0.5 }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white dark:bg-black text-gray-900 dark:text-gray-100\">\r\n      <FontSwitcher>\r\n        <LandingBackgroundBlobs />\r\n        <GrainOverlay />\r\n        <VignetteEffect />\r\n        <ModernNavbar />\r\n\r\n      {/* Hero Section - PREMIUM with YOUR COLORS */}\r\n      <section className=\"relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 py-20 overflow-hidden\">\r\n        {/* FANTASTIC dotted background */}\r\n        <div className=\"absolute inset-0 dots-multi\"></div>\r\n        <div className=\"absolute inset-0 dots-kairos-red\"></div>\r\n <div className=\"max-w-7xl mx-auto w-full\">\r\n          <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-12 items-center\">\r\n            {/* Left side - Text content */}\r\n            <motion.div\r\n              className=\"text-left space-y-6\"\r\n              initial=\"hidden\"\r\n              animate=\"visible\"\r\n              variants={containerVariants}\r\n            >\r\n              <motion.h1\r\n                className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight font-roboto relative z-10\"\r\n                variants={itemVariants}\r\n              >\r\n                <span className=\"text-foreground\">Agents that build world models for </span>\r\n                <span className=\"text-red-400 relative text-outline-kairos text-glow-minimal\">\r\n                  forecasting\r\n                     </span>\r\n              </motion.h1>\r\n\r\n              <motion.p\r\n                className=\"text-base sm:text-lg md:text-xl text-muted max-w-2xl leading-relaxed\"\r\n                variants={itemVariants}\r\n              >\r\n                A powerful agentic forecasting system that goes beyond traditional time series models by intelligently gathering context and simulating scenarios, saving businesses hours of manual work and cost reduction.\r\n              </motion.p>\r\n\r\n              {/* Waitlist Form */}\r\n              <motion.div\r\n                className=\"w-full max-w-md lg:max-w-md pt-4 relative z-20\"\r\n                variants={itemVariants}\r\n                id=\"waitlist\"\r\n              >\r\n                {isSubmitted ? (\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"text-center p-4 sm:p-6 bg-foreground/20 glass-card backdrop-blur-xl border-2 border-green-400 dark:border-green-600 rounded-2xl w-full shadow-2xl shadow-green-400/20\"\r\n                  >\r\n                    <div className=\"text-green-600  text-xl sm:text-2xl mb-2\">✓</div>\r\n                    <h3 className=\"text-base sm:text-lg font-semibold text-green-600 mb-1\">\r\n                      You're on the list!\r\n                    </h3>\r\n                    <p className=\"text-foreground text-xs sm:text-sm\">\r\n                      Thanks for joining our waitlist. We'll be in touch soon!\r\n                    </p>\r\n                  </motion.div>\r\n                ) : (\r\n                  <form onSubmit={handleSubmit} className=\"flex flex-col sm:flex-row gap-3 w-full\">\r\n                    <input\r\n                      type=\"email\"\r\n                      value={email}\r\n                      onChange={(e) => setEmail(e.target.value)}\r\n                      placeholder=\"Enter your email\"\r\n                      required\r\n                      disabled={isSubmitting}\r\n                      className=\"flex-grow px-4 py-3 sm:py-3 text-sm sm:text-base rounded-full bg-secondary border border-border text-foreground placeholder-visible focus:outline-none focus:ring-2 focus:ring-red-400 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation\"\r\n                    />\r\n                    <motion.button\r\n                      type=\"submit\"\r\n                      disabled={isSubmitting}\r\n                      className={`inline-flex items-center px-8 py-4 font-bold rounded-full border-2 border-black transition-colors duration-300 text-base relative z-10 ${\r\n                        isSubmitting\r\n                          ? 'bg-gray-400 text-gray-600 cursor-not-allowed'\r\n                          : 'bg-red-400 hover:bg-red-200 text-white cursor-pointer'\r\n                      }`}\r\n                      whileHover={!isSubmitting ? { scale: 1.05, y: -3 } : {}}\r\n                      whileTap={!isSubmitting ? { scale: 0.95 } : {}}\r\n                    >\r\n                      {isSubmitting ? (\r\n                        <>\r\n                          <div className=\"w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n                          Joining...\r\n                        </>\r\n                      ) : (\r\n                        'Join Waitlist'\r\n                      )}\r\n                    </motion.button>\r\n                  </form>\r\n                )}\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Right side - Animated SVG */}\r\n            <motion.div\r\n              className=\"flex justify-center lg:justify-end\"\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.3 }}\r\n            >\r\n              <LottieHeroAnimation />\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Scroll indicator */}\r\n        <motion.div\r\n          className=\"absolute bottom-10 left-1/2 transform -translate-x-1/2\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 1, duration: 1 }}\r\n        >\r\n          <motion.div\r\n            animate={{ y: [0, 10, 0] }}\r\n            transition={{ repeat: Infinity, duration: 1.5 }}\r\n            onClick={() => {\r\n              const el = document.getElementById('demo');\r\n              if (el) {\r\n                const yOffset = -40; // Less negative offset for more downward scroll\r\n                const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;\r\n                window.scrollTo({ top: y, behavior: 'smooth' });\r\n              }\r\n            }}\r\n            style={{ cursor: 'pointer' }}\r\n          >\r\n            <ArrowRightIcon className=\"h-6 w-6 text-white rotate-90\" />\r\n          </motion.div>\r\n        </motion.div>\r\n      </section>\r\n\r\n      {/* Vision Section - PREMIUM with YOUR COLORS */}\r\n      <section id=\"about\" className=\"py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\r\n        {/* FANTASTIC backgrounds with your colors */}\r\n           <div className=\"absolute inset-0 dots-kairos-beige\"></div>\r\n        <div className=\"absolute inset-0 dots-kairos-purple\"></div>\r\n\r\n        <motion.div\r\n          className=\"max-w-6xl mx-auto relative z-10\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          viewport={{ once: true, amount: 0.2 }}\r\n          transition={{ duration: 0.8 }}\r\n        >\r\n          {/* Hero Title */}\r\n          <motion.div\r\n            className=\"text-center mb-20\"\r\n            initial={{ y: 60, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n          >\r\n            <h2 className=\"text-5xl md:text-7xl font-bold font-roboto mb-6 relative z-10\">\r\n              <span className=\"text-foreground\">Our </span>\r\n              <span className=\"text-red-400 relative text-outline-kairos text-glow-minimal\">\r\n                Vision\r\n                 </span>\r\n            </h2>\r\n            <p className=\"text-xl text-muted max-w-3xl mx-auto leading-relaxed\">\r\n              Revolutionizing how machines understand and predict the future through time-aware intelligence\r\n            </p>\r\n          </motion.div>\r\n\r\n          {/* Context & Causality */}\r\n          <motion.div\r\n            className=\"glass-card p-8 lg:p-12 mb-8 group cursor-pointer\"\r\n            initial={{ y: 50, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.5, delay: 0.1 }}\r\n            whileHover={{ y: -4, transition: { duration: 0.3 } }}\r\n          >\r\n            <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-16 items-center\">\r\n              {/* Left side - Text content */}\r\n              <div className=\"text-center lg:text-left space-y-6\">\r\n                <div className=\"mb-6 p-4 rounded-2xl bg-red-50 dark:bg-red-900/20 w-fit mx-auto lg:mx-0 group-hover:bg-red-100 dark:group-hover:bg-red-900/30 transition-all duration-300 border border-red-200 dark:border-red-800\">\r\n                  <div className=\"w-8 h-8 bg-red-400 rounded-lg\"></div>\r\n                </div>\r\n                <h3 className=\"text-2xl lg:text-4xl font-bold text-foreground mb-6\">\r\n                  <span className=\"relative text-outline-kairos text-glow-minimal\">\r\n                    Context & Causality\r\n                  </span>\r\n                </h3>\r\n                <p className=\"text-lg text-muted leading-relaxed\">\r\n                  Building intelligent agents that understand context and causality to power real-world forecasting and decision-making with unprecedented accuracy.\r\n                </p>\r\n              </div>\r\n\r\n              {/* Right side - Animation - Hidden on mobile */}\r\n              <div className=\"hidden lg:flex justify-center lg:justify-end\">\r\n                <div className=\"w-full max-w-sm\">\r\n                  <DotLottieReact\r\n                    src=\"/animations/data.lottie\"\r\n                    loop\r\n                    autoplay\r\n                    style={{\r\n                      width: '100%',\r\n                      height: '100%',\r\n                      minHeight: '300px',\r\n                      filter: 'hue-rotate(350deg) saturate(1.2)'\r\n                    }}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Time as a Core Dimension */}\r\n          <motion.div\r\n            className=\"glass-card p-8 lg:p-12 mb-16 group cursor-pointer\"\r\n            initial={{ y: 50, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.5, delay: 0.2 }}\r\n            whileHover={{ y: -4, transition: { duration: 0.3 } }}\r\n          >\r\n            <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-16 items-center\">\r\n              {/* Left side - Animation - Hidden on mobile */}\r\n              <div className=\"hidden lg:flex justify-center lg:justify-start order-2 lg:order-1\">\r\n                <div className=\"w-full max-w-sm\">\r\n                  <DotLottieReact\r\n                    src=\"/animations/ccicle.lottie\"\r\n                    loop\r\n                    autoplay\r\n                    style={{\r\n                      width: '100%',\r\n                      height: '100%',\r\n                      minHeight: '300px',\r\n                      filter: 'hue-rotate(350deg) saturate(1.2)'\r\n                    }}\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Right side - Text content */}\r\n              <div className=\"text-center lg:text-left space-y-6 order-1 lg:order-2\">\r\n                <div className=\"mb-6 p-4 rounded-2xl bg-red-50 dark:bg-red-900/20 w-fit mx-auto lg:mx-0 group-hover:bg-red-100 dark:group-hover:bg-red-900/30 transition-all duration-300 border border-red-200 dark:border-red-800\">\r\n                  <div className=\"w-8 h-8 bg-red-400 rounded-lg\"></div>\r\n                </div>\r\n                <h3 className=\"text-2xl lg:text-4xl font-bold text-foreground mb-6\">\r\n                  <span className=\"relative text-outline-kairos text-glow-minimal\">\r\n                    Time as a Core Dimension\r\n                  </span>\r\n                </h3>\r\n                <p className=\"text-lg text-muted leading-relaxed\">\r\n                  Creating foundational time series models that treat time as a core dimension, enabling machines to reason over contexts and simulate dynamic futures.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </motion.div>\r\n      </section>\r\n\r\n      {/* Features Section - PREMIUM with YOUR COLORS */}\r\n      <section id=\"features\" className=\"py-24 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\r\n        {/* FANTASTIC backgrounds with your colors */}\r\n        <div className=\"absolute inset-0 bg-gray-50/50 dark:bg-gray-800/20\"></div>\r\n        <div className=\"absolute inset-0 dots-multi\"></div>\r\n        <div className=\"absolute inset-0 dots-kairos-beige\"></div>\r\n\r\n        <motion.div\r\n          className=\"max-w-6xl mx-auto relative\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          viewport={{ once: true, amount: 0.1 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          <motion.div\r\n            className=\"text-center mb-20\"\r\n            initial={{ y: 50, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7 }}\r\n          >\r\n            <h2 className=\"text-4xl md:text-5xl font-bold font-roboto mb-6 relative z-10\">\r\n              <span className=\"text-foreground\">Powerful </span>\r\n              <span className=\"text-red-400 relative text-outline-kairos text-glow-minimal\">\r\n                Features\r\n              </span>\r\n            </h2>\r\n            <p className=\"text-lg text-muted max-w-2xl mx-auto\">\r\n              Discover the cutting-edge capabilities that make Kairos the future of forecasting\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid md:grid-cols-1 lg:grid-cols-3 gap-8\">\r\n            {/* Feature 1 */}\r\n            <motion.div\r\n              className=\"glass-card p-8 flex flex-col h-full group cursor-pointer\"\r\n              initial={{ y: 50, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.1 }}\r\n              whileHover={{ y: -8, transition: { duration: 0.3 } }}\r\n            >\r\n              <div className=\"mb-6 p-4 rounded-2xl bg-red-50 dark:bg-red-900/20 w-fit group-hover:bg-red-100 dark:group-hover:bg-red-900/30 transition-all duration-300 border border-red-200 dark:border-red-800\">\r\n                <ChartBarIcon className=\"h-8 w-8 text-kairos-red group-hover:animate-pulse\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-foreground mb-4 leading-relaxed\">\r\n                Multi-Modal Data Processing\r\n              </h3>\r\n              <p className=\"text-muted leading-relaxed\">\r\n                Gathers and processes both structured (time series) and unstructured (text, news, web) data to generate accurate, real-world forecasts.\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Feature 2 */}\r\n            <motion.div\r\n              className=\"glass-card p-8 flex flex-col h-full group cursor-pointer\"\r\n              initial={{ y: 50, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.2 }}\r\n              whileHover={{ y: -8, transition: { duration: 0.3 } }}\r\n            >\r\n              <div className=\"mb-6 p-4 rounded-2xl bg-purple-50 dark:bg-purple-900/20 w-fit group-hover:bg-purple-100 dark:group-hover:bg-purple-900/30 transition-all duration-300 border border-purple-200 dark:border-purple-800\">\r\n                <CodeBracketIcon className=\"h-8 w-8 text-kairos-purple group-hover:animate-pulse\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-foreground mb-4 leading-relaxed\">\r\n                Causal Discovery & Transparency\r\n              </h3>\r\n              <p className=\"text-muted leading-relaxed\">\r\n                Performs causal discovery and provides transparent reasoning through explainability graphs and traceable logic.\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Feature 3 */}\r\n            <motion.div\r\n              className=\"glass-card p-8 flex flex-col h-full group cursor-pointer\"\r\n              initial={{ y: 50, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.3 }}\r\n              whileHover={{ y: -8, transition: { duration: 0.3 } }}\r\n            >\r\n              <div className=\"mb-6 p-4 rounded-2xl bg-amber-50 dark:bg-amber-900/20 w-fit group-hover:bg-amber-100 dark:group-hover:bg-amber-900/30 transition-all duration-300 border border-amber-200 dark:border-amber-800\">\r\n                <LightBulbIcon className=\"h-8 w-8 text-kairos-beige group-hover:animate-pulse\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-foreground mb-4 leading-relaxed\">\r\n                Foundation Models & Dashboard\r\n              </h3>\r\n              <p className=\"text-muted leading-relaxed\">\r\n                Combines time series foundation models with a user-facing dashboard for tailored insights, scenario simulations, and business-specific agent templates.\r\n              </p>\r\n            </motion.div>\r\n          </div>\r\n        </motion.div>\r\n      </section>\r\n\r\n      {/* Demo Section - PREMIUM with YOUR COLORS - Hidden on mobile */}\r\n      <section id=\"demo\" className=\"hidden lg:block py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\r\n        {/* FANTASTIC backgrounds with your colors */}\r\n        <div className=\"absolute inset-0 bg-gray-50/30 dark:bg-gray-800/10\"></div>\r\n        <div className=\"absolute inset-0 dots-multi\"></div>\r\n        <div className=\"absolute inset-0 dots-kairos-red\"></div>\r\n\r\n        <motion.div\r\n          className=\"max-w-7xl mx-auto relative z-10\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          viewport={{ once: true, amount: 0.1 }}\r\n          transition={{ duration: 0.8 }}\r\n        >\r\n          <motion.div\r\n            className=\"text-center mb-20\"\r\n            initial={{ y: 60, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n          >\r\n            <h2 className=\"text-5xl md:text-7xl font-bold font-roboto mb-8 relative z-10\">\r\n              <span className=\"text-foreground\">See Kairos </span>\r\n              <span className=\"text-red-400 relative text-outline-kairos text-glow-minimal\">\r\n                in Action\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-kairos-red/50 via-kairos-beige/50 to-kairos-purple/50 blur-2xl -z-10\"></div>\r\n              </span>\r\n            </h2>\r\n            <p className=\"text-xl text-muted max-w-3xl mx-auto leading-relaxed\">\r\n              Experience the future of forecasting with our interactive demonstration.\r\n              Watch as AI transforms complex data into actionable insights.\r\n            </p>\r\n          </motion.div>\r\n\r\n          {/* Premium Demo Container */}\r\n          <motion.div\r\n            className=\"relative\"\r\n            initial={{ y: 80, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n          >\r\n            {/* Main demo card - PREMIUM */}\r\n            <div className=\"glass-card p-12 relative overflow-hidden border-glow-kairos\">\r\n              {/* FANTASTIC overlays with your colors */}\r\n              <div className=\"absolute inset-0  pointer-events-none\"></div>\r\n              <div className=\"absolute inset-0 dots-kairos-purple opacity-15 pointer-events-none\"></div>\r\n\r\n              {/* Demo chart container - PREMIUM */}\r\n              <motion.div\r\n                className=\"relative z-10 bg-white/90 dark:bg-gray-800/90 rounded-3xl p-8 border border-gray-200 dark:border-gray-700 shadow-2xl overflow-hidden\"\r\n                initial={{ scale: 0.95, opacity: 0 }}\r\n                whileInView={{ scale: 1, opacity: 1 }}\r\n                viewport={{ once: true }}\r\n                transition={{ duration: 0.8, delay: 0.6 }}\r\n                whileHover={{\r\n                  scale: 1.02,\r\n                  transition: { duration: 0.4, ease: \"easeOut\" }\r\n                }}\r\n              >\r\n                <div className=\"absolute inset-0 dots-kairos-red opacity-10\"></div>\r\n                <div className=\"relative z-10\">\r\n                  <DemoChart />\r\n                </div>\r\n              </motion.div>\r\n            </div>\r\n          </motion.div>\r\n        </motion.div>\r\n      </section>\r\n\r\n      {/* Talk to Founders Section - PREMIUM */}\r\n      <section id=\"founders\" className=\"pt-24 pb-48 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\r\n        {/* FANTASTIC backgrounds with your colors */}\r\n        <div className=\"absolute inset-0 bg-gray-50/40 dark:bg-gray-800/20\"></div>\r\n        <div className=\"absolute inset-0 dots-multi\"></div>\r\n        <div className=\"absolute inset-0 dots-kairos-beige\"></div>\r\n        <motion.div\r\n          className=\"max-w-5xl mx-auto text-center relative z-10\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          viewport={{ once: true, amount: 0.1 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          <motion.h2\r\n            className=\"text-4xl md:text-5xl font-bold mb-8 text-foreground font-roboto\"\r\n            initial={{ y: 50, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7 }}\r\n          >\r\n            <span className=\"text-foreground\">Talk to Our </span>\r\n            <span className=\"text-red-400 relative text-outline-kairos text-glow-minimal\">\r\n              Founders\r\n            </span>\r\n          </motion.h2>\r\n          <motion.p\r\n            className=\"text-xl text-muted mb-12 max-w-3xl mx-auto leading-relaxed\"\r\n            initial={{ y: 30, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7, delay: 0.2 }}\r\n          >\r\n            Schedule a personalized demo and discover how Kairos can transform your forecasting workflow.\r\n            Learn directly from the team building the future of AI-powered predictions.\r\n          </motion.p>\r\n\r\n          <motion.div\r\n            className=\"flex flex-col lg:flex-row gap-12 items-center justify-center\"\r\n            initial={{ y: 30, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.5, delay: 0.4 }}\r\n          >\r\n            {/* CTA Button */}\r\n            <motion.div className=\"text-center\">\r\n              <motion.a\r\n                href=\"https://calendly.com/jajoo-kairosai/30min\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"inline-flex items-center px-10 py-5 bg-red-400 hover:bg-red-200 text-white font-bold rounded-full border-2 border-black transition-colors duration-300 text-lg\"\r\n                whileHover={{ scale: 1.05, y: -3 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                <CalendarDaysIcon className=\"h-6 w-6 mr-3\" />\r\n                Schedule a Demo\r\n              </motion.a>\r\n              <p className=\"text-muted text-sm mt-3\">30-minute personalized session</p>\r\n            </motion.div>\r\n\r\n            {/* Value Proposition */}\r\n           \r\n          </motion.div>\r\n\r\n\r\n        </motion.div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"py-16 px-4 sm:px-6 lg:px-8 border-t border-border/20\">\r\n        <motion.div\r\n          className=\"max-w-6xl mx-auto\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          viewport={{ once: true, amount: 0.1 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-12\">\r\n            <motion.div\r\n              className=\"md:col-span-2\"\r\n              initial={{ y: 20, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.1 }}\r\n            >\r\n              <h3 className=\"text-2xl font-bold text-foreground mb-4 font-roboto\">Kairos</h3>\r\n              <p className=\"text-muted leading-relaxed max-w-md\">\r\n                Trying to teach models what Einstein taught physics: that time isn't just a label, it's a dimension!\r\n              </p>\r\n              <div className=\"mt-6 text-sm text-muted\">\r\n                © 2024 Kairos AI. All rights reserved.\r\n              </div>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ y: 20, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.2 }}\r\n            >\r\n              <h3 className=\"text-lg text-foreground mb-4\">Links</h3>\r\n              <ul className=\"space-y-2\">\r\n                <li><Link href=\"/\" className=\"text-muted hover:text-primary transition-colors\">Home</Link></li>\r\n                <li><Link href=\"/about\" className=\"text-muted hover:text-primary transition-colors\">About</Link></li>\r\n                <li><Link href=\"/contact\" className=\"text-muted hover:text-primary transition-colors\">Contact</Link></li>\r\n              </ul>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ y: 20, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.3 }}\r\n            >\r\n              <h3 className=\"text-lg text-foreground mb-4\">Legal</h3>\r\n              <ul className=\"space-y-2\">\r\n                <li><Link href=\"/privacy\" className=\"text-muted hover:text-primary transition-colors\">Privacy Policy</Link></li>\r\n                <li><Link href=\"/terms\" className=\"text-muted hover:text-primary transition-colors\">Terms of Service</Link></li>\r\n              </ul>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ y: 20, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.4 }}\r\n            >\r\n              <h3 className=\"text-lg font-semibold text-foreground mb-4\">Connect</h3>\r\n              <div className=\"flex space-x-4\">\r\n                <motion.a\r\n                  href=\"https://x.com/kairos_ai__\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"text-muted hover:text-primary transition-colors duration-200 p-2 rounded-lg hover:bg-secondary/10\"\r\n                  whileHover={{ scale: 1.1 }}\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\r\n                    <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\r\n                  </svg>\r\n                </motion.a>\r\n                <motion.a\r\n                  href=\"https://www.linkedin.com/company/kairos-ai/\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"text-muted hover:text-primary transition-colors duration-200 p-2 rounded-lg hover:bg-secondary/10\"\r\n                  whileHover={{ scale: 1.1 }}\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\r\n                    <path d=\"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\" />\r\n                  </svg>\r\n                </motion.a>\r\n                <motion.a\r\n                  href=\"http://www.github.com/KairosAI-IN\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"text-muted hover:text-primary transition-colors duration-200 p-2 rounded-lg hover:bg-secondary/10\"\r\n                  whileHover={{ scale: 1.1 }}\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\r\n                    <path fillRule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </motion.a>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          <motion.div\r\n            className=\"mt-12 pt-8 border-t border-white/10 text-center\"\r\n            initial={{ opacity: 0 }}\r\n            whileInView={{ opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.5, delay: 0.6 }}\r\n          >\r\n            <p className=\"text-gray-400\">\r\n              &copy; {new Date().getFullYear()} Kairos AI. All rights reserved.\r\n            </p>\r\n          </motion.div>\r\n        </motion.div>\r\n      </footer>\r\n      </FontSwitcher>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAjBA;;;;;;;;;;;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI;YACF,yBAAyB;YACzB,MAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;gBAC3B,MAAM;gBACN,gBAAgB;gBAChB;YACF;YAEA,wCAAwC;YACxC,MAAM,iBAAiB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAE5E,gCAAgC;YAChC,IAAI,eAAe,QAAQ,CAAC,MAAM,WAAW,KAAK;gBAChD,MAAM;gBACN,gBAAgB;gBAChB;YACF;YAEA,0CAA0C;YAC1C,IAAI;gBACF,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,cAAc;gBAC9B,SAAS,MAAM,CAAC,QAAQ;gBACxB,SAAS,MAAM,CAAC,SAAS;gBACzB,SAAS,MAAM,CAAC,WAAW,CAAC;;OAE7B,EAAE,MAAM;WACJ,EAAE,IAAI,OAAO,cAAc,GAAG;;YAE7B,EAAE,UAAU,SAAS,EAAE;gBAE3B,MAAM,WAAW,MAAM,MAAM,oCAAoC;oBAC/D,QAAQ;oBACR,MAAM;gBACR;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;oBAClB,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,gBAAgB;gBAC9B,OAAO;oBACL,QAAQ,GAAG,CAAC,uBAAuB,OAAO,OAAO;gBACnD;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,GAAG,CAAC,0BAA0B;YACxC;YAEA,6CAA6C;YAC7C,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,aAAa;YACzB,QAAQ,GAAG,CAAC,YAAY,IAAI,OAAO,cAAc;YACjD,QAAQ,GAAG,CAAC;YAEZ,kEAAkE;YAClE,MAAM,gBAAgB;mBAAI;gBAAgB,MAAM,WAAW;aAAG;YAC9D,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YAEtD,6BAA6B;YAC7B,aAAa,OAAO,CAAC,2BAA2B,IAAI,OAAO,WAAW;YAEtE,8CAA8C;YAC9C,eAAe;YACf,SAAS;QAEX,EAAE,OAAO,KAAK;YACZ,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,8CAA8C;IAC9C,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,8HAAA,CAAA,UAAY;;8BACX,6LAAC,+IAAA,CAAA,UAAsB;;;;;8BACvB,6LAAC,qIAAA,CAAA,UAAY;;;;;8BACb,6LAAC,uIAAA,CAAA,UAAc;;;;;8BACf,6LAAC,gJAAA,CAAA,UAAY;;;;;8BAGf,6LAAC;oBAAQ,WAAU;;sCAEjB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACtB,6LAAC;4BAAI,WAAU;sCACN,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAQ;wCACR,SAAQ;wCACR,UAAU;;0DAEV,6LAAC,qNAAA,CAAA,SAAM,CAAC,EAAE;gDACR,WAAU;gDACV,UAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAkB;;;;;;kEAClC,6LAAC;wDAAK,WAAU;kEAA8D;;;;;;;;;;;;0DAKhF,6LAAC,qNAAA,CAAA,SAAM,CAAC,CAAC;gDACP,WAAU;gDACV,UAAU;0DACX;;;;;;0DAKD,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,UAAU;gDACV,IAAG;0DAEF,4BACC,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEAA2C;;;;;;sEAC1D,6LAAC;4DAAG,WAAU;sEAAyD;;;;;;sEAGvE,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;;;;;;yEAKpD,6LAAC;oDAAK,UAAU;oDAAc,WAAU;;sEACtC,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4DACxC,aAAY;4DACZ,QAAQ;4DACR,UAAU;4DACV,WAAU;;;;;;sEAEZ,6LAAC,qNAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,MAAK;4DACL,UAAU;4DACV,WAAW,CAAC,uIAAuI,EACjJ,eACI,iDACA,yDACJ;4DACF,YAAY,CAAC,eAAe;gEAAE,OAAO;gEAAM,GAAG,CAAC;4DAAE,IAAI,CAAC;4DACtD,UAAU,CAAC,eAAe;gEAAE,OAAO;4DAAK,IAAI,CAAC;sEAE5C,6BACC;;kFACE,6LAAC;wEAAI,WAAU;;;;;;oEAA6F;;+EAI9G;;;;;;;;;;;;;;;;;;;;;;;kDASZ,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;kDAExC,cAAA,6LAAC,uJAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;;;;;sCAM1B,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;gCAAG,UAAU;4BAAE;sCAEpC,cAAA,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,QAAQ;oCAAU,UAAU;gCAAI;gCAC9C,SAAS;oCACP,MAAM,KAAK,SAAS,cAAc,CAAC;oCACnC,IAAI,IAAI;wCACN,MAAM,UAAU,CAAC,IAAI,gDAAgD;wCACrE,MAAM,IAAI,GAAG,qBAAqB,GAAG,GAAG,GAAG,OAAO,WAAW,GAAG;wCAChE,OAAO,QAAQ,CAAC;4CAAE,KAAK;4CAAG,UAAU;wCAAS;oCAC/C;gCACF;gCACA,OAAO;oCAAE,QAAQ;gCAAU;0CAE3B,cAAA,6LAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAMhC,6LAAC;oBAAQ,IAAG;oBAAQ,WAAU;;sCAEzB,6LAAC;4BAAI,WAAU;;;;;;sCAClB,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,YAAY;gCAAE,UAAU;4BAAI;;8CAG5B,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAkB;;;;;;8DAClC,6LAAC;oDAAK,WAAU;8DAA8D;;;;;;;;;;;;sDAIhF,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;;;;;;;8CAMtE,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,YAAY;wCAAE,GAAG,CAAC;wCAAG,YAAY;4CAAE,UAAU;wCAAI;oCAAE;8CAEnD,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEAAiD;;;;;;;;;;;kEAInE,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;0DAMpD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,kLAAA,CAAA,iBAAc;wDACb,KAAI;wDACJ,IAAI;wDACJ,QAAQ;wDACR,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,WAAW;4DACX,QAAQ;wDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQV,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,YAAY;wCAAE,GAAG,CAAC;wCAAG,YAAY;4CAAE,UAAU;wCAAI;oCAAE;8CAEnD,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,kLAAA,CAAA,iBAAc;wDACb,KAAI;wDACJ,IAAI;wDACJ,QAAQ;wDACR,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,WAAW;4DACX,QAAQ;wDACV;;;;;;;;;;;;;;;;0DAMN,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEAAiD;;;;;;;;;;;kEAInE,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU5D,6LAAC;oBAAQ,IAAG;oBAAW,WAAU;;sCAE/B,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAkB;;;;;;8DAClC,6LAAC;oDAAK,WAAU;8DAA8D;;;;;;;;;;;;sDAIhF,6LAAC;4CAAE,WAAU;sDAAuC;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,aAAa;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,YAAY;gDAAE,GAAG,CAAC;gDAAG,YAAY;oDAAE,UAAU;gDAAI;4CAAE;;8DAEnD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,6LAAC;oDAAG,WAAU;8DAA6D;;;;;;8DAG3E,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAM5C,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,aAAa;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,YAAY;gDAAE,GAAG,CAAC;gDAAG,YAAY;oDAAE,UAAU;gDAAI;4CAAE;;8DAEnD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;8DAE7B,6LAAC;oDAAG,WAAU;8DAA6D;;;;;;8DAG3E,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAM5C,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,aAAa;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,YAAY;gDAAE,GAAG,CAAC;gDAAG,YAAY;oDAAE,UAAU;gDAAI;4CAAE;;8DAEnD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,4NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,6LAAC;oDAAG,WAAU;8DAA6D;;;;;;8DAG3E,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASlD,6LAAC;oBAAQ,IAAG;oBAAO,WAAU;;sCAE3B,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAkB;;;;;;8DAClC,6LAAC;oDAAK,WAAU;;wDAA8D;sEAE5E,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAGnB,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;;;;;;;8CAOtE,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAGxC,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;oDAAM,SAAS;gDAAE;gDACnC,aAAa;oDAAE,OAAO;oDAAG,SAAS;gDAAE;gDACpC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,YAAY;oDACV,OAAO;oDACP,YAAY;wDAAE,UAAU;wDAAK,MAAM;oDAAU;gDAC/C;;kEAEA,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,6IAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStB,6LAAC;oBAAQ,IAAG;oBAAW,WAAU;;sCAE/B,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC,qNAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;sDAClC,6LAAC;4CAAK,WAAU;sDAA8D;;;;;;;;;;;;8CAIhF,6LAAC,qNAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;8CAKD,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAGxC,cAAA,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,WAAU;;0DACpB,6LAAC,qNAAA,CAAA,SAAM,CAAC,CAAC;gDACP,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,YAAY;oDAAE,OAAO;oDAAM,GAAG,CAAC;gDAAE;gDACjC,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,6LAAC,gOAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG/C,6LAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAY/C,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,UAAU;4BAAE,MAAM;4BAAM,QAAQ;wBAAI;wBACpC,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,GAAG;4CAAI,SAAS;wCAAE;wCAC7B,aAAa;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAAsC;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;kDAK3C,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;4CAAI,SAAS;wCAAE;wCAC7B,aAAa;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAI,WAAU;sEAAkD;;;;;;;;;;;kEAC/E,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;sEAAkD;;;;;;;;;;;kEACpF,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAW,WAAU;sEAAkD;;;;;;;;;;;;;;;;;;;;;;;kDAI1F,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;4CAAI,SAAS;wCAAE;wCAC7B,aAAa;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAW,WAAU;sEAAkD;;;;;;;;;;;kEACtF,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;sEAAkD;;;;;;;;;;;;;;;;;;;;;;;kDAIxF,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;4CAAI,SAAS;wCAAE;wCAC7B,aAAa;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,6LAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAC3D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,SAAM,CAAC,CAAC;wDACP,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAI;kEAEzB,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;4DAAY,eAAY;sEAC3E,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;kEAGZ,6LAAC,qNAAA,CAAA,SAAM,CAAC,CAAC;wDACP,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAI;kEAEzB,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;4DAAY,eAAY;sEAC3E,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;kEAGZ,6LAAC,qNAAA,CAAA,SAAM,CAAC,CAAC;wDACP,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAI;kEAEzB,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;4DAAY,eAAY;sEAC3E,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAmtB,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOjwB,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,aAAa;oCAAE,SAAS;gCAAE;gCAC1B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,6LAAC;oCAAE,WAAU;;wCAAgB;wCACnB,IAAI,OAAO,WAAW;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;GAhsBwB;KAAA", "debugId": null}}]}