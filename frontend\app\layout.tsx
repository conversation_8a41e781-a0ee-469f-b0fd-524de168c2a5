"use client"

import { inter, inknutAntiqua } from './fonts';
import "./globals.css"
import { usePathname } from 'next/navigation';
import BackgroundBlobs from './components/BackgroundBlobs';
import { ChatProvider } from './contexts/ChatContext';
import { ThemeProvider } from './contexts/ThemeContext';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isOnChatPage = pathname?.startsWith('/chats');

  return (
    <html lang="en" className={`${inter.variable} ${inknutAntiqua.variable}`} suppressHydrationWarning>
      <head>
        <title>Kairos AI</title>
        <meta name="description" content="Kairos AI - Time-aware intelligence for forecasting and decision-making" />
        <link rel="icon" href="/kairoslogo.png" />
        <link rel="apple-touch-icon" href="/kairoslogo.png" />
      </head>
      <body className="bg-white dark:bg-black">
        <ThemeProvider>
          {!isOnChatPage && <BackgroundBlobs />}
          <ChatProvider>
            {children}
          </ChatProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}





