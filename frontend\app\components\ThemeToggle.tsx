'use client';

import React from 'react';
import { motion } from 'motion/react';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';

interface ThemeToggleProps {
  className?: string;
}

export default function ThemeToggle({ className = '' }: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();

  return (
    <motion.button
      onClick={toggleTheme}
      className={`relative flex items-center justify-center w-14 h-7 sm:w-16 sm:h-8 rounded-full transition-all duration-500 shadow-lg border touch-manipulation ${
        theme === 'dark'
          ? 'bg-gray-700 dark:bg-gray-600 shadow-gray-700/30 border-white/20'
          : 'bg-gray-200 shadow-gray-200/30 border-black/20'
      } ${className}`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {/* Toggle circle - Apple-like */}
      <motion.div
  className={`absolute top-0 bottom-0 my-auto w-6 h-6 sm:w-7 sm:h-7 rounded-full flex items-center justify-center shadow-xl transition-colors duration-500 ${
    theme === 'dark'
      ? 'bg-white border border-gray-300 left-[calc(100%-26px)] sm:left-[calc(100%-30px)]'
      : 'bg-white border border-gray-300 shadow-lg left-[2px]'
  }`}
  layout
  transition={{
    type: 'spring',
    stiffness: 700,
    damping: 30,
  }}
>

        {/* Icon with smooth transition */}
        <motion.div
          initial={false}
          animate={{
            scale: 1,
            rotate: theme === 'dark' ? 0 : 180,
          }}
          transition={{
            duration: 0.5,
            ease: 'easeInOut'
          }}
        >
          {theme === 'dark' ? (
            <MoonIcon className="w-4 h-4 text-gray-700" />
          ) : (
            <SunIcon className="w-4 h-4 text-yellow-500" />
          )}
        </motion.div>
      </motion.div>

      {/* Background icons with better positioning */}
      <div className="absolute inset-0 flex items-center justify-between px-2.5">
        <motion.div
          animate={{
            opacity: theme === 'light' ? 0 : 0.6,
            scale: theme === 'light' ? 0.8 : 1
          }}
          transition={{ duration: 0.4 }}
        >
          <SunIcon className="w-3.5 h-3.5 text-yellow-400/70" />
        </motion.div>
        <motion.div
          animate={{
            opacity: theme === 'dark' ? 0 : 0.6,
            scale: theme === 'dark' ? 0.8 : 1
          }}
          transition={{ duration: 0.4 }}
        >
          <MoonIcon className="w-3.5 h-3.5 text-gray-600/70" />
        </motion.div>
      </div>
    </motion.button>
  );
}
