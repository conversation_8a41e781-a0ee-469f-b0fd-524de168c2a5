!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={})}(this,(function(t){"use strict";function e(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const s=(t,e,n)=>n>e?e:n<t?t:n;let i=()=>{};const r={},o=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),a=t=>/^0[^.\s]+$/u.test(t);function l(t){let e;return()=>(void 0===e&&(e=t()),e)}const u=t=>t,c=(t,e)=>n=>e(t(n)),h=(...t)=>t.reduce(c),d=(t,e,n)=>{const s=e-t;return 0===s?1:(n-t)/s};class p{constructor(){this.subscriptions=[]}add(t){return e(this.subscriptions,t),()=>n(this.subscriptions,t)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,n);else for(let i=0;i<s;i++){const s=this.subscriptions[i];s&&s(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const f=t=>1e3*t,m=t=>t/1e3;function g(t,e){return e?t*(1e3/e):0}const y=new Set;const v=(t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t},w=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function b(t,e,n,s){if(t===e&&n===s)return u;const i=e=>function(t,e,n,s,i){let r,o,a=0;do{o=e+(n-e)/2,r=w(o,s,i)-t,r>0?n=o:e=o}while(Math.abs(r)>1e-7&&++a<12);return o}(e,0,1,t,n);return t=>0===t||1===t?t:w(i(t),e,s)}const T=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,x=t=>e=>1-t(1-e),M=b(.33,1.53,.69,.99),V=x(M),S=T(V),A=t=>(t*=2)<1?.5*V(t):.5*(2-Math.pow(2,-10*(t-1))),E=t=>1-Math.sin(Math.acos(t)),k=x(E),P=T(E),C=b(.42,0,1,1),F=b(0,0,.58,1),O=b(.42,0,.58,1);const R=t=>Array.isArray(t)&&"number"!=typeof t[0];function B(t,e){return R(t)?t[v(0,t.length,e)]:t}const D=t=>Array.isArray(t)&&"number"==typeof t[0],L={linear:u,easeIn:C,easeInOut:O,easeOut:F,circIn:E,circInOut:P,circOut:k,backIn:V,backInOut:S,backOut:M,anticipate:A},I=t=>{if(D(t)){t.length;const[e,n,s,i]=t;return b(e,n,s,i)}return"string"==typeof t?L[t]:t},W=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],N={value:null,addProjectionMetrics:null};function j(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,a=W.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,s=new Set,i=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(c.schedule(e),t()),l++,e(a)}const c={schedule:(t,e=!1,r=!1)=>{const a=r&&i?n:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{a=t,i?r=!0:(i=!0,[n,s]=[s,n],n.forEach(u),e&&N.value&&N.value.frameloop[e].push(l),l=0,n.clear(),i=!1,r&&(r=!1,c.process(t)))}};return c}(o,e?n:void 0),t)),{}),{setup:l,read:u,resolveKeyframes:c,preUpdate:h,update:d,preRender:p,render:f,postRender:m}=a,g=()=>{const o=r.useManualTiming?i.timestamp:performance.now();n=!1,r.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,l.process(i),u.process(i),c.process(i),h.process(i),d.process(i),p.process(i),f.process(i),m.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(g))};return{schedule:W.reduce(((e,r)=>{const o=a[r];return e[r]=(e,r=!1,a=!1)=>(n||(n=!0,s=!0,i.isProcessing||t(g)),o.schedule(e,r,a)),e}),{}),cancel:t=>{for(let e=0;e<W.length;e++)a[W[e]].cancel(t)},state:i,steps:a}}const{schedule:K,cancel:$,state:z,steps:U}=j("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0);let Y;function H(){Y=void 0}const X={now:()=>(void 0===Y&&X.set(z.isProcessing||r.useManualTiming?z.timestamp:performance.now()),Y),set:t=>{Y=t,queueMicrotask(H)}},q={layout:0,mainThread:0,waapi:0},G=t=>e=>"string"==typeof e&&e.startsWith(t),Z=G("--"),_=G("var(--"),J=t=>!!_(t)&&Q.test(t.split("/*")[0].trim()),Q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},et={...tt,transform:t=>s(0,1,t)},nt={...tt,default:1},st=t=>Math.round(1e5*t)/1e5,it=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const rt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ot=(t,e)=>n=>Boolean("string"==typeof n&&rt.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),at=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[i,r,o,a]=s.match(it);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},lt={...tt,transform:t=>Math.round((t=>s(0,255,t))(t))},ut={test:ot("rgb","red"),parse:at("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+lt.transform(t)+", "+lt.transform(e)+", "+lt.transform(n)+", "+st(et.transform(s))+")"};const ct={test:ot("#"),parse:function(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}},transform:ut.transform},ht=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),dt=ht("deg"),pt=ht("%"),ft=ht("px"),mt=ht("vh"),gt=ht("vw"),yt=(()=>({...pt,parse:t=>pt.parse(t)/100,transform:t=>pt.transform(100*t)}))(),vt={test:ot("hsl","hue"),parse:at("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+pt.transform(st(e))+", "+pt.transform(st(n))+", "+st(et.transform(s))+")"},wt={test:t=>ut.test(t)||ct.test(t)||vt.test(t),parse:t=>ut.test(t)?ut.parse(t):vt.test(t)?vt.parse(t):ct.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ut.transform(t):vt.transform(t)},bt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Tt="number",xt="color",Mt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Vt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const o=e.replace(Mt,(t=>(wt.test(t)?(s.color.push(r),i.push(xt),n.push(wt.parse(t))):t.startsWith("var(")?(s.var.push(r),i.push("var"),n.push(t)):(s.number.push(r),i.push(Tt),n.push(parseFloat(t))),++r,"${}"))).split("${}");return{values:n,split:o,indexes:s,types:i}}function St(t){return Vt(t).values}function At(t){const{split:e,types:n}=Vt(t),s=e.length;return t=>{let i="";for(let r=0;r<s;r++)if(i+=e[r],void 0!==t[r]){const e=n[r];i+=e===Tt?st(t[r]):e===xt?wt.transform(t[r]):t[r]}return i}}const Et=t=>"number"==typeof t?0:t;const kt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(it)?.length||0)+(t.match(bt)?.length||0)>0},parse:St,createTransformer:At,getAnimatableNone:function(t){const e=St(t);return At(t)(e.map(Et))}};function Pt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Ct({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let i=0,r=0,o=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,a=2*n-s;i=Pt(a,s,t+1/3),r=Pt(a,s,t),o=Pt(a,s,t-1/3)}else i=r=o=n;return{red:Math.round(255*i),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}function Ft(t,e){return n=>n>0?e:t}const Ot=(t,e,n)=>t+(e-t)*n,Rt=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Bt=[ct,ut,vt];function Dt(t){const e=(n=t,Bt.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let s=e.parse(t);return e===vt&&(s=Ct(s)),s}const Lt=(t,e)=>{const n=Dt(t),s=Dt(e);if(!n||!s)return Ft(t,e);const i={...n};return t=>(i.red=Rt(n.red,s.red,t),i.green=Rt(n.green,s.green,t),i.blue=Rt(n.blue,s.blue,t),i.alpha=Ot(n.alpha,s.alpha,t),ut.transform(i))},It=new Set(["none","hidden"]);function Wt(t,e){return It.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Nt(t,e){return n=>Ot(t,e,n)}function jt(t){return"number"==typeof t?Nt:"string"==typeof t?J(t)?Ft:wt.test(t)?Lt:zt:Array.isArray(t)?Kt:"object"==typeof t?wt.test(t)?Lt:$t:Ft}function Kt(t,e){const n=[...t],s=n.length,i=t.map(((t,n)=>jt(t)(t,e[n])));return t=>{for(let e=0;e<s;e++)n[e]=i[e](t);return n}}function $t(t,e){const n={...t,...e},s={};for(const i in n)void 0!==t[i]&&void 0!==e[i]&&(s[i]=jt(t[i])(t[i],e[i]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const zt=(t,e)=>{const n=kt.createTransformer(e),s=Vt(t),i=Vt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?It.has(t)&&!i.values.length||It.has(e)&&!s.values.length?Wt(t,e):h(Kt(function(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],o=t.indexes[r][s[r]],a=t.values[o]??0;n[i]=a,s[r]++}return n}(s,i),i.values),n):Ft(t,e)};function Ut(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Ot(t,e,n);return jt(t)(t,e)}const Yt=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>K.update(e,t),stop:()=>$(e),now:()=>z.isProcessing?z.timestamp:X.now()}},Ht=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let e=0;e<i;e++)s+=t(e/(i-1))+", ";return`linear(${s.substring(0,s.length-2)})`},Xt=2e4;function qt(t){let e=0;let n=t.next(e);for(;!n.done&&e<Xt;)e+=50,n=t.next(e);return e>=Xt?1/0:e}function Gt(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(qt(s),Xt);return{type:"keyframes",ease:t=>s.next(i*t).value/e,duration:m(i)}}function Zt(t,e,n){const s=Math.max(e-5,0);return g(n-t(s),e-s)}const _t=100,Jt=10,Qt=1,te=0,ee=800,ne=.3,se=.3,ie={granular:.01,default:2},re={granular:.005,default:.5},oe=.01,ae=10,le=.05,ue=1,ce=.001;function he({duration:t=ee,bounce:e=ne,velocity:n=te,mass:i=Qt}){let r,o,a=1-e;a=s(le,ue,a),t=s(oe,ae,m(t)),a<1?(r=e=>{const s=e*a,i=s*t,r=s-n,o=pe(e,a),l=Math.exp(-i);return ce-r/o*l},o=e=>{const s=e*a*t,i=s*n+n,o=Math.pow(a,2)*Math.pow(e,2)*t,l=Math.exp(-s),u=pe(Math.pow(e,2),a);return(-r(e)+ce>0?-1:1)*((i-o)*l)/u}):(r=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const l=function(t,e,n){let s=n;for(let n=1;n<de;n++)s-=t(s)/e(s);return s}(r,o,5/t);if(t=f(t),isNaN(l))return{stiffness:_t,damping:Jt,duration:t};{const e=Math.pow(l,2)*i;return{stiffness:e,damping:2*a*Math.sqrt(i*e),duration:t}}}const de=12;function pe(t,e){return t*Math.sqrt(1-e*e)}const fe=["duration","bounce"],me=["stiffness","damping","mass"];function ge(t,e){return e.some((e=>void 0!==t[e]))}function ye(t=se,e=ne){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:r}=n;const o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:c,mass:h,duration:d,velocity:p,isResolvedFromDuration:g}=function(t){let e={velocity:te,stiffness:_t,damping:Jt,mass:Qt,isResolvedFromDuration:!1,...t};if(!ge(t,me)&&ge(t,fe))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),r=i*i,o=2*s(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:Qt,stiffness:r,damping:o}}else{const n=he(t);e={...e,...n,mass:Qt},e.isResolvedFromDuration=!0}return e}({...n,velocity:-m(n.velocity||0)}),y=p||0,v=c/(2*Math.sqrt(u*h)),w=a-o,b=m(Math.sqrt(u/h)),T=Math.abs(w)<5;let x;if(i||(i=T?ie.granular:ie.default),r||(r=T?re.granular:re.default),v<1){const t=pe(b,v);x=e=>{const n=Math.exp(-v*b*e);return a-n*((y+v*b*w)/t*Math.sin(t*e)+w*Math.cos(t*e))}}else if(1===v)x=t=>a-Math.exp(-b*t)*(w+(y+b*w)*t);else{const t=b*Math.sqrt(v*v-1);x=e=>{const n=Math.exp(-v*b*e),s=Math.min(t*e,300);return a-n*((y+v*b*w)*Math.sinh(s)+t*w*Math.cosh(s))/t}}const M={calculatedDuration:g&&d||null,next:t=>{const e=x(t);if(g)l.done=t>=d;else{let n=0===t?y:0;v<1&&(n=0===t?f(y):Zt(x,t,e));const s=Math.abs(n)<=i,o=Math.abs(a-e)<=r;l.done=s&&o}return l.value=l.done?a:e,l},toString:()=>{const t=Math.min(qt(M),Xt),e=Ht((e=>M.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return M}function ve({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let f=n*e;const m=h+f,g=void 0===o?m:o(m);g!==m&&(f=g-h);const y=t=>-f*Math.exp(-t/s),v=t=>g+y(t),w=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let b,T;const x=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(b=t,T=ye({keyframes:[d.value,p(d.value)],velocity:Zt(v,t,d.value),damping:i,stiffness:r,restDelta:u,restSpeed:c}))};return x(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==b||(e=!0,w(t),x(t)),void 0!==b&&t>=b?T.next(t-b):(!e&&w(t),d)}}}function we(t,e,{clamp:n=!0,ease:i,mixer:o}={}){const a=t.length;if(e.length,1===a)return()=>e[0];if(2===a&&e[0]===e[1])return()=>e[1];const l=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),e=[...e].reverse());const c=function(t,e,n){const s=[],i=n||r.mix||Ut,o=t.length-1;for(let n=0;n<o;n++){let r=i(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||u:e;r=h(t,r)}s.push(r)}return s}(e,i,o),p=c.length,f=n=>{if(l&&n<t[0])return e[0];let s=0;if(p>1)for(;s<t.length-2&&!(n<t[s+1]);s++);const i=d(t[s],t[s+1],n);return c[s](i)};return n?e=>f(s(t[0],t[a-1],e)):f}function be(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=d(0,e,s);t.push(Ot(n,1,i))}}function Te(t){const e=[0];return be(e,t.length-1),e}function xe(t,e){return t.map((t=>t*e))}function Me(t,e){return t.map((()=>e||O)).splice(0,t.length-1)}function Ve({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=R(s)?s.map(I):I(s),r={done:!1,value:e[0]},o=we(xe(n&&n.length===e.length?n:Te(e),t),e,{ease:Array.isArray(i)?i:Me(e,i)});return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}ye.applyToOptions=t=>{const e=Gt(t,100,ye);return t.ease=e.ease,t.duration=f(e.duration),t.type="keyframes",t};const Se=t=>null!==t;function Ae(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(Se),o=i<0||e&&"loop"!==n&&e%2==1?0:r.length-1;return o&&void 0!==s?s:r[o]}const Ee={decay:ve,inertia:ve,tween:Ve,keyframes:Ve,spring:ye};function ke(t){"string"==typeof t.type&&(t.type=Ee[t.type])}class Pe{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ce=t=>t/100;class Fe extends Pe{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){const{motionValue:t}=this.options;t&&t.updatedAt!==X.now()&&this.tick(X.now())}if(this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:e}=this.options;e&&e()},q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;ke(t);const{type:e=Ve,repeat:n=0,repeatDelay:s=0,repeatType:i,velocity:r=0}=t;let{keyframes:o}=t;const a=e||Ve;a!==Ve&&"number"!=typeof o[0]&&(this.mixKeyframes=h(Ce,Ut(o[0],o[1])),o=[0,100]);const l=a({...t,keyframes:o});"mirror"===i&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=qt(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:r,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:l}=this;if(null===this.startTime)return n.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:d,repeatDelay:p,type:f,onUpdate:m,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>i;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let w=this.currentTime,b=n;if(h){const t=Math.min(this.currentTime,i)/a;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,h+1);Boolean(e%2)&&("reverse"===d?(n=1-n,p&&(n-=p/a)):"mirror"===d&&(b=o)),w=s(0,1,n)*a}const T=v?{done:!1,value:c[0]}:b.next(w);r&&(T.value=r(T.value));let{done:x}=T;v||null===l||(x=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const M=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return M&&f!==ve&&(T.value=Ae(c,this.options,g,this.speed)),m&&m(T.value),M&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return m(this.calculatedDuration)}get time(){return m(this.currentTime)}set time(t){t=f(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(X.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=m(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Yt,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(X.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Oe(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Re=t=>180*t/Math.PI,Be=t=>{const e=Re(Math.atan2(t[1],t[0]));return Le(e)},De={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Be,rotateZ:Be,skewX:t=>Re(Math.atan(t[1])),skewY:t=>Re(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Le=t=>((t%=360)<0&&(t+=360),t),Ie=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),We=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Ne={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ie,scaleY:We,scale:t=>(Ie(t)+We(t))/2,rotateX:t=>Le(Re(Math.atan2(t[6],t[5]))),rotateY:t=>Le(Re(Math.atan2(-t[2],t[0]))),rotateZ:Be,rotate:Be,skewX:t=>Re(Math.atan(t[4])),skewY:t=>Re(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function je(t){return t.includes("scale")?1:0}function Ke(t,e){if(!t||"none"===t)return je(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Ne,i=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=De,i=e}if(!i)return je(e);const r=s[e],o=i[1].split(",").map(ze);return"function"==typeof r?r(o):o[r]}const $e=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Ke(n,e)};function ze(t){return parseFloat(t.trim())}const Ue=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ye=(()=>new Set(Ue))(),He=t=>t===tt||t===ft,Xe=new Set(["x","y","z"]),qe=Ue.filter((t=>!Xe.has(t)));const Ge={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Ke(e,"x"),y:(t,{transform:e})=>Ke(e,"y")};Ge.translateX=Ge.x,Ge.translateY=Ge.y;const Ze=new Set;let _e=!1,Je=!1,Qe=!1;function tn(){if(Je){const t=Array.from(Ze).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return qe.forEach((n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}Je=!1,_e=!1,Ze.forEach((t=>t.complete(Qe))),Ze.clear()}function en(){Ze.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(Je=!0)}))}function nn(){Qe=!0,en(),tn(),Qe=!1}class sn{constructor(t,e,n,s,i,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=i,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(Ze.add(this),_e||(_e=!0,K.read(en),K.resolveKeyframes(tn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const i=s?.get(),r=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){const s=n.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===i&&s.set(t[0])}Oe(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),Ze.delete(this)}cancel(){"scheduled"===this.state&&(Ze.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const rn=t=>t.startsWith("--");function on(t,e,n){rn(e)?t.style.setProperty(e,n):t.style[e]=n}const an=l((()=>void 0!==window.ScrollTimeline)),ln={};function un(t,e){const n=l(t);return()=>ln[e]??n()}const cn=un((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),hn=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,dn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:hn([0,.65,.55,1]),circOut:hn([.55,0,1,.45]),backIn:hn([.31,.01,.66,-.59]),backOut:hn([.33,1.53,.69,.99])};function pn(t,e){return t?"function"==typeof t?cn()?Ht(t,e):"ease-out":D(t)?hn(t):Array.isArray(t)?t.map((t=>pn(t,e)||dn.easeOut)):dn[t]:void 0}function fn(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=pn(a,i);Array.isArray(h)&&(c.easing=h),N.value&&q.waapi++;const d={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);const p=t.animate(c,d);return N.value&&p.finished.finally((()=>{q.waapi--})),p}function mn(t){return"function"==typeof t&&"applyToOptions"in t}function gn({type:t,...e}){return mn(t)&&cn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class yn extends Pe{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:s,pseudoElement:i,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=Boolean(i),this.allowFlatten=r,this.options=t,t.type;const l=gn(t);this.animation=fn(e,n,s,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const t=Ae(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):on(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return m(Number(t))}get time(){return m(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=f(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&an()?(this.animation.timeline=t,u):e(this)}}const vn={anticipate:A,backInOut:S,circInOut:P};function wn(t){"string"==typeof t.ease&&t.ease in vn&&(t.ease=vn[t.ease])}class bn extends yn{constructor(t){wn(t),ke(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:s,element:i,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const o=new Fe({...r,autoplay:!1}),a=f(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}const Tn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!kt.test(t)&&"0"!==t||t.startsWith("url(")));const xn=new Set(["opacity","clipPath","filter","transform"]),Mn=l((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));function Vn(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:o}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Mn()&&n&&xn.has(n)&&("transform"!==n||!l)&&!a&&!s&&"mirror"!==i&&0!==r&&"inertia"!==o}class Sn extends Pe{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r="loop",keyframes:o,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=X.now();const h={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:i,repeatType:r,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||sn;this.keyframeResolver=new d(o,((t,e,n)=>this.onKeyframesResolved(t,e,h,!n)),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,s){this.keyframeResolver=void 0;const{name:i,type:o,velocity:a,delay:l,isHandoff:c,onUpdate:h}=n;this.resolvedAt=X.now(),function(t,e,n,s){const i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;const r=t[t.length-1],o=Tn(i,e),a=Tn(r,e);return!(!o||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||mn(n))&&s)}(t,i,o,a)||(!r.instantAnimations&&l||h?.(Ae(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const d={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},p=!c&&Vn(d)?new bn({...d,element:d.motionValue.owner.current}):new Fe(d);p.finished.then((()=>this.notifyFinished())).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(this.keyframeResolver?.resume(),nn()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class An{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>t.finished)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map((e=>e.attachTimeline(t)));return()=>{e.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class En extends An{then(t,e){return this.finished.finally(t).then((()=>{}))}}class kn extends yn{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const Pn=new WeakMap,Cn=(t,e="")=>`${t}:${e}`;function Fn(t){const e=Pn.get(t)||new Map;return Pn.set(t,e),e}const On=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Rn(t){const e=On.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Bn(t,e,n=1){const[s,i]=Rn(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const t=r.trim();return o(t)?parseFloat(t):t}return J(i)?Bn(i,e,n+1):i}function Dn(t,e){return t?.[e]??t?.default??t}const Ln=new Set(["width","height","top","left","right","bottom",...Ue]),In=t=>e=>e.test(t),Wn=[tt,ft,pt,dt,gt,mt,{test:t=>"auto"===t,parse:t=>t}],Nn=t=>Wn.find(In(t));const jn=new Set(["brightness","contrast","saturate","opacity"]);function Kn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(it)||[];if(!s)return t;const i=n.replace(s,"");let r=jn.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const $n=/\b([a-z-]*)\(.*?\)/gu,zn={...kt,getAnimatableNone:t=>{const e=t.match($n);return e?e.map(Kn).join(" "):t}},Un={...tt,transform:Math.round},Yn={rotate:dt,rotateX:dt,rotateY:dt,rotateZ:dt,scale:nt,scaleX:nt,scaleY:nt,scaleZ:nt,skew:dt,skewX:dt,skewY:dt,distance:ft,translateX:ft,translateY:ft,translateZ:ft,x:ft,y:ft,z:ft,perspective:ft,transformPerspective:ft,opacity:et,originX:yt,originY:yt,originZ:ft},Hn={borderWidth:ft,borderTopWidth:ft,borderRightWidth:ft,borderBottomWidth:ft,borderLeftWidth:ft,borderRadius:ft,radius:ft,borderTopLeftRadius:ft,borderTopRightRadius:ft,borderBottomRightRadius:ft,borderBottomLeftRadius:ft,width:ft,maxWidth:ft,height:ft,maxHeight:ft,top:ft,right:ft,bottom:ft,left:ft,padding:ft,paddingTop:ft,paddingRight:ft,paddingBottom:ft,paddingLeft:ft,margin:ft,marginTop:ft,marginRight:ft,marginBottom:ft,marginLeft:ft,backgroundPositionX:ft,backgroundPositionY:ft,...Yn,zIndex:Un,fillOpacity:et,strokeOpacity:et,numOctaves:Un},Xn={...Hn,color:wt,backgroundColor:wt,outlineColor:wt,fill:wt,stroke:wt,borderColor:wt,borderTopColor:wt,borderRightColor:wt,borderBottomColor:wt,borderLeftColor:wt,filter:zn,WebkitFilter:zn},qn=t=>Xn[t];function Gn(t,e){let n=qn(t);return n!==zn&&(n=kt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Zn=new Set(["auto","none","0"]);class _n extends sn{constructor(t,e,n,s,i){super(t,e,n,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),J(s))){const i=Bn(s,e.current);void 0!==i&&(t[n]=i),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!Ln.has(n)||2!==t.length)return;const[s,i]=t,r=Nn(s),o=Nn(i);if(r!==o)if(He(r)&&He(o))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else Ge[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||a(s)))&&n.push(e);var s;n.length&&function(t,e,n){let s,i=0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!Zn.has(e)&&Vt(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=Gn(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ge[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const i=n.length-1,r=n[i];n[i]=Ge[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}}const Jn=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function Qn(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&Jn.has(e)&&(t[n]=t[n]+"px")}const ts=l((()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0})),es=new Set(["opacity","clipPath","filter","transform"]);function ns(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const ss={current:void 0};class is{constructor(t,e={}){this.version="__VERSION__",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=X.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=X.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new p);const n=this.events[t].add(e);return"change"===t?()=>{n(),K.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return ss.current&&ss.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=X.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return g(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rs(t,e){return new is(t,e)}const os=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class as{constructor(){this.latest={},this.values=new Map}set(t,e,n,s){const i=this.values.get(t);i&&i.onRemove();const r=()=>{this.latest[t]=os(e.get(),Hn[t]),n&&K.render(n)};r();const o=e.on("change",r);s&&e.addDependent(s);const a=()=>{o(),n&&$(n),this.values.delete(t),s&&e.removeDependent(s)};return this.values.set(t,{value:e,onRemove:a}),a}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}const ls={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const us=new WeakMap;function cs(t,e,n,s){let i,r;return Ye.has(n)?(e.get("transform")||e.set("transform",new is("none"),(()=>{t.style.transform=function(t){let e="",n=!0;for(let s=0;s<Ue.length;s++){const i=Ue[s],r=t.latest[i];if(void 0===r)continue;let o=!0;o="number"==typeof r?r===(i.startsWith("scale")?1:0):0===parseFloat(r),o||(n=!1,e+=`${ls[i]||i}(${t.latest[i]}) `)}return n?"none":e.trim()}(e)})),r=e.get("transform")):i=rn(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,s,i,r)}const{schedule:hs,cancel:ds}=j(queueMicrotask,!1),ps={x:!1,y:!1};function fs(){return ps.x||ps.y}function ms(t,e){const n=ns(t),s=new AbortController;return[n,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function gs(t){return!("touch"===t.pointerType||fs())}const ys=(t,e)=>!!e&&(t===e||ys(t,e.parentElement)),vs=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,ws=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const bs=new WeakSet;function Ts(t){return e=>{"Enter"===e.key&&t(e)}}function xs(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Ms(t){return vs(t)&&!fs()}function Vs(t,e){const n=window.getComputedStyle(t);return rn(e)?n.getPropertyValue(e):n[e]}function Ss(t,e){let n;const s=()=>{const{currentTime:s}=e,i=(null===s?0:s.value)/100;n!==i&&t(i),n=i};return K.preUpdate(s,!0),()=>$(s)}function As(){const{value:t}=N;null!==t?(t.frameloop.rate.push(z.delta),t.animations.mainThread.push(q.mainThread),t.animations.waapi.push(q.waapi),t.animations.layout.push(q.layout)):$(As)}function Es(t){return t.reduce(((t,e)=>t+e),0)/t.length}function ks(t,e=Es){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const Ps=t=>Math.round(1e3/t);function Cs(){N.value=null,N.addProjectionMetrics=null}function Fs(){const{value:t}=N;if(!t)throw new Error("Stats are not being measured");Cs(),$(As);const e={frameloop:{setup:ks(t.frameloop.setup),rate:ks(t.frameloop.rate),read:ks(t.frameloop.read),resolveKeyframes:ks(t.frameloop.resolveKeyframes),preUpdate:ks(t.frameloop.preUpdate),update:ks(t.frameloop.update),preRender:ks(t.frameloop.preRender),render:ks(t.frameloop.render),postRender:ks(t.frameloop.postRender)},animations:{mainThread:ks(t.animations.mainThread),waapi:ks(t.animations.waapi),layout:ks(t.animations.layout)},layoutProjection:{nodes:ks(t.layoutProjection.nodes),calculatedTargetDeltas:ks(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:ks(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=Ps(n.min),n.max=Ps(n.max),n.avg=Ps(n.avg),[n.min,n.max]=[n.max,n.min],e}function Os(...t){const e=!Array.isArray(t[0]),n=e?0:-1,s=t[0+n],i=we(t[1+n],t[2+n],t[3+n]);return e?i(s):i}function Rs(t){const e=[];ss.current=e;const n=t();ss.current=void 0;const s=rs(n);return function(t,e,n){const s=()=>e.set(n()),i=()=>K.preRender(s,!1,!0),r=t.map((t=>t.on("change",i)));e.on("destroy",(()=>{r.forEach((t=>t())),$(s)}))}(e,s,t),s}const Bs=[...Wn,wt,kt],Ds=t=>Bs.find(In(t));function Ls(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let Is={},Ws=null;const Ns=(t,e)=>{Is[t]=e},js=()=>{Ws||(Ws=document.createElement("style"),Ws.id="motion-view");let t="";for(const e in Is){const n=Is[e];t+=`${e} {\n`;for(const[e,s]of Object.entries(n))t+=`  ${e}: ${s};\n`;t+="}\n"}Ws.textContent=t,document.head.appendChild(Ws),Is={}},Ks=()=>{Ws&&Ws.parentElement&&Ws.parentElement.removeChild(Ws)};function $s(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function zs(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}const Us=["layout","enter","exit","new","old"];function Ys(t){const{update:e,targets:n,options:s}=t;if(!document.startViewTransition)return new Promise((async t=>{await e(),t(new An([]))}));(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",n)||Ns(":root",{"view-transition-name":"none"}),Ns("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),js();const i=document.startViewTransition((async()=>{await e()}));return i.finished.finally((()=>{Ks()})),new Promise((t=>{i.ready.then((()=>{const e=document.getAnimations().filter(zs),i=[];n.forEach(((t,e)=>{for(const n of Us){if(!t[n])continue;const{keyframes:r,options:o}=t[n];for(let[t,a]of Object.entries(r)){if(!a)continue;const r={...Dn(s,t),...Dn(o,t)},l=Ls(n);if("opacity"===t&&!Array.isArray(a)){a=["new"===l?0:1,a]}"function"==typeof r.delay&&(r.delay=r.delay(0,1)),r.duration&&(r.duration=f(r.duration)),r.delay&&(r.delay=f(r.delay));const u=new yn({...r,element:document.documentElement,name:t,pseudoElement:`::view-transition-${l}(${e})`,keyframes:a});i.push(u)}}}));for(const t of e){if("finished"===t.playState)continue;const{effect:e}=t;if(!(e&&e instanceof KeyframeEffect))continue;const{pseudoElement:r}=e;if(!r)continue;const o=$s(r);if(!o)continue;const a=n.get(o.layer);if(a)Hs(a,"enter")&&Hs(a,"exit")&&e.getKeyframes().some((t=>t.mixBlendMode))?i.push(new kn(t)):t.cancel();else{const n="group"===o.type?"layout":"";let r={...Dn(s,n)};r.duration&&(r.duration=f(r.duration)),r=gn(r);const a=pn(r.ease,r.duration);e.updateTiming({delay:f(r.delay??0),duration:r.duration,easing:a}),i.push(new kn(t))}}t(new An(i))}))}))}function Hs(t,e){return t?.[e]?.keyframes.opacity}let Xs=[],qs=null;function Gs(){qs=null;const[t]=Xs;var e;t&&(n(Xs,e=t),qs=e,Ys(e).then((t=>{e.notifyReady(t),t.finished.finally(Gs)})))}function Zs(){for(let t=Xs.length-1;t>=0;t--){const e=Xs[t],{interrupt:n}=e.options;if("immediate"===n){const n=Xs.slice(0,t+1).map((t=>t.update)),s=Xs.slice(t+1);e.update=()=>{n.forEach((t=>t()))},Xs=[e,...s];break}}qs&&"immediate"!==Xs[0]?.options.interrupt||Gs()}class _s{constructor(t,e={}){var n;this.currentTarget="root",this.targets=new Map,this.notifyReady=u,this.readyPromise=new Promise((t=>{this.notifyReady=t})),this.update=t,this.options={interrupt:"wait",...e},n=this,Xs.push(n),hs.render(Zs)}get(t){return this.currentTarget=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentTarget:s,targets:i}=this;i.has(s)||i.set(s,{});i.get(s)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const Js=K,Qs=W.reduce(((t,e)=>(t[e]=t=>$(t),t)),{}),ti=t=>Boolean(t&&t.getVelocity);function ei(t){return"object"==typeof t&&!Array.isArray(t)}function ni(t,e,n,s){return"string"==typeof t&&ei(e)?ns(t,n,s):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function si(t,e,n){return t*(e+1)}function ii(t,e,n,s){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:s.get(e)??t}function ri(t,e,s,i,r,o){!function(t,e,s){for(let i=0;i<t.length;i++){const r=t[i];r.at>e&&r.at<s&&(n(t,r),i--)}}(t,r,o);for(let n=0;n<e.length;n++)t.push({value:e[n],at:Ot(r,o,i[n]),easing:B(s,n)})}function oi(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function ai(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function li(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function ui(t,e){return e[t]||(e[t]=[]),e[t]}function ci(t){return Array.isArray(t)?t:[t]}function hi(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const di=t=>"number"==typeof t,pi=t=>t.every(di),fi=new WeakMap;function mi(t){const e=[{},{}];return t?.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function gi(t,e,n,s){if("function"==typeof e){const[i,r]=mi(s);e=e(void 0!==n?n:t.custom,i,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[i,r]=mi(s);e=e(void 0!==n?n:t.custom,i,r)}return e}function yi(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,rs(n))}function vi(t){return(t=>Array.isArray(t))(t)?t[t.length-1]||0:t}function wi(t,e){const n=function(t,e,n){const s=t.getProps();return gi(s,e,void 0!==n?n:s.custom,t)}(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const e in r){yi(t,e,vi(r[e]))}}function bi(t,e){const n=t.getValue("willChange");if(s=n,Boolean(ti(s)&&s.add))return n.add(e);if(!n&&r.WillChange){const n=new r.WillChange("auto");t.addValue("willChange",n),n.add(e)}var s}const Ti=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),xi="data-"+Ti("framerAppearId");function Mi(t){return t.props[xi]}const Vi=t=>null!==t;const Si={type:"spring",stiffness:500,damping:25,restSpeed:10},Ai={type:"keyframes",duration:.8},Ei={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ki=(t,{keyframes:e})=>e.length>2?Ai:Ye.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Si:Ei;const Pi=(t,e,n,s={},i,o)=>a=>{const l=Dn(s,t)||{},u=l.delay||s.delay||0;let{elapsed:c=0}=s;c-=f(u);const h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-c,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:o?void 0:i};(function({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(l)||Object.assign(h,ki(t,h)),h.duration&&(h.duration=f(h.duration)),h.repeatDelay&&(h.repeatDelay=f(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(d=!0)),(r.instantAnimations||r.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),h.allowFlatten=!l.type&&!l.ease,d&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(Vi),r=e&&"loop"!==n&&e%2==1?0:i.length-1;return r&&void 0!==s?s:i[r]}(h.keyframes,l);if(void 0!==t)return void K.update((()=>{h.onUpdate(t),h.onComplete()}))}return l.isSync?new Fe(h):new Sn(h)};function Ci({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,s}function Fi(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(r=s);const l=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const e in a){const s=t.getValue(e,t.latestValues[e]??null),i=a[e];if(void 0===i||u&&Ci(u,e))continue;const o={delay:n,...Dn(r||{},e)},c=s.get();if(void 0!==c&&!s.isAnimating&&!Array.isArray(i)&&i===c&&!o.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const n=Mi(t);if(n){const t=window.MotionHandoffAnimation(n,e,K);null!==t&&(o.startTime=t,h=!0)}}bi(t,e),s.start(Pi(e,s,i,t.shouldReduceMotion&&Ln.has(e)?{type:!1}:o,t,h));const d=s.animation;d&&l.push(d)}return o&&Promise.all(l).then((()=>{K.update((()=>{o&&wi(t,o)}))})),l}const Oi={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Ri={};for(const t in Oi)Ri[t]={isEnabled:e=>Oi[t].some((t=>!!e[t]))};const Bi=()=>({x:{min:0,max:0},y:{min:0,max:0}}),Di="undefined"!=typeof window,Li={current:null},Ii={current:!1};const Wi=["initial","animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"];function Ni(t){return null!==(e=t.animate)&&"object"==typeof e&&"function"==typeof e.start||Wi.some((e=>function(t){return"string"==typeof t||Array.isArray(t)}(t[e])));var e}const ji=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Ki{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:i,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=sn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=X.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,K.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=Boolean(i),this.isControllingVariants=Ni(e),this.isVariantNode=function(t){return Boolean(Ni(t)||t.variants)}(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in c){const e=c[t];void 0!==a[t]&&ti(e)&&e.set(a[t],!1)}}mount(t){this.current=t,fi.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),Ii.current||function(){if(Ii.current=!0,Di)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Li.current=t.matches;t.addListener(e),e()}else Li.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Li.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),$(this.notifyUpdate),$(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Ye.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&K.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),i=e.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{s(),i(),r&&r(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in Ri){const e=Ri[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<ji.length;e++){const n=ji[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const s=t["on"+n];s&&(this.propEventSubscriptions[n]=this.on(n,s))}this.prevMotionValues=function(t,e,n){for(const s in e){const i=e[s],r=n[s];if(ti(i))t.addValue(s,i);else if(ti(r))t.addValue(s,rs(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{const e=t.getStaticValue(s);t.addValue(s,rs(void 0!==e?e:i,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=rs(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(o(n)||a(n))?n=parseFloat(n):!Ds(n)&&kt.test(e)&&(n=Gn(t,e)),this.setBaseTarget(t,ti(n)?n.get():n)),ti(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const s=gi(this.props,e,this.presenceContext?.custom);s&&(n=s[t])}if(e&&void 0!==n)return n;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||ti(s)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new p),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class $i extends Ki{constructor(){super(...arguments),this.KeyframeResolver=_n}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ti(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}const zi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ui=Ue.length;function Yi(t,e,n){const{style:s,vars:i,transformOrigin:r}=t;let o=!1,a=!1;for(const t in e){const n=e[t];if(Ye.has(t))o=!0;else if(Z(t))i[t]=n;else{const e=os(n,Hn[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(e.transform||(o||n?s.transform=function(t,e,n){let s="",i=!0;for(let r=0;r<Ui;r++){const o=Ue[r],a=t[o];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=os(a,Hn[o]);l||(i=!1,s+=`${zi[o]||o}(${t}) `),n&&(e[o]=t)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=r;s.transformOrigin=`${t} ${e} ${n}`}}function Hi(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const e in n)t.style.setProperty(e,n[e])}const Xi={};function qi(t,{layout:e,layoutId:n}){return Ye.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Xi[t]||"opacity"===t)}function Gi(t,e,n){const{style:s}=t,i={};for(const r in s)(ti(s[r])||e.style&&ti(e.style[r])||qi(r,t)||void 0!==n?.getValue(r)?.liveStyle)&&(i[r]=s[r]);return i}class Zi extends $i{constructor(){super(...arguments),this.type="html",this.renderInstance=Hi}readValueFromInstance(t,e){if(Ye.has(e))return $e(t,e);{const s=(n=t,window.getComputedStyle(n)),i=(Z(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof i?i.trim():i}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return function(t,e){return function({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}(t,e)}build(t,e,n){Yi(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Gi(t,e,n)}}class _i extends Ki{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}const Ji={offset:"stroke-dashoffset",array:"stroke-dasharray"},Qi={offset:"strokeDashoffset",array:"strokeDasharray"};function tr(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:r=1,pathOffset:o=0,...a},l,u,c){if(Yi(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==n&&(h.y=n),void 0!==s&&(h.scale=s),void 0!==i&&function(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?Ji:Qi;t[r.offset]=ft.transform(-s);const o=ft.transform(e),a=ft.transform(n);t[r.array]=`${o} ${a}`}(h,i,r,o,!1)}const er=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nr extends $i{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Bi}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Ye.has(e)){const t=qn(e);return t&&t.default||0}return e=er.has(e)?e:Ti(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return function(t,e,n){const s=Gi(t,e,n);for(const n in t)(ti(t[n])||ti(e[n]))&&(s[-1!==Ue.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return s}(t,e,n)}build(t,e,n){tr(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,s){!function(t,e,n,s){Hi(t,e,void 0,s);for(const n in e.attrs)t.setAttribute(er.has(n)?n:Ti(n),e.attrs[n])}(t,e,0,s)}mount(t){var e;this.isSVGTag="string"==typeof(e=t.tagName)&&"svg"===e.toLowerCase(),super.mount(t)}}function sr(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=function(t){return t instanceof SVGElement&&"svg"!==t.tagName}(t)?new nr(e):new Zi(e);n.mount(t),fi.set(t,n)}function ir(t){const e=new _i({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),fi.set(t,e)}function rr(t,e,n,s){const i=[];if(function(t,e){return ti(t)||"number"==typeof t||"string"==typeof t&&!ei(e)}(t,e))i.push(function(t,e,n){const s=ti(t)?t:rs(t);return s.start(Pi("",s,e,n)),s.animation}(t,ei(e)&&e.default||e,n&&n.default||n));else{const r=ni(t,e,s),o=r.length;for(let t=0;t<o;t++){const s=r[t],a=s instanceof Element?sr:ir;fi.has(s)||a(s);const l=fi.get(s),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,o)),i.push(...Fi(l,{...e,transition:u},{}))}}return i}function or(t,e,n){const s=[],i=function(t,{defaultTransition:e={},...n}={},s,i){const r=e.duration||.3,o=new Map,a=new Map,l={},u=new Map;let c=0,h=0,p=0;for(let n=0;n<t.length;n++){const o=t[n];if("string"==typeof o){u.set(o,h);continue}if(!Array.isArray(o)){u.set(o.name,ii(h,o.at,c,u));continue}let[d,m,g={}]=o;void 0!==g.at&&(h=ii(h,g.at,c,u));let y=0;const v=(t,n,s,o=0,a=0)=>{const l=ci(t),{delay:u=0,times:c=Te(l),type:d="keyframes",repeat:m,repeatType:g,repeatDelay:v=0,...w}=n;let{ease:b=e.ease||"easeOut",duration:T}=n;const x="function"==typeof u?u(o,a):u,M=l.length,V=mn(d)?d:i?.[d];if(M<=2&&V){let t=100;if(2===M&&pi(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...w};void 0!==T&&(e.duration=f(T));const n=Gt(e,t,V);b=n.ease,T=n.duration}T??(T=r);const S=h+x;1===c.length&&0===c[0]&&(c[1]=1);const A=c.length-l.length;if(A>0&&be(c,A),1===l.length&&l.unshift(null),m){T=si(T,m);const t=[...l],e=[...c];b=Array.isArray(b)?[...b]:[b];const n=[...b];for(let s=0;s<m;s++){l.push(...t);for(let i=0;i<t.length;i++)c.push(e[i]+(s+1)),b.push(0===i?"linear":B(n,i-1))}oi(c,m)}const E=S+T;ri(s,l,b,c,S,E),y=Math.max(x+T,y),p=Math.max(E,p)};if(ti(d))v(m,g,ui("default",li(d,a)));else{const t=ni(d,m,s,l),e=t.length;for(let n=0;n<e;n++){const s=li(t[n],a);for(const t in m)v(m[t],hi(g,t),ui(t,s),n,e)}}c=h,h+=y}return a.forEach(((t,s)=>{for(const i in t){const r=t[i];r.sort(ai);const a=[],l=[],u=[];for(let t=0;t<r.length;t++){const{at:e,value:n,easing:s}=r[t];a.push(n),l.push(d(0,p,e)),u.push(s||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),o.has(s)||o.set(s,{keyframes:{},transition:{}});const c=o.get(s);c.keyframes[i]=a,c.transition[i]={...e,duration:p,ease:u,times:l,...n}}})),o}(t,e,n,{spring:ye});return i.forEach((({keyframes:t,transition:e},n)=>{s.push(...rr(n,t,e))})),s}function ar(t){return function(e,n,s){let i=[];var r;r=e,i=Array.isArray(r)&&r.some(Array.isArray)?or(e,n,t):rr(e,n,s,t);const o=new En(i);return t&&t.animations.push(o),o}}const lr=ar();const ur=t=>function(e,n,s){return new En(function(t,e,n,s){const i=ns(t,s),r=i.length,o=[];for(let t=0;t<r;t++){const s=i[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,r));for(const t in e){let n=e[t];Array.isArray(n)||(n=[n]);const i={...Dn(a,t)};i.duration&&(i.duration=f(i.duration)),i.delay&&(i.delay=f(i.delay));const r=Fn(s),l=Cn(t,i.pseudoElement||""),u=r.get(l);u&&u.stop(),o.push({map:r,key:l,unresolvedKeyframes:n,options:{...i,element:s,name:t,allowFlatten:!a.type&&!a.ease}})}}for(let t=0;t<o.length;t++){const{unresolvedKeyframes:e,options:n}=o[t],{element:s,name:i,pseudoElement:r}=n;r||null!==e[0]||(e[0]=Vs(s,i)),Oe(e),Qn(e,i),!r&&e.length<2&&e.unshift(Vs(s,i)),n.keyframes=e}const a=[];for(let t=0;t<o.length;t++){const{map:e,key:n,options:s}=o[t],i=new yn(s);e.set(n,i),i.finished.finally((()=>e.delete(n))),a.push(i)}return a}(e,n,s,t))},cr=ur(),hr=new WeakMap;let dr;function pr({target:t,contentRect:e,borderBoxSize:n}){hr.get(t)?.forEach((s=>{s({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function fr(t){t.forEach(pr)}function mr(t,e){dr||"undefined"!=typeof ResizeObserver&&(dr=new ResizeObserver(fr));const n=ns(t);return n.forEach((t=>{let n=hr.get(t);n||(n=new Set,hr.set(t,n)),n.add(e),dr?.observe(t)})),()=>{n.forEach((t=>{const n=hr.get(t);n?.delete(e),n?.size||dr?.unobserve(t)}))}}const gr=new Set;let yr;function vr(t){return gr.add(t),yr||(yr=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};gr.forEach((t=>t(e)))},window.addEventListener("resize",yr)),()=>{gr.delete(t),!gr.size&&yr&&(yr=void 0)}}const wr={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function br(t,e,n,s){const i=n[e],{length:r,position:o}=wr[e],a=i.current,l=n.time;i.current=t[`scroll${o}`],i.scrollLength=t[`scroll${r}`]-t[`client${r}`],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=d(0,i.scrollLength,i.current);const u=s-l;i.velocity=u>50?0:g(i.current-a,u)}const Tr={start:0,center:.5,end:1};function xr(t,e,n=0){let s=0;if(t in Tr&&(t=Tr[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?s=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?s=e/100*document.documentElement.clientWidth:t.endsWith("vh")?s=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(s=e*t),n+s}const Mr=[0,0];function Vr(t,e,n,s){let i=Array.isArray(t)?t:Mr,r=0,o=0;return"number"==typeof t?i=[t,t]:"string"==typeof t&&(i=(t=t.trim()).includes(" ")?t.split(" "):[t,Tr[t]?t:"0"]),r=xr(i[0],n,s),o=xr(i[1],e),r-o}const Sr={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Ar={x:0,y:0};function Er(t,e,n){const{offset:i=Sr.All}=n,{target:r=t,axis:o="y"}=n,a="y"===o?"height":"width",l=r!==t?function(t,e){const n={x:0,y:0};let s=t;for(;s&&s!==e;)if(s instanceof HTMLElement)n.x+=s.offsetLeft,n.y+=s.offsetTop,s=s.offsetParent;else if("svg"===s.tagName){const t=s.getBoundingClientRect();s=s.parentElement;const e=s.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(s instanceof SVGGraphicsElement))break;{const{x:t,y:e}=s.getBBox();n.x+=t,n.y+=e;let i=null,r=s.parentNode;for(;!i;)"svg"===r.tagName&&(i=r),r=s.parentNode;s=i}}return n}(r,t):Ar,u=r===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(r),c={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let h=!e[o].interpolate;const d=i.length;for(let t=0;t<d;t++){const n=Vr(i[t],c[a],u[a],l[o]);h||n===e[o].interpolatorOffsets[t]||(h=!0),e[o].offset[t]=n}h&&(e[o].interpolate=we(e[o].offset,Te(i),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=s(0,1,e[o].interpolate(e[o].current))}function kr(t,e,n,s={}){return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let s=e;for(;s&&s!==t;)n.x.targetOffset+=s.offsetLeft,n.y.targetOffset+=s.offsetTop,s=s.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,s.target,n),update:e=>{!function(t,e,n){br(t,"x",e,n),br(t,"y",e,n),e.time=n}(t,n,e),(s.offset||s.target)&&Er(t,n,s)},notify:()=>e(n)}}const Pr=new WeakMap,Cr=new WeakMap,Fr=new WeakMap,Or=t=>t===document.documentElement?window:t;function Rr(t,{container:e=document.documentElement,...n}={}){let s=Fr.get(e);s||(s=new Set,Fr.set(e,s));const i=kr(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(s.add(i),!Pr.has(e)){const t=()=>{for(const t of s)t.measure()},n=()=>{for(const t of s)t.update(z.timestamp)},i=()=>{for(const t of s)t.notify()},a=()=>{K.read(t),K.read(n),K.preUpdate(i)};Pr.set(e,a);const l=Or(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&Cr.set(e,(o=a,"function"==typeof(r=e)?vr(r):mr(r,o))),l.addEventListener("scroll",a,{passive:!0}),a()}var r,o;const a=Pr.get(e);return K.read(a,!1,!0),()=>{$(a);const t=Fr.get(e);if(!t)return;if(t.delete(i),t.size)return;const n=Pr.get(e);Pr.delete(e),n&&(Or(e).removeEventListener("scroll",n),Cr.get(e)?.(),window.removeEventListener("resize",n))}}const Br=new Map;function Dr({source:t,container:e,...n}){const{axis:s}=n;t&&(e=t);const i=Br.get(e)??new Map;Br.set(e,i);const r=n.target??"self",o=i.get(r)??{},a=s+(n.offset??[]).join(",");return o[a]||(o[a]=!n.target&&an()?new ScrollTimeline({source:e,axis:s}):function(t){const e={value:0},n=Rr((n=>{e.value=100*n[t.axis].progress}),t);return{currentTime:e,cancel:n}}({container:e,...n})),o[a]}const Lr={some:0,all:1};const Ir=(t,e)=>Math.abs(t-e);t.AsyncMotionValueAnimation=Sn,t.DOMKeyframesResolver=_n,t.GroupAnimation=An,t.GroupAnimationWithThen=En,t.JSAnimation=Fe,t.KeyframeResolver=sn,t.MotionGlobalConfig=r,t.MotionValue=is,t.NativeAnimation=yn,t.NativeAnimationExtended=bn,t.NativeAnimationWrapper=kn,t.SubscriptionManager=p,t.ViewTransitionBuilder=_s,t.acceleratedValues=es,t.activeAnimations=q,t.addUniqueItem=e,t.alpha=et,t.analyseComplexValue=Vt,t.animate=lr,t.animateMini=cr,t.animateValue=function(t){return new Fe(t)},t.animateView=function(t,e={}){return new _s(t,e)},t.animationMapKey=Cn,t.anticipate=A,t.applyPxDefaults=Qn,t.backIn=V,t.backInOut=S,t.backOut=M,t.calcGeneratorDuration=qt,t.cancelFrame=$,t.cancelMicrotask=ds,t.cancelSync=Qs,t.circIn=E,t.circInOut=P,t.circOut=k,t.clamp=s,t.collectMotionValues=ss,t.color=wt,t.complex=kt,t.convertOffsetToTimes=xe,t.createGeneratorEasing=Gt,t.createRenderBatcher=j,t.createScopedAnimate=ar,t.cubicBezier=b,t.cubicBezierAsString=hn,t.defaultEasing=Me,t.defaultOffset=Te,t.defaultValueTypes=Xn,t.degrees=dt,t.delay=function(t,e){return function(t,e){const n=X.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&($(s),t(r-e))};return K.setup(s,!0),()=>$(s)}(t,f(e))},t.dimensionValueTypes=Wn,t.distance=Ir,t.distance2D=function(t,e){const n=Ir(t.x,e.x),s=Ir(t.y,e.y);return Math.sqrt(n**2+s**2)},t.easeIn=C,t.easeInOut=O,t.easeOut=F,t.easingDefinitionToFunction=I,t.fillOffset=be,t.fillWildcards=Oe,t.findDimensionValueType=Nn,t.findValueType=Ds,t.flushKeyframeResolvers=nn,t.frame=K,t.frameData=z,t.frameSteps=U,t.generateLinearEasing=Ht,t.getAnimatableNone=Gn,t.getAnimationMap=Fn,t.getComputedStyle=Vs,t.getDefaultValueType=qn,t.getEasingForSegment=B,t.getMixer=jt,t.getValueAsType=os,t.getValueTransition=Dn,t.getVariableValue=Bn,t.hasWarned=function(t){return y.has(t)},t.hex=ct,t.hover=function(t,e,n={}){const[s,i,r]=ms(t,n),o=t=>{if(!gs(t))return;const{target:n}=t,s=e(n,t);if("function"!=typeof s||!n)return;const r=t=>{gs(t)&&(s(t),n.removeEventListener("pointerleave",r))};n.addEventListener("pointerleave",r,i)};return s.forEach((t=>{t.addEventListener("pointerenter",o,i)})),r},t.hsla=vt,t.hslaToRgba=Ct,t.inView=function(t,e,{root:n,margin:s,amount:i="some"}={}){const r=ns(t),o=new WeakMap,a=new IntersectionObserver((t=>{t.forEach((t=>{const n=o.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?o.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),o.delete(t.target))}))}),{root:n,rootMargin:s,threshold:"number"==typeof i?i:Lr[i]});return r.forEach((t=>a.observe(t))),()=>a.disconnect()},t.inertia=ve,t.interpolate=we,t.invariant=i,t.invisibleValues=It,t.isBezierDefinition=D,t.isCSSVariableName=Z,t.isCSSVariableToken=J,t.isDragActive=fs,t.isDragging=ps,t.isEasingArray=R,t.isGenerator=mn,t.isNodeOrChild=ys,t.isNumericalString=o,t.isPrimaryPointer=vs,t.isWaapiSupportedEasing=function t(e){return Boolean("function"==typeof e&&cn()||!e||"string"==typeof e&&(e in dn||cn())||D(e)||Array.isArray(e)&&e.every(t))},t.isZeroValueString=a,t.keyframes=Ve,t.mapEasingToNativeEasing=pn,t.mapValue=function(t,e,n,s){const i=Os(e,n,s);return Rs((()=>i(t.get())))},t.maxGeneratorDuration=Xt,t.memo=l,t.microtask=hs,t.millisecondsToSeconds=m,t.mirrorEasing=T,t.mix=Ut,t.mixArray=Kt,t.mixColor=Lt,t.mixComplex=zt,t.mixImmediate=Ft,t.mixLinearColor=Rt,t.mixNumber=Ot,t.mixObject=$t,t.mixVisibility=Wt,t.motionValue=rs,t.moveItem=function([...t],e,n){const s=e<0?t.length+e:e;if(s>=0&&s<t.length){const s=n<0?t.length+n:n,[i]=t.splice(e,1);t.splice(s,0,i)}return t},t.noop=u,t.number=tt,t.numberValueTypes=Hn,t.observeTimeline=Ss,t.parseCSSVariable=Rn,t.parseValueFromTransform=Ke,t.percent=pt,t.pipe=h,t.positionalKeys=Ln,t.press=function(t,e,n={}){const[s,i,r]=ms(t,n),o=t=>{const s=t.currentTarget;if(!Ms(t)||bs.has(s))return;bs.add(s);const r=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),bs.has(s)&&bs.delete(s),Ms(t)&&"function"==typeof r&&r(t,{success:e})},a=t=>{o(t,s===window||s===document||n.useGlobalTarget||ys(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return s.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",o,i),t instanceof HTMLElement&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const s=Ts((()=>{if(bs.has(n))return;xs(n,"down");const t=Ts((()=>{xs(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>xs(n,"cancel")),e)}));n.addEventListener("keydown",s,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",s)),e)})(t,i))),e=t,ws.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),r},t.progress=d,t.progressPercentage=yt,t.px=ft,t.readTransformValue=$e,t.recordStats=function(){if(N.value)throw Cs(),new Error("Stats are already being measured");const t=N;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},K.postRender(As,!0),Fs},t.removeItem=n,t.resolveElements=ns,t.reverseEasing=x,t.rgbUnit=lt,t.rgba=ut,t.scale=nt,t.scroll=function(t,{axis:e="y",container:n=document.documentElement,...s}={}){n===document.documentElement&&("y"===e&&n.scrollHeight===n.clientHeight||"x"===e&&n.scrollWidth===n.clientWidth)&&(n=document.body);const i={axis:e,container:n,...s};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)?Rr((n=>{t(n[e.axis].progress,n)}),e):Ss(t,Dr(e))}(t,i):function(t,e){const n=Dr(e);return t.attachTimeline({timeline:e.target?void 0:n,observe:t=>(t.pause(),Ss((e=>{t.time=t.duration*e}),n))})}(t,i)},t.scrollInfo=Rr,t.secondsToMilliseconds=f,t.setDragLock=function(t){return"x"===t||"y"===t?ps[t]?null:(ps[t]=!0,()=>{ps[t]=!1}):ps.x||ps.y?null:(ps.x=ps.y=!0,()=>{ps.x=ps.y=!1})},t.setStyle=on,t.spring=ye,t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:s}={}){return(i,r)=>{const o="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,r),a=Math.abs(o-i);let l=t*a;if(s){const e=r*t;l=I(s)(l/e)*e}return e+l}},t.startWaapiAnimation=fn,t.statsBuffer=N,t.steps=function(t,e="end"){return n=>{const i=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,r="end"===e?Math.floor(i):Math.ceil(i);return s(0,1,r/t)}},t.styleEffect=function(t,e){const n=ns(t),s=[];for(let t=0;t<n.length;t++){const i=n[t],r=us.get(i)??new as;us.set(i,r);for(const t in e){const n=cs(i,r,t,e[t]);s.push(n)}}return()=>{for(const t of s)t()}},t.supportedWaapiEasing=dn,t.supportsBrowserAnimation=Vn,t.supportsFlags=ln,t.supportsLinearEasing=cn,t.supportsPartialKeyframes=ts,t.supportsScrollTimeline=an,t.sync=Js,t.testValueType=In,t.time=X,t.transform=Os,t.transformPropOrder=Ue,t.transformProps=Ye,t.transformValue=Rs,t.transformValueTypes=Yn,t.velocityPerSecond=g,t.vh=mt,t.vw=gt,t.warnOnce=function(t,e,n){t||y.has(e)||(console.warn(e),n&&console.warn(n),y.add(e))},t.warning=()=>{},t.wrap=v}));
